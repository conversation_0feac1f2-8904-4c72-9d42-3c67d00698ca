
#ifndef _MMSYSTEM_H
#define _MMSYSTEM_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif
#pragma pack(push,1)
#ifdef __cplusplus
extern "C" {
#endif
#define	WINMMAPI	DECLSPEC_IMPORT
#define _loadds
#define _huge
#define MAXPNAMELEN 32
#define MAXERRORLENGTH 256
#define MAX_JOYSTICKOEMVXDNAME 260
#define _MMRESULT_
#define TIME_MS 1
#define TIME_SAMPLES 2
#define TIME_BYTES 4
#define TIME_SMPTE 8
#define TIME_MIDI 16
#define TIME_TICKS 32
#define MAKEFOURCC(c0,c1,c2,c3) ((DWORD)(BYTE)(c0)|((DWORD)(BYTE)(c1)<<8)|((DWORD)(BYTE)(c2)<<16)|((DWORD)(BYTE)(c3)<<24))
#ifndef mmioFOURCC
#define mmioFOURCC(c0,c1,c2,c3) MAKEFOURCC(c0,c1,c2,c3)
#endif
#define MM_JOY1MOVE 0x3A0
#define MM_JOY2MOVE 0x3A1
#define MM_JOY1ZMOVE 0x3A2
#define MM_JOY2ZMOVE 0x3A3
#define MM_JOY1BUTTONDOWN 0x3B5
#define MM_JOY2BUTTONDOWN 0x3B6
#define MM_JOY1BUTTONUP 0x3B7
#define MM_JOY2BUTTONUP 0x3B8
#define MM_MCINOTIFY 0x3B9
#define MM_WOM_OPEN 0x3BB
#define MM_WOM_CLOSE 0x3BC
#define MM_WOM_DONE 0x3BD
#define MM_WIM_OPEN 0x3BE
#define MM_WIM_CLOSE 0x3BF
#define MM_WIM_DATA 0x3C0
#define MM_MIM_OPEN 0x3C1
#define MM_MIM_CLOSE 0x3C2
#define MM_MIM_DATA 0x3C3
#define MM_MIM_LONGDATA 0x3C4
#define MM_MIM_ERROR 0x3C5
#define MM_MIM_LONGERROR 0x3C6
#define MM_MOM_OPEN 0x3C7
#define MM_MOM_CLOSE 0x3C8
#define MM_MOM_DONE 0x3C9
#define MM_DRVM_OPEN 0x3D0
#define MM_DRVM_CLOSE 0x3D1
#define MM_DRVM_DATA 0x3D2
#define MM_DRVM_ERROR 0x3D3
#define MM_STREAM_OPEN	0x3D4
#define MM_STREAM_CLOSE	0x3D5
#define MM_STREAM_DONE	0x3D6
#define MM_STREAM_ERROR	0x3D7
#define MM_MOM_POSITIONCB 0x3CA
#define MM_MCISIGNAL 0x3CB
#define MM_MIM_MOREDATA 0x3CC
#define MM_MIXM_LINE_CHANGE 0x3D0
#define MM_MIXM_CONTROL_CHANGE 0x3D1
#define MMSYSERR_BASE 0
#define WAVERR_BASE 32
#define MIDIERR_BASE 64
#define TIMERR_BASE 96
#define JOYERR_BASE 160
#define MCIERR_BASE 256
#define MIXERR_BASE 1024
#define MCI_STRING_OFFSET 512
#define MCI_VD_OFFSET 1024
#define MCI_CD_OFFSET 1088
#define MCI_WAVE_OFFSET 1152
#define MCI_SEQ_OFFSET 1216
#define MMSYSERR_NOERROR 0
#define MMSYSERR_ERROR (MMSYSERR_BASE+1)
#define MMSYSERR_BADDEVICEID (MMSYSERR_BASE+2)
#define MMSYSERR_NOTENABLED (MMSYSERR_BASE+3)
#define MMSYSERR_ALLOCATED (MMSYSERR_BASE+4)
#define MMSYSERR_INVALHANDLE (MMSYSERR_BASE+5)
#define MMSYSERR_NODRIVER (MMSYSERR_BASE+6)
#define MMSYSERR_NOMEM (MMSYSERR_BASE+7)
#define MMSYSERR_NOTSUPPORTED (MMSYSERR_BASE+8)
#define MMSYSERR_BADERRNUM (MMSYSERR_BASE+9)
#define MMSYSERR_INVALFLAG (MMSYSERR_BASE+10)
#define MMSYSERR_INVALPARAM (MMSYSERR_BASE+11)
#define MMSYSERR_HANDLEBUSY (MMSYSERR_BASE+12)
#define MMSYSERR_INVALIDALIAS (MMSYSERR_BASE+13)
#define MMSYSERR_BADDB (MMSYSERR_BASE+14)
#define MMSYSERR_KEYNOTFOUND (MMSYSERR_BASE+15)
#define MMSYSERR_READERROR (MMSYSERR_BASE+16)
#define MMSYSERR_WRITEERROR (MMSYSERR_BASE+17)
#define MMSYSERR_DELETEERROR (MMSYSERR_BASE+18)
#define MMSYSERR_VALNOTFOUND (MMSYSERR_BASE+19)
#define MMSYSERR_NODRIVERCB (MMSYSERR_BASE+20)
#define MMSYSERR_LASTERROR (MMSYSERR_BASE+20)
#define DRV_LOAD 1
#define DRV_ENABLE 2
#define DRV_OPEN 3
#define DRV_CLOSE 4
#define DRV_DISABLE 5
#define DRV_FREE 6
#define DRV_CONFIGURE 7
#define DRV_QUERYCONFIGURE 8
#define DRV_INSTALL 9
#define DRV_REMOVE 10
#define DRV_EXITSESSION 11
#define DRV_POWER 15
#define DRV_RESERVED 0x800
#define DRV_USER 0x4000
#define DRVCNF_CANCEL 0
#define DRVCNF_OK 1
#define DRVCNF_RESTART 2
#define DRV_CANCEL DRVCNF_CANCEL
#define DRV_OK DRVCNF_OK
#define DRV_RESTART DRVCNF_RESTART
#define DRV_MCI_FIRST DRV_RESERVED
#define DRV_MCI_LAST (DRV_RESERVED+0xFFF)
#define CALLBACK_TYPEMASK 0x70000
#define CALLBACK_NULL 0
#define CALLBACK_WINDOW 0x10000
#define CALLBACK_TASK 0x20000
#define CALLBACK_FUNCTION 0x30000
#define CALLBACK_THREAD CALLBACK_TASK
#define CALLBACK_EVENT 0x50000
#define SND_SYNC 0
#define SND_ASYNC 1
#define SND_NODEFAULT 2
#define SND_MEMORY 4
#define SND_LOOP 8
#define SND_NOSTOP 16
#define SND_NOWAIT	0x2000
#define SND_ALIAS 0x10000
#define SND_ALIAS_ID	0x110000
#define SND_FILENAME 0x20000
#define SND_RESOURCE 0x40004
#define SND_PURGE 0x40
#define SND_APPLICATION 0x80
#define SND_ALIAS_START	0
#define	sndAlias(c0,c1)	(SND_ALIAS_START+(DWORD)(BYTE)(c0)|((DWORD)(BYTE)(c1)<<8))
#define SND_ALIAS_SYSTEMASTERISK sndAlias('S','*')
#define SND_ALIAS_SYSTEMQUESTION sndAlias('S','?')
#define SND_ALIAS_SYSTEMHAND sndAlias('S','H')
#define SND_ALIAS_SYSTEMEXIT sndAlias('S','E')
#define SND_ALIAS_SYSTEMSTART sndAlias('S','S')
#define SND_ALIAS_SYSTEMWELCOME sndAlias('S','W')
#define SND_ALIAS_SYSTEMEXCLAMATION sndAlias('S','!')
#define SND_ALIAS_SYSTEMDEFAULT sndAlias('S','D')
#define WAVERR_BADFORMAT (WAVERR_BASE + 0)
#define WAVERR_STILLPLAYING (WAVERR_BASE + 1)
#define WAVERR_UNPREPARED (WAVERR_BASE + 2)
#define WAVERR_SYNC (WAVERR_BASE + 3)
#define WAVERR_LASTERROR (WAVERR_BASE + 3)
#define WOM_OPEN MM_WOM_OPEN
#define WOM_CLOSE MM_WOM_CLOSE
#define WOM_DONE MM_WOM_DONE
#define WIM_OPEN MM_WIM_OPEN
#define WIM_CLOSE MM_WIM_CLOSE
#define WIM_DATA MM_WIM_DATA
#define WAVE_MAPPER ((UINT)-1)
#define WAVE_FORMAT_QUERY 1
#define WAVE_ALLOWSYNC 2
#define WAVE_MAPPED 4
#define WAVE_FORMAT_DIRECT 8
#define WAVE_FORMAT_DIRECT_QUERY (WAVE_FORMAT_QUERY|WAVE_FORMAT_DIRECT)
#define WHDR_DONE 1
#define WHDR_PREPARED 2
#define WHDR_BEGINLOOP 4
#define WHDR_ENDLOOP 8
#define WHDR_INQUEUE 16
#define WAVECAPS_PITCH 1
#define WAVECAPS_PLAYBACKRATE 2
#define WAVECAPS_VOLUME 4
#define WAVECAPS_LRVOLUME 8
#define WAVECAPS_SYNC 16
#define WAVECAPS_SAMPLEACCURATE 32
#define WAVECAPS_DIRECTSOUND 64
#define WAVE_INVALIDFORMAT 0
#define WAVE_FORMAT_1M08 1
#define WAVE_FORMAT_1S08 2
#define WAVE_FORMAT_1M16 4
#define WAVE_FORMAT_1S16 8
#define WAVE_FORMAT_2M08 16
#define WAVE_FORMAT_2S08 32
#define WAVE_FORMAT_2M16 64
#define WAVE_FORMAT_2S16 128
#define WAVE_FORMAT_4M08 256
#define WAVE_FORMAT_4S08 512
#define WAVE_FORMAT_4M16 1024
#define WAVE_FORMAT_4S16 2048
#define WAVE_FORMAT_PCM 1
#define MIDIERR_UNPREPARED MIDIERR_BASE
#define MIDIERR_STILLPLAYING (MIDIERR_BASE+1)
#define MIDIERR_NOMAP (MIDIERR_BASE+2)
#define MIDIERR_NOTREADY (MIDIERR_BASE+3)
#define MIDIERR_NODEVICE (MIDIERR_BASE+4)
#define MIDIERR_INVALIDSETUP (MIDIERR_BASE+5)
#define MIDIERR_BADOPENMODE (MIDIERR_BASE+6)
#define MIDIERR_DONT_CONTINUE (MIDIERR_BASE+7)
#define MIDIERR_LASTERROR (MIDIERR_BASE+7)
#define MIDIPATCHSIZE 128
#define MIM_OPEN MM_MIM_OPEN
#define MIM_CLOSE MM_MIM_CLOSE
#define MIM_DATA MM_MIM_DATA
#define MIM_LONGDATA MM_MIM_LONGDATA
#define MIM_ERROR MM_MIM_ERROR
#define MIM_LONGERROR MM_MIM_LONGERROR
#define MOM_OPEN MM_MOM_OPEN
#define MOM_CLOSE MM_MOM_CLOSE
#define MOM_DONE MM_MOM_DONE
#define MIM_MOREDATA MM_MIM_MOREDATA
#define MOM_POSITIONCB MM_MOM_POSITIONCB
#define MIDIMAPPER ((UINT)-1)
#define MIDI_MAPPER ((UINT)-1)
#define MIDI_IO_STATUS 32
#define MIDI_CACHE_ALL 1
#define MIDI_CACHE_BESTFIT 2
#define MIDI_CACHE_QUERY 3
#define MIDI_UNCACHE 4
#define MOD_MIDIPORT 1
#define MOD_SYNTH 2
#define MOD_SQSYNTH 3
#define MOD_FMSYNTH 4
#define MOD_MAPPER 5
#define MIDICAPS_VOLUME 1
#define MIDICAPS_LRVOLUME 2
#define MIDICAPS_CACHE 4
#define MIDICAPS_STREAM 8
#define MHDR_DONE 1
#define MHDR_PREPARED 2
#define MHDR_INQUEUE 4
#define MHDR_ISSTRM 8
#define MEVT_F_SHORT 0
#define MEVT_F_LONG 0x80000000
#define MEVT_F_CALLBACK 0x40000000
#define MEVT_EVENTTYPE(x) ((BYTE)(((x)>>24)&0xFF))
#define MEVT_EVENTPARM(x) ((DWORD)((x)&0xFFFFFFL))
#define MEVT_SHORTMSG 0
#define MEVT_TEMPO 1
#define MEVT_NOP 2
#define MEVT_LONGMSG ((BYTE)0x80)
#define MEVT_COMMENT ((BYTE)0x82)
#define MEVT_VERSION ((BYTE)0x84)
#define MIDISTRM_ERROR (-2)
#define MIDIPROP_SET 0x80000000
#define MIDIPROP_GET 0x40000000
#define MIDIPROP_TIMEDIV 1
#define MIDIPROP_TEMPO 2
#define AUX_MAPPER ((UINT)-1)
#define AUXCAPS_CDAUDIO 1
#define AUXCAPS_AUXIN 2
#define AUXCAPS_VOLUME 1
#define AUXCAPS_LRVOLUME 2
#define MIXER_SHORT_NAME_CHARS 16
#define MIXER_LONG_NAME_CHARS 64
#define MIXERR_INVALLINE MIXERR_BASE
#define MIXERR_INVALCONTROL (MIXERR_BASE+1)
#define MIXERR_INVALVALUE (MIXERR_BASE+2)
#define MIXERR_LASTERROR (MIXERR_BASE+2)
#define MIXER_OBJECTF_HANDLE 0x80000000
#define MIXER_OBJECTF_MIXER 0
#define MIXER_OBJECTF_HMIXER (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIXER)
#define MIXER_OBJECTF_WAVEOUT 0x10000000
#define MIXER_OBJECTF_HWAVEOUT (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_WAVEOUT)
#define MIXER_OBJECTF_WAVEIN 0x20000000
#define MIXER_OBJECTF_HWAVEIN (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_WAVEIN)
#define MIXER_OBJECTF_MIDIOUT 0x30000000
#define MIXER_OBJECTF_HMIDIOUT (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIDIOUT)
#define MIXER_OBJECTF_MIDIIN 0x40000000
#define MIXER_OBJECTF_HMIDIIN (MIXER_OBJECTF_HANDLE|MIXER_OBJECTF_MIDIIN)
#define MIXER_OBJECTF_AUX 0x50000000
#define MIXERLINE_LINEF_ACTIVE 1
#define MIXERLINE_LINEF_DISCONNECTED 0x8000
#define MIXERLINE_LINEF_SOURCE 0x80000000
#define MIXERLINE_COMPONENTTYPE_DST_FIRST 0
#define MIXERLINE_COMPONENTTYPE_DST_UNDEFINED MIXERLINE_COMPONENTTYPE_DST_FIRST
#define MIXERLINE_COMPONENTTYPE_DST_DIGITAL (MIXERLINE_COMPONENTTYPE_DST_FIRST+1)
#define MIXERLINE_COMPONENTTYPE_DST_LINE (MIXERLINE_COMPONENTTYPE_DST_FIRST+2)
#define MIXERLINE_COMPONENTTYPE_DST_MONITOR (MIXERLINE_COMPONENTTYPE_DST_FIRST+3)
#define MIXERLINE_COMPONENTTYPE_DST_SPEAKERS (MIXERLINE_COMPONENTTYPE_DST_FIRST+4)
#define MIXERLINE_COMPONENTTYPE_DST_HEADPHONES (MIXERLINE_COMPONENTTYPE_DST_FIRST+5)
#define MIXERLINE_COMPONENTTYPE_DST_TELEPHONE (MIXERLINE_COMPONENTTYPE_DST_FIRST+6)
#define MIXERLINE_COMPONENTTYPE_DST_WAVEIN (MIXERLINE_COMPONENTTYPE_DST_FIRST+7)
#define MIXERLINE_COMPONENTTYPE_DST_VOICEIN (MIXERLINE_COMPONENTTYPE_DST_FIRST+8)
#define MIXERLINE_COMPONENTTYPE_DST_LAST (MIXERLINE_COMPONENTTYPE_DST_FIRST+8)
#define MIXERLINE_COMPONENTTYPE_SRC_FIRST 0x1000
#define MIXERLINE_COMPONENTTYPE_SRC_UNDEFINED MIXERLINE_COMPONENTTYPE_SRC_FIRST
#define MIXERLINE_COMPONENTTYPE_SRC_DIGITAL (MIXERLINE_COMPONENTTYPE_SRC_FIRST+1)
#define MIXERLINE_COMPONENTTYPE_SRC_LINE (MIXERLINE_COMPONENTTYPE_SRC_FIRST+2)
#define MIXERLINE_COMPONENTTYPE_SRC_MICROPHONE (MIXERLINE_COMPONENTTYPE_SRC_FIRST+3)
#define MIXERLINE_COMPONENTTYPE_SRC_SYNTHESIZER (MIXERLINE_COMPONENTTYPE_SRC_FIRST+4)
#define MIXERLINE_COMPONENTTYPE_SRC_COMPACTDISC (MIXERLINE_COMPONENTTYPE_SRC_FIRST+5)
#define MIXERLINE_COMPONENTTYPE_SRC_TELEPHONE (MIXERLINE_COMPONENTTYPE_SRC_FIRST+6)
#define MIXERLINE_COMPONENTTYPE_SRC_PCSPEAKER (MIXERLINE_COMPONENTTYPE_SRC_FIRST+7)
#define MIXERLINE_COMPONENTTYPE_SRC_WAVEOUT (MIXERLINE_COMPONENTTYPE_SRC_FIRST+8)
#define MIXERLINE_COMPONENTTYPE_SRC_AUXILIARY (MIXERLINE_COMPONENTTYPE_SRC_FIRST+9)
#define MIXERLINE_COMPONENTTYPE_SRC_ANALOG (MIXERLINE_COMPONENTTYPE_SRC_FIRST+10)
#define MIXERLINE_COMPONENTTYPE_SRC_LAST (MIXERLINE_COMPONENTTYPE_SRC_FIRST+10)
#define MIXERLINE_TARGETTYPE_UNDEFINED 0
#define MIXERLINE_TARGETTYPE_WAVEOUT 1
#define MIXERLINE_TARGETTYPE_WAVEIN 2
#define MIXERLINE_TARGETTYPE_MIDIOUT 3
#define MIXERLINE_TARGETTYPE_MIDIIN 4
#define MIXERLINE_TARGETTYPE_AUX 5
#define MIXER_GETLINEINFOF_DESTINATION 0
#define MIXER_GETLINEINFOF_SOURCE 1
#define MIXER_GETLINEINFOF_LINEID 2
#define MIXER_GETLINEINFOF_COMPONENTTYPE 3
#define MIXER_GETLINEINFOF_TARGETTYPE 4
#define MIXER_GETLINEINFOF_QUERYMASK 15
#define MIXERCONTROL_CONTROLF_UNIFORM 1
#define MIXERCONTROL_CONTROLF_MULTIPLE 2
#define MIXERCONTROL_CONTROLF_DISABLED 0x80000000
#define MIXERCONTROL_CT_CLASS_MASK 0xF0000000
#define MIXERCONTROL_CT_CLASS_CUSTOM 0
#define MIXERCONTROL_CT_CLASS_METER 0x10000000
#define MIXERCONTROL_CT_CLASS_SWITCH 0x20000000
#define MIXERCONTROL_CT_CLASS_NUMBER 0x30000000
#define MIXERCONTROL_CT_CLASS_SLIDER 0x40000000
#define MIXERCONTROL_CT_CLASS_FADER 0x50000000
#define MIXERCONTROL_CT_CLASS_TIME 0x60000000
#define MIXERCONTROL_CT_CLASS_LIST 0x70000000
#define MIXERCONTROL_CT_SUBCLASS_MASK 0xF000000
#define MIXERCONTROL_CT_SC_SWITCH_BOOLEAN 0
#define MIXERCONTROL_CT_SC_SWITCH_BUTTON 0x1000000
#define MIXERCONTROL_CT_SC_METER_POLLED 0
#define MIXERCONTROL_CT_SC_TIME_MICROSECS 0
#define MIXERCONTROL_CT_SC_TIME_MILLISECS 0x1000000
#define MIXERCONTROL_CT_SC_LIST_SINGLE 0
#define MIXERCONTROL_CT_SC_LIST_MULTIPLE 0x1000000
#define MIXERCONTROL_CT_UNITS_MASK 0xFF0000
#define MIXERCONTROL_CT_UNITS_CUSTOM 0
#define MIXERCONTROL_CT_UNITS_BOOLEAN 0x10000
#define MIXERCONTROL_CT_UNITS_SIGNED 0x20000
#define MIXERCONTROL_CT_UNITS_UNSIGNED 0x30000
#define MIXERCONTROL_CT_UNITS_DECIBELS 0x40000
#define MIXERCONTROL_CT_UNITS_PERCENT 0x50000
#define MIXERCONTROL_CONTROLTYPE_CUSTOM (MIXERCONTROL_CT_CLASS_CUSTOM|MIXERCONTROL_CT_UNITS_CUSTOM)
#define MIXERCONTROL_CONTROLTYPE_BOOLEANMETER (MIXERCONTROL_CT_CLASS_METER|MIXERCONTROL_CT_SC_METER_POLLED|MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_SIGNEDMETER (MIXERCONTROL_CT_CLASS_METER|MIXERCONTROL_CT_SC_METER_POLLED|MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_PEAKMETER (MIXERCONTROL_CONTROLTYPE_SIGNEDMETER+1)
#define MIXERCONTROL_CONTROLTYPE_UNSIGNEDMETER (MIXERCONTROL_CT_CLASS_METER|MIXERCONTROL_CT_SC_METER_POLLED|MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_BOOLEAN (MIXERCONTROL_CT_CLASS_SWITCH|MIXERCONTROL_CT_SC_SWITCH_BOOLEAN|MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_ONOFF (MIXERCONTROL_CONTROLTYPE_BOOLEAN+1)
#define MIXERCONTROL_CONTROLTYPE_MUTE (MIXERCONTROL_CONTROLTYPE_BOOLEAN+2)
#define MIXERCONTROL_CONTROLTYPE_MONO (MIXERCONTROL_CONTROLTYPE_BOOLEAN+3)
#define MIXERCONTROL_CONTROLTYPE_LOUDNESS (MIXERCONTROL_CONTROLTYPE_BOOLEAN+4)
#define MIXERCONTROL_CONTROLTYPE_STEREOENH (MIXERCONTROL_CONTROLTYPE_BOOLEAN+5)
#define MIXERCONTROL_CONTROLTYPE_BUTTON (MIXERCONTROL_CT_CLASS_SWITCH|MIXERCONTROL_CT_SC_SWITCH_BUTTON|MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_DECIBELS (MIXERCONTROL_CT_CLASS_NUMBER|MIXERCONTROL_CT_UNITS_DECIBELS)
#define MIXERCONTROL_CONTROLTYPE_SIGNED (MIXERCONTROL_CT_CLASS_NUMBER|MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_UNSIGNED (MIXERCONTROL_CT_CLASS_NUMBER|MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_PERCENT (MIXERCONTROL_CT_CLASS_NUMBER|MIXERCONTROL_CT_UNITS_PERCENT)
#define MIXERCONTROL_CONTROLTYPE_SLIDER (MIXERCONTROL_CT_CLASS_SLIDER|MIXERCONTROL_CT_UNITS_SIGNED)
#define MIXERCONTROL_CONTROLTYPE_PAN (MIXERCONTROL_CONTROLTYPE_SLIDER+1)
#define MIXERCONTROL_CONTROLTYPE_QSOUNDPAN (MIXERCONTROL_CONTROLTYPE_SLIDER+2)
#define MIXERCONTROL_CONTROLTYPE_FADER (MIXERCONTROL_CT_CLASS_FADER|MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_VOLUME (MIXERCONTROL_CONTROLTYPE_FADER+1)
#define MIXERCONTROL_CONTROLTYPE_BASS (MIXERCONTROL_CONTROLTYPE_FADER+2)
#define MIXERCONTROL_CONTROLTYPE_TREBLE (MIXERCONTROL_CONTROLTYPE_FADER+3)
#define MIXERCONTROL_CONTROLTYPE_EQUALIZER (MIXERCONTROL_CONTROLTYPE_FADER+4)
#define MIXERCONTROL_CONTROLTYPE_SINGLESELECT (MIXERCONTROL_CT_CLASS_LIST|MIXERCONTROL_CT_SC_LIST_SINGLE|MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_MUX (MIXERCONTROL_CONTROLTYPE_SINGLESELECT+1)
#define MIXERCONTROL_CONTROLTYPE_MULTIPLESELECT (MIXERCONTROL_CT_CLASS_LIST|MIXERCONTROL_CT_SC_LIST_MULTIPLE|MIXERCONTROL_CT_UNITS_BOOLEAN)
#define MIXERCONTROL_CONTROLTYPE_MIXER (MIXERCONTROL_CONTROLTYPE_MULTIPLESELECT+1)
#define MIXERCONTROL_CONTROLTYPE_MICROTIME (MIXERCONTROL_CT_CLASS_TIME|MIXERCONTROL_CT_SC_TIME_MICROSECS|MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXERCONTROL_CONTROLTYPE_MILLITIME (MIXERCONTROL_CT_CLASS_TIME|MIXERCONTROL_CT_SC_TIME_MILLISECS|MIXERCONTROL_CT_UNITS_UNSIGNED)
#define MIXER_GETLINECONTROLSF_ALL 0
#define MIXER_GETLINECONTROLSF_ONEBYID 1
#define MIXER_GETLINECONTROLSF_ONEBYTYPE 2
#define MIXER_GETLINECONTROLSF_QUERYMASK 15
#define MIXER_GETCONTROLDETAILSF_VALUE 0
#define MIXER_GETCONTROLDETAILSF_LISTTEXT 1
#define MIXER_GETCONTROLDETAILSF_QUERYMASK 15
#define MIXER_SETCONTROLDETAILSF_VALUE 0
#define MIXER_SETCONTROLDETAILSF_CUSTOM 1
#define MIXER_SETCONTROLDETAILSF_QUERYMASK 15
#define TIMERR_NOERROR 0
#define TIMERR_NOCANDO (TIMERR_BASE+1)
#define TIMERR_STRUCT (TIMERR_BASE+33)
#define TIME_ONESHOT 0
#define TIME_PERIODIC 1
#define TIME_CALLBACK_FUNCTION 0
#define TIME_CALLBACK_EVENT_SET 16
#define TIME_CALLBACK_EVENT_PULSE 32
#if (WINVER >= 0x0501)
#define TIME_KILL_SYNCHRONOUS 0x0100
#endif
#define JOYERR_NOERROR (0)
#define JOYERR_PARMS (JOYERR_BASE+5)
#define JOYERR_NOCANDO (JOYERR_BASE+6)
#define JOYERR_UNPLUGGED (JOYERR_BASE+7)
#define JOY_BUTTON1 1
#define JOY_BUTTON2 2
#define JOY_BUTTON3 4
#define JOY_BUTTON4 8
#define JOY_BUTTON1CHG 256
#define JOY_BUTTON2CHG 512
#define JOY_BUTTON3CHG 1024
#define JOY_BUTTON4CHG 2048
#define JOY_BUTTON5 257
#define JOY_BUTTON6 513
#define JOY_BUTTON7 1025
#define JOY_BUTTON8 2049
#define JOY_BUTTON9 256
#define JOY_BUTTON10 512
#define JOY_BUTTON11 1024
#define JOY_BUTTON12 2048
#define JOY_BUTTON13 4096
#define JOY_BUTTON14 8192
#define JOY_BUTTON15 16384
#define JOY_BUTTON16 32768
#define JOY_BUTTON17 65536
#define JOY_BUTTON18 0x20000
#define JOY_BUTTON19 0x40000
#define JOY_BUTTON20 0x80000
#define JOY_BUTTON21 0x100000
#define JOY_BUTTON22 0x200000
#define JOY_BUTTON23 0x400000
#define JOY_BUTTON24 0x800000
#define JOY_BUTTON25 0x1000000
#define JOY_BUTTON26 0x2000000
#define JOY_BUTTON27 0x4000000
#define JOY_BUTTON28 0x8000000
#define JOY_BUTTON29 0x10000000
#define JOY_BUTTON30 0x20000000
#define JOY_BUTTON31 0x40000000
#define JOY_BUTTON32 0x80000000
#define JOY_POVCENTERED	((WORD)-1)
#define JOY_POVFORWARD	0
#define JOY_POVRIGHT	9000
#define JOY_POVBACKWARD	18000
#define JOY_POVLEFT	27000
#define JOY_RETURNX	1
#define JOY_RETURNY	2
#define JOY_RETURNZ	4l
#define JOY_RETURNR	8
#define JOY_RETURNU	16
#define JOY_RETURNV	32
#define JOY_RETURNPOV	64
#define JOY_RETURNBUTTONS	128
#define JOY_RETURNRAWDATA	256
#define JOY_RETURNPOVCTS	512
#define JOY_RETURNCENTERED	1024
#define JOY_USEDEADZONE	2048
#define JOY_RETURNALL	(JOY_RETURNX|JOY_RETURNY|JOY_RETURNZ|JOY_RETURNR|JOY_RETURNU|JOY_RETURNV|JOY_RETURNPOV|JOY_RETURNBUTTONS)
#define JOY_CAL_READALWAYS	0x10000
#define JOY_CAL_READXYONLY	0x20000
#define JOY_CAL_READ3	0x40000
#define JOY_CAL_READ4	0x80000
#define JOY_CAL_READXONLY	0x100000
#define JOY_CAL_READYONLY	0x200000
#define JOY_CAL_READ5	0x400000
#define JOY_CAL_READ6	0x800000
#define JOY_CAL_READZONLY	0x1000000
#define JOY_CAL_READRONLY	0x2000000
#define JOY_CAL_READUONLY	0x4000000
#define JOY_CAL_READVONLY	0x8000000
#define JOYSTICKID1 0
#define JOYSTICKID2 1
#define JOYCAPS_HASZ	1
#define JOYCAPS_HASR	2
#define JOYCAPS_HASU	4
#define JOYCAPS_HASV	8
#define JOYCAPS_HASPOV	16
#define JOYCAPS_POV4DIR	32
#define JOYCAPS_POVCTS	64
#define MMIOERR_BASE 256
#define MMIOERR_FILENOTFOUND (MMIOERR_BASE+1)
#define MMIOERR_OUTOFMEMORY (MMIOERR_BASE+2)
#define MMIOERR_CANNOTOPEN (MMIOERR_BASE+3)
#define MMIOERR_CANNOTCLOSE (MMIOERR_BASE+4)
#define MMIOERR_CANNOTREAD (MMIOERR_BASE+5)
#define MMIOERR_CANNOTWRITE (MMIOERR_BASE+6)
#define MMIOERR_CANNOTSEEK (MMIOERR_BASE+7)
#define MMIOERR_CANNOTEXPAND (MMIOERR_BASE+8)
#define MMIOERR_CHUNKNOTFOUND (MMIOERR_BASE+9)
#define MMIOERR_UNBUFFERED (MMIOERR_BASE+10)
#define MMIOERR_PATHNOTFOUND (MMIOERR_BASE+11)
#define MMIOERR_ACCESSDENIED (MMIOERR_BASE+12)
#define MMIOERR_SHARINGVIOLATION (MMIOERR_BASE+13)
#define MMIOERR_NETWORKERROR (MMIOERR_BASE+14)
#define MMIOERR_TOOMANYOPENFILES (MMIOERR_BASE+15)
#define MMIOERR_INVALIDFILE (MMIOERR_BASE+16)
#define CFSEPCHAR '+'
#define MMIO_RWMODE 3
#define MMIO_SHAREMODE 0x70
#define MMIO_CREATE 0x1000
#define MMIO_PARSE 256
#define MMIO_DELETE 512
#define MMIO_EXIST 0x4000
#define MMIO_ALLOCBUF 0x10000
#define MMIO_GETTEMP 0x20000
#define MMIO_DIRTY 0x10000000
#define MMIO_READ 0
#define MMIO_WRITE 1
#define MMIO_READWRITE 2
#define MMIO_COMPAT 0
#define MMIO_EXCLUSIVE 16
#define MMIO_DENYWRITE 32
#define MMIO_DENYREAD 0x30
#define MMIO_DENYNONE 64
#define MMIO_FHOPEN 16
#define MMIO_EMPTYBUF 16
#define MMIO_TOUPPER 16
#define MMIO_INSTALLPROC 0x10000
#define MMIO_GLOBALPROC 0x10000000
#define MMIO_REMOVEPROC 0x20000
#define MMIO_UNICODEPROC 0x1000000
#define MMIO_FINDPROC 0x40000
#define MMIO_FINDCHUNK 16
#define MMIO_FINDRIFF 32
#define MMIO_FINDLIST 64
#define MMIO_CREATERIFF 32
#define MMIO_CREATELIST 64
#define MMIOM_READ MMIO_READ
#define MMIOM_WRITE MMIO_WRITE
#define MMIOM_SEEK 2
#define MMIOM_OPEN 3
#define MMIOM_CLOSE 4
#define MMIOM_WRITEFLUSH 5
#define MMIOM_RENAME 6
#define MMIOM_USER 0x8000
#define FOURCC_RIFF mmioFOURCC('R', 'I', 'F', 'F')
#define FOURCC_LIST mmioFOURCC('L', 'I', 'S', 'T')
#define FOURCC_DOS mmioFOURCC('D', 'O', 'S', ' ')
#define FOURCC_MEM mmioFOURCC('M', 'E', 'M', ' ')
#define MMIO_DEFAULTBUFFER 8192
#define MCIERR_INVALID_DEVICE_ID (MCIERR_BASE+1)
#define MCIERR_UNRECOGNIZED_KEYWORD (MCIERR_BASE+3)
#define MCIERR_UNRECOGNIZED_COMMAND (MCIERR_BASE+5)
#define MCIERR_HARDWARE (MCIERR_BASE+6)
#define MCIERR_INVALID_DEVICE_NAME (MCIERR_BASE+7)
#define MCIERR_OUT_OF_MEMORY (MCIERR_BASE+8)
#define MCIERR_DEVICE_OPEN (MCIERR_BASE+9)
#define MCIERR_CANNOT_LOAD_DRIVER (MCIERR_BASE+10)
#define MCIERR_MISSING_COMMAND_STRING (MCIERR_BASE+11)
#define MCIERR_PARAM_OVERFLOW (MCIERR_BASE+12)
#define MCIERR_MISSING_STRING_ARGUMENT (MCIERR_BASE+13)
#define MCIERR_BAD_INTEGER (MCIERR_BASE+14)
#define MCIERR_PARSER_INTERNAL (MCIERR_BASE+15)
#define MCIERR_DRIVER_INTERNAL (MCIERR_BASE+16)
#define MCIERR_MISSING_PARAMETER (MCIERR_BASE+17)
#define MCIERR_UNSUPPORTED_FUNCTION (MCIERR_BASE+18)
#define MCIERR_FILE_NOT_FOUND (MCIERR_BASE+19)
#define MCIERR_DEVICE_NOT_READY (MCIERR_BASE+20)
#define MCIERR_INTERNAL (MCIERR_BASE+21)
#define MCIERR_DRIVER (MCIERR_BASE+22)
#define MCIERR_CANNOT_USE_ALL (MCIERR_BASE+23)
#define MCIERR_MULTIPLE (MCIERR_BASE+24)
#define MCIERR_EXTENSION_NOT_FOUND (MCIERR_BASE+25)
#define MCIERR_OUTOFRANGE (MCIERR_BASE+26)
#define MCIERR_FLAGS_NOT_COMPATIBLE (MCIERR_BASE+28)
#define MCIERR_FILE_NOT_SAVED (MCIERR_BASE+30)
#define MCIERR_DEVICE_TYPE_REQUIRED (MCIERR_BASE+31)
#define MCIERR_DEVICE_LOCKED (MCIERR_BASE+32)
#define MCIERR_DUPLICATE_ALIAS (MCIERR_BASE+33)
#define MCIERR_BAD_CONSTANT (MCIERR_BASE+34)
#define MCIERR_MUST_USE_SHAREABLE (MCIERR_BASE+35)
#define MCIERR_MISSING_DEVICE_NAME (MCIERR_BASE+36)
#define MCIERR_BAD_TIME_FORMAT (MCIERR_BASE+37)
#define MCIERR_NO_CLOSING_QUOTE (MCIERR_BASE+38)
#define MCIERR_DUPLICATE_FLAGS (MCIERR_BASE+39)
#define MCIERR_INVALID_FILE (MCIERR_BASE+40)
#define MCIERR_NULL_PARAMETER_BLOCK (MCIERR_BASE+41)
#define MCIERR_UNNAMED_RESOURCE (MCIERR_BASE+42)
#define MCIERR_NEW_REQUIRES_ALIAS (MCIERR_BASE+43)
#define MCIERR_NOTIFY_ON_AUTO_OPEN (MCIERR_BASE+44)
#define MCIERR_NO_ELEMENT_ALLOWED (MCIERR_BASE+45)
#define MCIERR_NONAPPLICABLE_FUNCTION (MCIERR_BASE+46)
#define MCIERR_ILLEGAL_FOR_AUTO_OPEN (MCIERR_BASE+47)
#define MCIERR_FILENAME_REQUIRED (MCIERR_BASE+48)
#define MCIERR_EXTRA_CHARACTERS (MCIERR_BASE+49)
#define MCIERR_DEVICE_NOT_INSTALLED (MCIERR_BASE+50)
#define MCIERR_GET_CD (MCIERR_BASE+51)
#define MCIERR_SET_CD (MCIERR_BASE+52)
#define MCIERR_SET_DRIVE (MCIERR_BASE+53)
#define MCIERR_DEVICE_LENGTH (MCIERR_BASE+54)
#define MCIERR_DEVICE_ORD_LENGTH (MCIERR_BASE+55)
#define MCIERR_NO_INTEGER (MCIERR_BASE+56)
#define MCIERR_WAVE_OUTPUTSINUSE (MCIERR_BASE+64)
#define MCIERR_WAVE_SETOUTPUTINUSE (MCIERR_BASE+65)
#define MCIERR_WAVE_INPUTSINUSE (MCIERR_BASE+66)
#define MCIERR_WAVE_SETINPUTINUSE (MCIERR_BASE+67)
#define MCIERR_WAVE_OUTPUTUNSPECIFIED (MCIERR_BASE+68)
#define MCIERR_WAVE_INPUTUNSPECIFIED (MCIERR_BASE+69)
#define MCIERR_WAVE_OUTPUTSUNSUITABLE (MCIERR_BASE+70)
#define MCIERR_WAVE_SETOUTPUTUNSUITABLE (MCIERR_BASE+71)
#define MCIERR_WAVE_INPUTSUNSUITABLE (MCIERR_BASE+72)
#define MCIERR_WAVE_SETINPUTUNSUITABLE (MCIERR_BASE+73)
#define MCIERR_SEQ_DIV_INCOMPATIBLE (MCIERR_BASE+80)
#define MCIERR_SEQ_PORT_INUSE (MCIERR_BASE+81)
#define MCIERR_SEQ_PORT_NONEXISTENT (MCIERR_BASE+82)
#define MCIERR_SEQ_PORT_MAPNODEVICE (MCIERR_BASE+83)
#define MCIERR_SEQ_PORT_MISCERROR (MCIERR_BASE+84)
#define MCIERR_SEQ_TIMER (MCIERR_BASE+85)
#define MCIERR_SEQ_PORTUNSPECIFIED (MCIERR_BASE+86)
#define MCIERR_SEQ_NOMIDIPRESENT (MCIERR_BASE+87)
#define MCIERR_NO_WINDOW (MCIERR_BASE+90)
#define MCIERR_CREATEWINDOW (MCIERR_BASE+91)
#define MCIERR_FILE_READ (MCIERR_BASE+92)
#define MCIERR_FILE_WRITE (MCIERR_BASE+93)
#define MCIERR_NO_IDENTITY (MCIERR_BASE+94)
#define MCIERR_CUSTOM_DRIVER_BASE (MCIERR_BASE+256)
#define MCI_FIRST DRV_MCI_FIRST
#define MCI_OPEN 0x803
#define MCI_CLOSE 0x804
#define MCI_ESCAPE 0x805
#define MCI_PLAY 0x806
#define MCI_SEEK 0x807
#define MCI_STOP 0x808
#define MCI_PAUSE 0x809
#define MCI_INFO 0x80A
#define MCI_GETDEVCAPS 0x80B
#define MCI_SPIN 0x80C
#define MCI_SET 0x80D
#define MCI_STEP 0x80E
#define MCI_RECORD 0x80F
#define MCI_SYSINFO 0x810
#define MCI_BREAK 0x811
#define MCI_SAVE 0x813
#define MCI_STATUS 0x814
#define MCI_CUE 0x830
#define MCI_REALIZE 0x840
#define MCI_WINDOW 0x841
#define MCI_PUT 0x842
#define MCI_WHERE 0x843
#define MCI_FREEZE 0x844
#define MCI_UNFREEZE 0x845
#define MCI_LOAD 0x850
#define MCI_CUT 0x851
#define MCI_COPY 0x852
#define MCI_PASTE 0x853
#define MCI_UPDATE 0x854
#define MCI_RESUME 0x855
#define MCI_DELETE 0x856
#define MCI_USER_MESSAGES (DRV_MCI_FIRST+0x400)
#define MCI_LAST 0xFFF
#define MCI_ALL_DEVICE_ID ((MCIDEVICEID)-1)
#define MCI_DEVTYPE_VCR 513
#define MCI_DEVTYPE_VIDEODISC 514
#define MCI_DEVTYPE_OVERLAY 515
#define MCI_DEVTYPE_CD_AUDIO 516
#define MCI_DEVTYPE_DAT 517
#define MCI_DEVTYPE_SCANNER 518
#define MCI_DEVTYPE_ANIMATION 519
#define MCI_DEVTYPE_DIGITAL_VIDEO 520
#define MCI_DEVTYPE_OTHER 521
#define MCI_DEVTYPE_WAVEFORM_AUDIO 522
#define MCI_DEVTYPE_SEQUENCER 523
#define MCI_DEVTYPE_FIRST MCI_DEVTYPE_VCR
#define MCI_DEVTYPE_LAST MCI_DEVTYPE_SEQUENCER
#define MCI_DEVTYPE_FIRST_USER 0x1000
#define MCI_MODE_NOT_READY (MCI_STRING_OFFSET+12)
#define MCI_MODE_STOP (MCI_STRING_OFFSET+13)
#define MCI_MODE_PLAY (MCI_STRING_OFFSET+14)
#define MCI_MODE_RECORD (MCI_STRING_OFFSET+15)
#define MCI_MODE_SEEK (MCI_STRING_OFFSET+16)
#define MCI_MODE_PAUSE (MCI_STRING_OFFSET+17)
#define MCI_MODE_OPEN (MCI_STRING_OFFSET+18)
#define MCI_FORMAT_MILLISECONDS 0
#define MCI_FORMAT_HMS 1
#define MCI_FORMAT_MSF 2
#define MCI_FORMAT_FRAMES 3
#define MCI_FORMAT_SMPTE_24 4
#define MCI_FORMAT_SMPTE_25 5
#define MCI_FORMAT_SMPTE_30 6
#define MCI_FORMAT_SMPTE_30DROP 7
#define MCI_FORMAT_BYTES 8
#define MCI_FORMAT_SAMPLES 9
#define MCI_FORMAT_TMSF 10
#define MCI_MSF_MINUTE(t) ((BYTE)(t))
#define MCI_MSF_SECOND(t) ((BYTE)(((WORD)(t))>>8))
#define MCI_MSF_FRAME(t) ((BYTE)((t)>>16))
#define MCI_MAKE_MSF(m,s,f) ((DWORD)(((BYTE)(m)|((WORD)(s)<<8))|(((DWORD)(BYTE)(f))<<16)))
#define MCI_TMSF_TRACK(t) ((BYTE)(t))
#define MCI_TMSF_MINUTE(t) ((BYTE)(((WORD)(t))>>8))
#define MCI_TMSF_SECOND(t) ((BYTE)((t)>>16))
#define MCI_TMSF_FRAME(t) ((BYTE)((t)>>24))
#define MCI_MAKE_TMSF(t,m,s,f) ((DWORD)(((BYTE)(t)|((WORD)(m)<<8))|(((DWORD)(BYTE)(s)|((WORD)(f)<<8))<<16)))
#define MCI_HMS_HOUR(t) ((BYTE)(t))
#define MCI_HMS_MINUTE(t) ((BYTE)(((WORD)(t))>>8))
#define MCI_HMS_SECOND(t) ((BYTE)((t)>>16))
#define MCI_MAKE_HMS(h,m,s) ((DWORD)(((BYTE)(h)|((WORD)(m)<<8))|(((DWORD)(BYTE)(s))<<16)))
#define MCI_NOTIFY_SUCCESSFUL 1
#define MCI_NOTIFY_SUPERSEDED 2
#define MCI_NOTIFY_ABORTED 4
#define MCI_NOTIFY_FAILURE 8
#define MCI_NOTIFY 1
#define MCI_WAIT 2
#define MCI_FROM 4
#define MCI_TO 8
#define MCI_TRACK 16
#define MCI_OPEN_SHAREABLE 256
#define MCI_OPEN_ELEMENT 512
#define MCI_OPEN_ALIAS 1024
#define MCI_OPEN_ELEMENT_ID 2048
#define MCI_OPEN_TYPE_ID 0x1000
#define MCI_OPEN_TYPE 0x2000
#define MCI_SEEK_TO_START 256
#define MCI_SEEK_TO_END 512
#define MCI_STATUS_ITEM 256
#define MCI_STATUS_START 512
#define MCI_STATUS_LENGTH 1
#define MCI_STATUS_POSITION 2
#define MCI_STATUS_NUMBER_OF_TRACKS 3
#define MCI_STATUS_MODE 4
#define MCI_STATUS_MEDIA_PRESENT 5
#define MCI_STATUS_TIME_FORMAT 6
#define MCI_STATUS_READY 7
#define MCI_STATUS_CURRENT_TRACK 8
#define MCI_INFO_PRODUCT 256
#define MCI_INFO_FILE 512
#define MCI_INFO_MEDIA_UPC 1024
#define MCI_INFO_MEDIA_IDENTITY 2048
#define MCI_INFO_NAME 0x1000
#define MCI_INFO_COPYRIGHT 0x2000
#define MCI_GETDEVCAPS_ITEM 256
#define MCI_GETDEVCAPS_CAN_RECORD 1
#define MCI_GETDEVCAPS_HAS_AUDIO 2
#define MCI_GETDEVCAPS_HAS_VIDEO 3
#define MCI_GETDEVCAPS_DEVICE_TYPE 4
#define MCI_GETDEVCAPS_USES_FILES 5
#define MCI_GETDEVCAPS_COMPOUND_DEVICE 6
#define MCI_GETDEVCAPS_CAN_EJECT 7
#define MCI_GETDEVCAPS_CAN_PLAY 8
#define MCI_GETDEVCAPS_CAN_SAVE 9
#define MCI_SYSINFO_QUANTITY 256
#define MCI_SYSINFO_OPEN 512
#define MCI_SYSINFO_NAME 1024
#define MCI_SYSINFO_INSTALLNAME 2048
#define MCI_SET_DOOR_OPEN 256
#define MCI_SET_DOOR_CLOSED 512
#define MCI_SET_TIME_FORMAT 1024
#define MCI_SET_AUDIO 2048
#define MCI_SET_VIDEO 0x1000
#define MCI_SET_ON 0x2000
#define MCI_SET_OFF 0x4000
#define MCI_SET_AUDIO_ALL 0
#define MCI_SET_AUDIO_LEFT 1
#define MCI_SET_AUDIO_RIGHT 2
#define MCI_BREAK_KEY 256
#define MCI_BREAK_HWND 512
#define MCI_BREAK_OFF 1024
#define MCI_RECORD_INSERT 256
#define MCI_RECORD_OVERWRITE 512
#define MCI_SAVE_FILE 256
#define MCI_LOAD_FILE 256
#define MCI_VD_MODE_PARK (MCI_VD_OFFSET+1)
#define MCI_VD_MEDIA_CLV (MCI_VD_OFFSET+2)
#define MCI_VD_MEDIA_CAV (MCI_VD_OFFSET+3)
#define MCI_VD_MEDIA_OTHER (MCI_VD_OFFSET+4)
#define MCI_VD_FORMAT_TRACK 0x4001
#define MCI_VD_PLAY_REVERSE 0x10000
#define MCI_VD_PLAY_FAST 0x20000
#define MCI_VD_PLAY_SPEED 0x40000
#define MCI_VD_PLAY_SCAN 0x80000
#define MCI_VD_PLAY_SLOW 0x100000
#define MCI_VD_SEEK_REVERSE 0x10000
#define MCI_VD_STATUS_SPEED 0x4002
#define MCI_VD_STATUS_FORWARD 0x4003
#define MCI_VD_STATUS_MEDIA_TYPE 0x4004
#define MCI_VD_STATUS_SIDE 0x4005
#define MCI_VD_STATUS_DISC_SIZE 0x4006
#define MCI_VD_GETDEVCAPS_CLV 0x10000
#define MCI_VD_GETDEVCAPS_CAV 0x20000
#define MCI_VD_SPIN_UP 0x10000
#define MCI_VD_SPIN_DOWN 0x20000
#define MCI_VD_GETDEVCAPS_CAN_REVERSE 0x4002
#define MCI_VD_GETDEVCAPS_FAST_RATE 0x4003
#define MCI_VD_GETDEVCAPS_SLOW_RATE 0x4004
#define MCI_VD_GETDEVCAPS_NORMAL_RATE 0x4005
#define MCI_VD_STEP_FRAMES 0x10000
#define MCI_VD_STEP_REVERSE 0x20000
#define MCI_VD_ESCAPE_STRING 256
#define MCI_CDA_STATUS_TYPE_TRACK 0x4001
#define MCI_CDA_TRACK_AUDIO MCI_CD_OFFSET
#define MCI_CDA_TRACK_OTHER (MCI_CD_OFFSET+1)
#define MCI_WAVE_PCM MCI_WAVE_OFFSET
#define MCI_WAVE_MAPPER (MCI_WAVE_OFFSET+1)
#define MCI_WAVE_OPEN_BUFFER 0x10000
#define MCI_WAVE_SET_FORMATTAG 0x10000
#define MCI_WAVE_SET_CHANNELS 0x20000
#define MCI_WAVE_SET_SAMPLESPERSEC 0x40000
#define MCI_WAVE_SET_AVGBYTESPERSEC 0x80000
#define MCI_WAVE_SET_BLOCKALIGN 0x100000
#define MCI_WAVE_SET_BITSPERSAMPLE 0x200000
#define MCI_WAVE_INPUT 0x400000
#define MCI_WAVE_OUTPUT 0x800000
#define MCI_WAVE_STATUS_FORMATTAG 0x4001
#define MCI_WAVE_STATUS_CHANNELS 0x4002
#define MCI_WAVE_STATUS_SAMPLESPERSEC 0x4003
#define MCI_WAVE_STATUS_AVGBYTESPERSEC 0x4004
#define MCI_WAVE_STATUS_BLOCKALIGN 0x4005
#define MCI_WAVE_STATUS_BITSPERSAMPLE 0x4006
#define MCI_WAVE_STATUS_LEVEL 0x4007
#define MCI_WAVE_SET_ANYINPUT 0x4000000
#define MCI_WAVE_SET_ANYOUTPUT 0x8000000
#define MCI_WAVE_GETDEVCAPS_INPUTS 0x4001
#define MCI_WAVE_GETDEVCAPS_OUTPUTS 0x4002
#define MCI_SEQ_DIV_PPQN MCI_SEQ_OFFSET
#define MCI_SEQ_DIV_SMPTE_24 (MCI_SEQ_OFFSET+1)
#define MCI_SEQ_DIV_SMPTE_25 (MCI_SEQ_OFFSET+2)
#define MCI_SEQ_DIV_SMPTE_30DROP (MCI_SEQ_OFFSET+3)
#define MCI_SEQ_DIV_SMPTE_30 (MCI_SEQ_OFFSET+4)
#define MCI_SEQ_FORMAT_SONGPTR 0x4001
#define MCI_SEQ_FILE 0x4002
#define MCI_SEQ_MIDI 0x4003
#define MCI_SEQ_SMPTE 0x4004
#define MCI_SEQ_NONE 65533
#define MCI_SEQ_MAPPER 65535
#define MCI_SEQ_STATUS_TEMPO 0x4002
#define MCI_SEQ_STATUS_PORT 0x4003
#define MCI_SEQ_STATUS_SLAVE 0x4007
#define MCI_SEQ_STATUS_MASTER 0x4008
#define MCI_SEQ_STATUS_OFFSET 0x4009
#define MCI_SEQ_STATUS_DIVTYPE 0x400A
#define MCI_SEQ_STATUS_NAME 0x400B
#define MCI_SEQ_STATUS_COPYRIGHT 0x400C
#define MCI_SEQ_SET_TEMPO 0x10000
#define MCI_SEQ_SET_PORT 0x20000
#define MCI_SEQ_SET_SLAVE 0x40000
#define MCI_SEQ_SET_MASTER 0x80000
#define MCI_SEQ_SET_OFFSET 0x1000000
#define MCI_ANIM_OPEN_WS 0x10000
#define MCI_ANIM_OPEN_PARENT 0x20000
#define MCI_ANIM_OPEN_NOSTATIC 0x40000
#define MCI_ANIM_PLAY_SPEED 0x10000
#define MCI_ANIM_PLAY_REVERSE 0x20000
#define MCI_ANIM_PLAY_FAST 0x40000
#define MCI_ANIM_PLAY_SLOW 0x80000
#define MCI_ANIM_PLAY_SCAN 0x100000
#define MCI_ANIM_STEP_REVERSE 0x10000
#define MCI_ANIM_STEP_FRAMES 0x20000
#define MCI_ANIM_STATUS_SPEED 0x4001
#define MCI_ANIM_STATUS_FORWARD 0x4002
#define MCI_ANIM_STATUS_HWND 0x4003
#define MCI_ANIM_STATUS_HPAL 0x4004
#define MCI_ANIM_STATUS_STRETCH 0x4005
#define MCI_ANIM_INFO_TEXT 0x10000
#define MCI_ANIM_GETDEVCAPS_CAN_REVERSE 0x4001
#define MCI_ANIM_GETDEVCAPS_FAST_RATE 0x4002
#define MCI_ANIM_GETDEVCAPS_SLOW_RATE 0x4003
#define MCI_ANIM_GETDEVCAPS_NORMAL_RATE 0x4004
#define MCI_ANIM_GETDEVCAPS_PALETTES 0x4006
#define MCI_ANIM_GETDEVCAPS_CAN_STRETCH 0x4007
#define MCI_ANIM_GETDEVCAPS_MAX_WINDOWS 0x4008
#define MCI_ANIM_REALIZE_NORM 0x10000
#define MCI_ANIM_REALIZE_BKGD 0x20000
#define MCI_ANIM_WINDOW_HWND 0x10000
#define MCI_ANIM_WINDOW_STATE 0x40000
#define MCI_ANIM_WINDOW_TEXT 0x80000
#define MCI_ANIM_WINDOW_ENABLE_STRETCH 0x100000
#define MCI_ANIM_WINDOW_DISABLE_STRETCH 0x200000
#define MCI_ANIM_WINDOW_DEFAULT 0xL
#define MCI_ANIM_RECT 0x10000
#define MCI_ANIM_PUT_SOURCE 0x20000
#define MCI_ANIM_PUT_DESTINATION 0x40000
#define MCI_ANIM_WHERE_SOURCE 0x20000
#define MCI_ANIM_WHERE_DESTINATION 0x40000
#define MCI_ANIM_UPDATE_HDC 0x20000
#define MCI_OVLY_OPEN_WS 0x10000
#define MCI_OVLY_OPEN_PARENT 0x20000
#define MCI_OVLY_STATUS_HWND 0x4001
#define MCI_OVLY_STATUS_STRETCH 0x4002
#define MCI_OVLY_INFO_TEXT 0x10000
#define MCI_OVLY_GETDEVCAPS_CAN_STRETCH 0x4001
#define MCI_OVLY_GETDEVCAPS_CAN_FREEZE 0x4002
#define MCI_OVLY_GETDEVCAPS_MAX_WINDOWS 0x4003
#define MCI_OVLY_WINDOW_HWND 0x10000
#define MCI_OVLY_WINDOW_STATE 0x40000
#define MCI_OVLY_WINDOW_TEXT 0x80000
#define MCI_OVLY_WINDOW_ENABLE_STRETCH 0x100000
#define MCI_OVLY_WINDOW_DISABLE_STRETCH 0x200000
#define MCI_OVLY_WINDOW_DEFAULT 0xL
#define MCI_OVLY_RECT 0x10000
#define MCI_OVLY_PUT_SOURCE 0x20000
#define MCI_OVLY_PUT_DESTINATION 0x40000
#define MCI_OVLY_PUT_FRAME 0x80000
#define MCI_OVLY_PUT_VIDEO 0x100000
#define MCI_OVLY_WHERE_SOURCE 0x20000
#define MCI_OVLY_WHERE_DESTINATION 0x40000
#define MCI_OVLY_WHERE_FRAME 0x80000
#define MCI_OVLY_WHERE_VIDEO 0x100000
#define NEWTRANSPARENT 3
#define QUERYROPSUPPORT 40
#define SELECTDIB 41
#define DIBINDEX(n) MAKELONG((n),0x10FF)
#define SC_SCREENSAVE 0xF140
#define CAPS1 94
#define C1_TRANSPARENT 1
#ifndef SEEK_SET
#define SEEK_SET 0
#endif
#ifndef SEEK_CUR
#define SEEK_CUR 1
#endif
#ifndef SEEK_END
#define SEEK_END 2
#endif

typedef DWORD MCIERROR;
typedef UINT MCIDEVICEID;
typedef UINT(CALLBACK *YIELDPROC)(MCIDEVICEID,DWORD);
typedef UINT MMVERSION;
typedef UINT MMRESULT;
typedef struct mmtime_tag {
	UINT wType;
	union {
		DWORD ms;
		DWORD sample;
		DWORD cb;
		DWORD ticks;
		struct {
			BYTE hour;
			BYTE min;
			BYTE sec;
			BYTE frame;
			BYTE fps;
			BYTE dummy;
			BYTE pad[2];
		} smpte;
		struct {
			DWORD songptrpos;
		} midi;
	} u;
} MMTIME,*PMMTIME,*LPMMTIME;
DECLARE_HANDLE(HDRVR);
typedef struct tagDRVCONFIGINFO {
	DWORD dwDCISize;
	LPCWSTR lpszDCISectionName;
	LPCWSTR lpszDCIAliasName;
} DRVCONFIGINFO,*PDRVCONFIGINFO,*LPDRVCONFIGINFO;
typedef struct DRVCONFIGINFOEX {
	DWORD dwDCISize;
	LPCWSTR lpszDCISectionName;
	LPCWSTR lpszDCIAliasName;
	DWORD dnDevNode;
} DRVCONFIGINFOEX,*PDRVCONFIGINFOEX,*LPDRVCONFIGINFOEX;
typedef LRESULT(CALLBACK* DRIVERPROC)(DWORD,HDRVR,UINT,LPARAM,LPARAM);
typedef void (CALLBACK DRVCALLBACK)(HDRVR,UINT,DWORD,DWORD,DWORD);
typedef DRVCALLBACK *LPDRVCALLBACK;
typedef DRVCALLBACK *PDRVCALLBACK;
DECLARE_HANDLE(HWAVE);
DECLARE_HANDLE(HWAVEIN);
DECLARE_HANDLE(HWAVEOUT);
typedef HWAVEIN *LPHWAVEIN;
typedef HWAVEOUT *LPHWAVEOUT;
typedef DRVCALLBACK WAVECALLBACK;
typedef WAVECALLBACK *LPWAVECALLBACK;
typedef struct wavehdr_tag {
	LPSTR lpData;
	DWORD dwBufferLength;
	DWORD dwBytesRecorded;
	DWORD dwUser;
	DWORD dwFlags;
	DWORD dwLoops;
	struct wavehdr_tag *lpNext;
	DWORD reserved;
} WAVEHDR,*PWAVEHDR,*LPWAVEHDR;
typedef struct tagWAVEOUTCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	DWORD dwFormats;
	WORD wChannels;
	WORD wReserved1;
	DWORD dwSupport;
} WAVEOUTCAPSA,*PWAVEOUTCAPSA,*LPWAVEOUTCAPSA;
typedef struct tagWAVEOUTCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	DWORD dwFormats;
	WORD wChannels;
	WORD wReserved1;
	DWORD dwSupport;
} WAVEOUTCAPSW,*PWAVEOUTCAPSW,*LPWAVEOUTCAPSW;
typedef struct tagWAVEINCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	DWORD dwFormats;
	WORD wChannels;
	WORD wReserved1;
} WAVEINCAPSA,*PWAVEINCAPSA,*LPWAVEINCAPSA;
typedef struct tagWAVEINCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	DWORD dwFormats;
	WORD wChannels;
	WORD wReserved1;
} WAVEINCAPSW,*PWAVEINCAPSW,*LPWAVEINCAPSW;
typedef struct waveformat_tag {
	WORD wFormatTag;
	WORD nChannels;
	DWORD nSamplesPerSec;
	DWORD nAvgBytesPerSec;
	WORD nBlockAlign;
} WAVEFORMAT,*PWAVEFORMAT,*LPWAVEFORMAT;
typedef struct pcmwaveformat_tag {
	WAVEFORMAT wf;
	WORD wBitsPerSample;
} PCMWAVEFORMAT, *PPCMWAVEFORMAT,*LPPCMWAVEFORMAT;
#ifndef _WAVEFORMATEX_
#define _WAVEFORMATEX_
typedef struct tWAVEFORMATEX {
	WORD wFormatTag;
	WORD nChannels;
	DWORD nSamplesPerSec;
	DWORD nAvgBytesPerSec;
	WORD nBlockAlign;
	WORD wBitsPerSample;
	WORD cbSize;
} WAVEFORMATEX,*PWAVEFORMATEX,*LPWAVEFORMATEX;
typedef const WAVEFORMATEX *LPCWAVEFORMATEX;
#endif
DECLARE_HANDLE(HMIDI);
DECLARE_HANDLE(HMIDIIN);
DECLARE_HANDLE(HMIDIOUT);
DECLARE_HANDLE(HMIDISTRM);
typedef HMIDI *LPHMIDI;
typedef HMIDIIN *LPHMIDIIN;
typedef HMIDIOUT *LPHMIDIOUT;
typedef HMIDISTRM *LPHMIDISTRM;
typedef DRVCALLBACK MIDICALLBACK;
typedef MIDICALLBACK *LPMIDICALLBACK;
typedef WORD PATCHARRAY[MIDIPATCHSIZE];
typedef WORD *LPPATCHARRAY;
typedef WORD KEYARRAY[MIDIPATCHSIZE];
typedef WORD *LPKEYARRAY;
typedef struct tagMIDIOUTCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	WORD wTechnology;
	WORD wVoices;
	WORD wNotes;
	WORD wChannelMask;
	DWORD dwSupport;
} MIDIOUTCAPSA,*PMIDIOUTCAPSA,*LPMIDIOUTCAPSA;
typedef struct tagMIDIOUTCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	WORD wTechnology;
	WORD wVoices;
	WORD wNotes;
	WORD wChannelMask;
	DWORD dwSupport;
} MIDIOUTCAPSW,*PMIDIOUTCAPSW,*LPMIDIOUTCAPSW;
typedef struct tagMIDIINCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	DWORD dwSupport;
} MIDIINCAPSA,*PMIDIINCAPSA,*LPMIDIINCAPSA;
typedef struct tagMIDIINCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	DWORD dwSupport;
} MIDIINCAPSW,*PMIDIINCAPSW,*NPMIDIINCAPSW,*LPMIDIINCAPSW;
typedef struct midihdr_tag {
	LPSTR lpData;
	DWORD dwBufferLength;
	DWORD dwBytesRecorded;
	DWORD dwUser;
	DWORD dwFlags;
	struct midihdr_tag *lpNext;
	DWORD reserved;
	DWORD dwOffset;
	DWORD dwReserved[8];
} MIDIHDR,*PMIDIHDR,*LPMIDIHDR;
typedef struct midievent_tag {
	DWORD dwDeltaTime;
	DWORD dwStreamID;
	DWORD dwEvent;
	DWORD dwParms[1];
} MIDIEVENT;
typedef struct midistrmbuffver_tag {
	DWORD dwVersion;
	DWORD dwMid;
	DWORD dwOEMVersion;
} MIDISTRMBUFFVER;
typedef struct midiproptimediv_tag {
	DWORD cbStruct;
	DWORD dwTimeDiv;
} MIDIPROPTIMEDIV,*LPMIDIPROPTIMEDIV;
typedef struct midiproptempo_tag {
	DWORD cbStruct;
	DWORD dwTempo;
} MIDIPROPTEMPO,*LPMIDIPROPTEMPO;
typedef struct tagAUXCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	WORD wTechnology;
	WORD wReserved1;
	DWORD dwSupport;
} AUXCAPSA,*PAUXCAPSA,*LPAUXCAPSA;
typedef struct tagAUXCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	WORD wTechnology;
	WORD wReserved1;
	DWORD dwSupport;
} AUXCAPSW,*PAUXCAPSW,*LPAUXCAPSW;
DECLARE_HANDLE(HMIXEROBJ);
typedef HMIXEROBJ *LPHMIXEROBJ;
DECLARE_HANDLE(HMIXER);
typedef HMIXER *LPHMIXER;
typedef struct tagMIXERCAPSA {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	CHAR szPname[MAXPNAMELEN];
	DWORD fdwSupport;
	DWORD cDestinations;
} MIXERCAPSA,*PMIXERCAPSA,*LPMIXERCAPSA;
typedef struct tagMIXERCAPSW {
	WORD wMid;
	WORD wPid;
	MMVERSION vDriverVersion;
	WCHAR szPname[MAXPNAMELEN];
	DWORD fdwSupport;
	DWORD cDestinations;
} MIXERCAPSW,*PMIXERCAPSW,*LPMIXERCAPSW;
typedef struct tagMIXERLINEA {
	DWORD cbStruct;
	DWORD dwDestination;
	DWORD dwSource;
	DWORD dwLineID;
	DWORD fdwLine;
	DWORD dwUser;
	DWORD dwComponentType;
	DWORD cChannels;
	DWORD cConnections;
	DWORD cControls;
	CHAR szShortName[MIXER_SHORT_NAME_CHARS];
	CHAR szName[MIXER_LONG_NAME_CHARS];
	struct {
		DWORD dwType;
		DWORD dwDeviceID;
		WORD wMid;
		WORD wPid;
		MMVERSION vDriverVersion;
		CHAR szPname[MAXPNAMELEN];
	} Target;
} MIXERLINEA,*PMIXERLINEA,*LPMIXERLINEA;
typedef struct tagMIXERLINEW {
	DWORD cbStruct;
	DWORD dwDestination;
	DWORD dwSource;
	DWORD dwLineID;
	DWORD fdwLine;
	DWORD dwUser;
	DWORD dwComponentType;
	DWORD cChannels;
	DWORD cConnections;
	DWORD cControls;
	WCHAR szShortName[MIXER_SHORT_NAME_CHARS];
	WCHAR szName[MIXER_LONG_NAME_CHARS];
	struct {
		DWORD dwType;
		DWORD dwDeviceID;
		WORD wMid;
		WORD wPid;
		MMVERSION vDriverVersion;
		WCHAR szPname[MAXPNAMELEN];
	} Target;
} MIXERLINEW,*PMIXERLINEW,*LPMIXERLINEW;
typedef struct tagMIXERCONTROLA {
	DWORD cbStruct;
	DWORD dwControlID;
	DWORD dwControlType;
	DWORD fdwControl;
	DWORD cMultipleItems;
	CHAR szShortName[MIXER_SHORT_NAME_CHARS];
	CHAR szName[MIXER_LONG_NAME_CHARS];
	union {
		_ANONYMOUS_STRUCT struct {
			LONG lMinimum;
			LONG lMaximum;
		}_STRUCT_NAME(s);
		_ANONYMOUS_STRUCT struct {
			DWORD dwMinimum;
			DWORD dwMaximum;
		}_STRUCT_NAME(s1);
		DWORD dwReserved[6];
	} Bounds;
	union {
		DWORD cSteps;
		DWORD cbCustomData;
		DWORD dwReserved[6];
	} Metrics;
} MIXERCONTROLA,*PMIXERCONTROLA,*LPMIXERCONTROLA;
typedef struct tagMIXERCONTROLW {
	DWORD cbStruct;
	DWORD dwControlID;
	DWORD dwControlType;
	DWORD fdwControl;
	DWORD cMultipleItems;
	WCHAR szShortName[MIXER_SHORT_NAME_CHARS];
	WCHAR szName[MIXER_LONG_NAME_CHARS];
	union {
		_ANONYMOUS_STRUCT struct {
			LONG lMinimum;
			LONG lMaximum;
		}_STRUCT_NAME(s);
		_ANONYMOUS_STRUCT struct {
			DWORD dwMinimum;
			DWORD dwMaximum;
		}_STRUCT_NAME(s1);
		DWORD dwReserved[6];
	} Bounds;
	union {
		DWORD cSteps;
		DWORD cbCustomData;
		DWORD dwReserved[6];
	} Metrics;
} MIXERCONTROLW,*PMIXERCONTROLW,*LPMIXERCONTROLW;
typedef struct tagMIXERLINECONTROLSA {
	DWORD cbStruct;
	DWORD dwLineID;
	_ANONYMOUS_UNION union {
		DWORD dwControlID;
		DWORD dwControlType;
	} DUMMYUNIONNAME;
	DWORD cControls;
	DWORD cbmxctrl;
	LPMIXERCONTROLA pamxctrl;
} MIXERLINECONTROLSA,*PMIXERLINECONTROLSA,*LPMIXERLINECONTROLSA;
typedef struct tagMIXERLINECONTROLSW {
	DWORD cbStruct;
	DWORD dwLineID;
	_ANONYMOUS_UNION union {
		DWORD dwControlID;
		DWORD dwControlType;
	} DUMMYUNIONNAME;
	DWORD cControls;
	DWORD cbmxctrl;
	LPMIXERCONTROLW pamxctrl;
} MIXERLINECONTROLSW,*PMIXERLINECONTROLSW,*LPMIXERLINECONTROLSW;
typedef struct tMIXERCONTROLDETAILS {
	DWORD cbStruct;
	DWORD dwControlID;
	DWORD cChannels;
	_ANONYMOUS_UNION union {
		HWND hwndOwner;
		DWORD cMultipleItems;
	} DUMMYUNIONNAME;
	DWORD cbDetails;
	PVOID paDetails;
} MIXERCONTROLDETAILS,*PMIXERCONTROLDETAILS,*LPMIXERCONTROLDETAILS;
typedef struct tagMIXERCONTROLDETAILS_LISTTEXTA {
	DWORD dwParam1;
	DWORD dwParam2;
	CHAR szName[MIXER_LONG_NAME_CHARS];
} MIXERCONTROLDETAILS_LISTTEXTA,*PMIXERCONTROLDETAILS_LISTTEXTA,*LPMIXERCONTROLDETAILS_LISTTEXTA;
typedef struct tagMIXERCONTROLDETAILS_LISTTEXTW {
	DWORD dwParam1;
	DWORD dwParam2;
	WCHAR szName[MIXER_LONG_NAME_CHARS];
} MIXERCONTROLDETAILS_LISTTEXTW,*PMIXERCONTROLDETAILS_LISTTEXTW,*LPMIXERCONTROLDETAILS_LISTTEXTW;
typedef struct tMIXERCONTROLDETAILS_BOOLEAN {
	LONG fValue;
} MIXERCONTROLDETAILS_BOOLEAN,*PMIXERCONTROLDETAILS_BOOLEAN,*LPMIXERCONTROLDETAILS_BOOLEAN;
typedef struct tMIXERCONTROLDETAILS_SIGNED {
	LONG lValue;
} MIXERCONTROLDETAILS_SIGNED,*PMIXERCONTROLDETAILS_SIGNED,*LPMIXERCONTROLDETAILS_SIGNED;
typedef struct tMIXERCONTROLDETAILS_UNSIGNED {
	DWORD dwValue;
} MIXERCONTROLDETAILS_UNSIGNED,*PMIXERCONTROLDETAILS_UNSIGNED,*LPMIXERCONTROLDETAILS_UNSIGNED;
typedef void(CALLBACK TIMECALLBACK)(UINT,UINT,DWORD,DWORD,DWORD);
typedef TIMECALLBACK *LPTIMECALLBACK;
typedef struct timecaps_tag {
	UINT wPeriodMin;
	UINT wPeriodMax;
} TIMECAPS,*PTIMECAPS,*LPTIMECAPS;
typedef struct tagJOYCAPSA {
	WORD wMid;
	WORD wPid;
	CHAR szPname[MAXPNAMELEN];
	UINT wXmin;
	UINT wXmax;
	UINT wYmin;
	UINT wYmax;
	UINT wZmin;
	UINT wZmax;
	UINT wNumButtons;
	UINT wPeriodMin;
	UINT wPeriodMax;
	UINT wRmin;
	UINT wRmax;
	UINT wUmin;
	UINT wUmax;
	UINT wVmin;
	UINT wVmax;
	UINT wCaps;
	UINT wMaxAxes;
	UINT wNumAxes;
	UINT wMaxButtons;
	CHAR szRegKey[MAXPNAMELEN];
	CHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
} JOYCAPSA,*PJOYCAPSA,*LPJOYCAPSA;
typedef struct tagJOYCAPSW {
	WORD wMid;
	WORD wPid;
	WCHAR szPname[MAXPNAMELEN];
	UINT wXmin;
	UINT wXmax;
	UINT wYmin;
	UINT wYmax;
	UINT wZmin;
	UINT wZmax;
	UINT wNumButtons;
	UINT wPeriodMin;
	UINT wPeriodMax;
	UINT wRmin;
	UINT wRmax;
	UINT wUmin;
	UINT wUmax;
	UINT wVmin;
	UINT wVmax;
	UINT wCaps;
	UINT wMaxAxes;
	UINT wNumAxes;
	UINT wMaxButtons;
	WCHAR szRegKey[MAXPNAMELEN];
	WCHAR szOEMVxD[MAX_JOYSTICKOEMVXDNAME];
} JOYCAPSW,*PJOYCAPSW,*LPJOYCAPSW;
typedef struct joyinfo_tag {
	UINT wXpos;
	UINT wYpos;
	UINT wZpos;
	UINT wButtons;
} JOYINFO,*PJOYINFO,*LPJOYINFO;
typedef struct joyinfoex_tag {
	DWORD dwSize;
	DWORD dwFlags;
	DWORD dwXpos;
	DWORD dwYpos;
	DWORD dwZpos;
	DWORD dwRpos;
	DWORD dwUpos;
	DWORD dwVpos;
	DWORD dwButtons;
	DWORD dwButtonNumber;
	DWORD dwPOV;
	DWORD dwReserved1;
	DWORD dwReserved2;
} JOYINFOEX,*PJOYINFOEX,*LPJOYINFOEX;
typedef DWORD FOURCC;
typedef char *HPSTR;
DECLARE_HANDLE(HMMIO);
typedef LRESULT (CALLBACK MMIOPROC)(LPSTR,UINT,LPARAM,LPARAM);
typedef MMIOPROC *LPMMIOPROC;
typedef struct _MMIOINFO {
	DWORD dwFlags;
	FOURCC fccIOProc;
	LPMMIOPROC pIOProc;
	UINT wErrorRet;
	HTASK htask;
	LONG cchBuffer;
	HPSTR pchBuffer;
	HPSTR pchNext;
	HPSTR pchEndRead;
	HPSTR pchEndWrite;
	LONG lBufOffset;
	LONG lDiskOffset;
	DWORD adwInfo[3];
	DWORD dwReserved1;
	DWORD dwReserved2;
	HMMIO hmmio;
} MMIOINFO,*PMMIOINFO,*LPMMIOINFO;
typedef const MMIOINFO *LPCMMIOINFO;
typedef struct _MMCKINFO {
	FOURCC ckid;
	DWORD cksize;
	FOURCC fccType;
	DWORD dwDataOffset;
	DWORD dwFlags;
} MMCKINFO,*PMMCKINFO,*LPMMCKINFO;
typedef const MMCKINFO *LPCMMCKINFO;
typedef struct tagMCI_GENERIC_PARMS {
	DWORD dwCallback;
} MCI_GENERIC_PARMS,*PMCI_GENERIC_PARMS,*LPMCI_GENERIC_PARMS;
typedef struct tagMCI_OPEN_PARMSA {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCSTR lpstrDeviceType;
	LPCSTR lpstrElementName;
	LPCSTR lpstrAlias;
} MCI_OPEN_PARMSA,*PMCI_OPEN_PARMSA,*LPMCI_OPEN_PARMSA;
typedef struct tagMCI_OPEN_PARMSW {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCWSTR lpstrDeviceType;
	LPCWSTR lpstrElementName;
	LPCWSTR lpstrAlias;
} MCI_OPEN_PARMSW,*PMCI_OPEN_PARMSW,*LPMCI_OPEN_PARMSW;
typedef struct tagMCI_PLAY_PARMS {
	DWORD dwCallback;
	DWORD dwFrom;
	DWORD dwTo;
} MCI_PLAY_PARMS,*PMCI_PLAY_PARMS,*LPMCI_PLAY_PARMS;
typedef struct tagMCI_SEEK_PARMS {
	DWORD dwCallback;
	DWORD dwTo;
} MCI_SEEK_PARMS, *PMCI_SEEK_PARMS,*LPMCI_SEEK_PARMS;
typedef struct tagMCI_STATUS_PARMS {
	DWORD dwCallback;
	DWORD dwReturn;
	DWORD dwItem;
	DWORD dwTrack;
} MCI_STATUS_PARMS,*PMCI_STATUS_PARMS,*LPMCI_STATUS_PARMS;
typedef struct tagMCI_INFO_PARMSA {
	DWORD dwCallback;
	LPSTR lpstrReturn;
	DWORD dwRetSize;
} MCI_INFO_PARMSA,*LPMCI_INFO_PARMSA;
typedef struct tagMCI_INFO_PARMSW {
	DWORD dwCallback;
	LPWSTR lpstrReturn;
	DWORD dwRetSize;
} MCI_INFO_PARMSW,*LPMCI_INFO_PARMSW;
typedef struct tagMCI_GETDEVCAPS_PARMS {
	DWORD dwCallback;
	DWORD dwReturn;
	DWORD dwItem;
} MCI_GETDEVCAPS_PARMS,*PMCI_GETDEVCAPS_PARMS,*LPMCI_GETDEVCAPS_PARMS;
typedef struct tagMCI_SYSINFO_PARMSA {
	DWORD dwCallback;
	LPSTR lpstrReturn;
	DWORD dwRetSize;
	DWORD dwNumber;
	UINT wDeviceType;
} MCI_SYSINFO_PARMSA,*PMCI_SYSINFO_PARMSA,*LPMCI_SYSINFO_PARMSA;
typedef struct tagMCI_SYSINFO_PARMSW {
	DWORD dwCallback;
	LPWSTR lpstrReturn;
	DWORD dwRetSize;
	DWORD dwNumber;
	UINT wDeviceType;
} MCI_SYSINFO_PARMSW,*PMCI_SYSINFO_PARMSW,*LPMCI_SYSINFO_PARMSW;
typedef struct tagMCI_SET_PARMS {
	DWORD dwCallback;
	DWORD dwTimeFormat;
	DWORD dwAudio;
} MCI_SET_PARMS,*PMCI_SET_PARMS,*LPMCI_SET_PARMS;
typedef struct tagMCI_BREAK_PARMS {
	DWORD dwCallback;
	int nVirtKey;
	HWND hwndBreak;
} MCI_BREAK_PARMS,*PMCI_BREAK_PARMS,*LPMCI_BREAK_PARMS;
typedef struct tagMCI_SAVE_PARMSA {
	DWORD dwCallback;
	LPCSTR lpfilename;
} MCI_SAVE_PARMSA,*PMCI_SAVE_PARMSA,*LPMCI_SAVE_PARMSA;
typedef struct tagMCI_SAVE_PARMSW {
	DWORD dwCallback;
	LPCWSTR lpfilename;
} MCI_SAVE_PARMSW,*PMCI_SAVE_PARMSW,*LPMCI_SAVE_PARMSW;
typedef struct tagMCI_LOAD_PARMSA {
	DWORD dwCallback;
	LPCSTR lpfilename;
} MCI_LOAD_PARMSA,*PMCI_LOAD_PARMSA,*LPMCI_LOAD_PARMSA;
typedef struct tagMCI_LOAD_PARMSW {
	DWORD dwCallback;
	LPCWSTR lpfilename;
} MCI_LOAD_PARMSW,*PMCI_LOAD_PARMSW,*LPMCI_LOAD_PARMSW;
typedef struct tagMCI_RECORD_PARMS {
	DWORD dwCallback;
	DWORD dwFrom;
	DWORD dwTo;
} MCI_RECORD_PARMS,*LPMCI_RECORD_PARMS;
typedef struct tagMCI_VD_PLAY_PARMS {
	DWORD dwCallback;
	DWORD dwFrom;
	DWORD dwTo;
	DWORD dwSpeed;
} MCI_VD_PLAY_PARMS,*PMCI_VD_PLAY_PARMS,*LPMCI_VD_PLAY_PARMS;
typedef struct tagMCI_VD_STEP_PARMS {
	DWORD dwCallback;
	DWORD dwFrames;
} MCI_VD_STEP_PARMS,*PMCI_VD_STEP_PARMS,*LPMCI_VD_STEP_PARMS;
typedef struct tagMCI_VD_ESCAPE_PARMSA {
	DWORD dwCallback;
	LPCSTR lpstrCommand;
} MCI_VD_ESCAPE_PARMSA,*PMCI_VD_ESCAPE_PARMSA,*LPMCI_VD_ESCAPE_PARMSA;
typedef struct tagMCI_VD_ESCAPE_PARMSW {
	DWORD dwCallback;
	LPCWSTR lpstrCommand;
} MCI_VD_ESCAPE_PARMSW,*PMCI_VD_ESCAPE_PARMSW,*LPMCI_VD_ESCAPE_PARMSW;
typedef struct tagMCI_WAVE_OPEN_PARMSA {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCSTR lpstrDeviceType;
	LPCSTR lpstrElementName;
	LPCSTR lpstrAlias;
	DWORD dwBufferSeconds;
} MCI_WAVE_OPEN_PARMSA,*PMCI_WAVE_OPEN_PARMSA,*LPMCI_WAVE_OPEN_PARMSA;
typedef struct tagMCI_WAVE_OPEN_PARMSW {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCWSTR lpstrDeviceType;
	LPCWSTR lpstrElementName;
	LPCWSTR lpstrAlias;
	DWORD dwBufferSeconds;
} MCI_WAVE_OPEN_PARMSW,*PMCI_WAVE_OPEN_PARMSW,*LPMCI_WAVE_OPEN_PARMSW;
typedef struct tagMCI_WAVE_DELETE_PARMS {
	DWORD dwCallback;
	DWORD dwFrom;
	DWORD dwTo;
} MCI_WAVE_DELETE_PARMS, *PMCI_WAVE_DELETE_PARMS,*LPMCI_WAVE_DELETE_PARMS;
typedef struct tagMCI_WAVE_SET_PARMS {
	DWORD dwCallback;
	DWORD dwTimeFormat;
	DWORD dwAudio;
	UINT wInput;
	UINT wOutput;
	WORD wFormatTag;
	WORD wReserved2;
	WORD nChannels;
	WORD wReserved3;
	DWORD nSamplesPerSec;
	DWORD nAvgBytesPerSec;
	WORD nBlockAlign;
	WORD wReserved4;
	WORD wBitsPerSample;
	WORD wReserved5;
} MCI_WAVE_SET_PARMS,*PMCI_WAVE_SET_PARMS,*LPMCI_WAVE_SET_PARMS;

LRESULT WINAPI CloseDriver(HDRVR,LONG,LONG);
HDRVR WINAPI OpenDriver(LPCWSTR,LPCWSTR,LONG);
LRESULT WINAPI SendDriverMessage(HDRVR,UINT,LONG,LONG);
HMODULE WINAPI DrvGetModuleHandle(HDRVR);
HMODULE WINAPI GetDriverModuleHandle(HDRVR);
LRESULT WINAPI DefDriverProc(DWORD,HDRVR,UINT,LPARAM,LPARAM);
UINT WINAPI mmsystemGetVersion(void);
#define OutputDebugStr OutputDebugString
BOOL WINAPI sndPlaySoundA(LPCSTR,UINT);
BOOL WINAPI sndPlaySoundW(LPCWSTR,UINT);
BOOL WINAPI PlaySoundA(LPCSTR,HMODULE,DWORD);
BOOL WINAPI PlaySoundW(LPCWSTR,HMODULE,DWORD);
UINT WINAPI waveOutGetNumDevs(void);
MMRESULT WINAPI waveOutGetDevCapsA(UINT,LPWAVEOUTCAPSA,UINT);
MMRESULT WINAPI waveOutGetDevCapsW(UINT,LPWAVEOUTCAPSW,UINT);
MMRESULT WINAPI waveOutGetVolume(HWAVEOUT,PDWORD);
MMRESULT WINAPI waveOutSetVolume(HWAVEOUT,DWORD);
MMRESULT WINAPI waveOutGetErrorTextA(MMRESULT,LPSTR,UINT);
MMRESULT WINAPI waveOutGetErrorTextW(MMRESULT,LPWSTR,UINT);
MMRESULT WINAPI waveOutOpen(LPHWAVEOUT,UINT,LPCWAVEFORMATEX,DWORD,DWORD,DWORD);
MMRESULT WINAPI waveOutClose(HWAVEOUT);
MMRESULT WINAPI waveOutPrepareHeader(HWAVEOUT,LPWAVEHDR,UINT);
MMRESULT WINAPI waveOutUnprepareHeader(HWAVEOUT,LPWAVEHDR,UINT);
MMRESULT WINAPI waveOutWrite(HWAVEOUT,LPWAVEHDR,UINT);
MMRESULT WINAPI waveOutPause(HWAVEOUT);
MMRESULT WINAPI waveOutRestart(HWAVEOUT);
MMRESULT WINAPI waveOutReset(HWAVEOUT);
MMRESULT WINAPI waveOutBreakLoop(HWAVEOUT);
MMRESULT WINAPI waveOutGetPosition(HWAVEOUT,LPMMTIME,UINT);
MMRESULT WINAPI waveOutGetPitch(HWAVEOUT,PDWORD);
MMRESULT WINAPI waveOutSetPitch(HWAVEOUT,DWORD);
MMRESULT WINAPI waveOutGetPlaybackRate(HWAVEOUT,PDWORD);
MMRESULT WINAPI waveOutSetPlaybackRate(HWAVEOUT,DWORD);
MMRESULT WINAPI waveOutGetID(HWAVEOUT,LPUINT);
MMRESULT WINAPI waveOutMessage(HWAVEOUT,UINT,DWORD,DWORD);
UINT WINAPI waveInGetNumDevs(void);
MMRESULT WINAPI waveInGetDevCapsA(UINT,LPWAVEINCAPSA,UINT);
MMRESULT WINAPI waveInGetDevCapsW(UINT,LPWAVEINCAPSW,UINT);
MMRESULT WINAPI waveInGetErrorTextA(MMRESULT,LPSTR,UINT);
MMRESULT WINAPI waveInGetErrorTextW(MMRESULT,LPWSTR,UINT);
MMRESULT WINAPI waveInOpen(LPHWAVEIN,UINT,LPCWAVEFORMATEX,DWORD,DWORD,DWORD);
MMRESULT WINAPI waveInClose(HWAVEIN);
MMRESULT WINAPI waveInPrepareHeader(HWAVEIN,LPWAVEHDR,UINT);
MMRESULT WINAPI waveInUnprepareHeader(HWAVEIN,LPWAVEHDR,UINT);
MMRESULT WINAPI waveInAddBuffer(HWAVEIN,LPWAVEHDR,UINT);
MMRESULT WINAPI waveInStart(HWAVEIN);
MMRESULT WINAPI waveInStop(HWAVEIN);
MMRESULT WINAPI waveInReset(HWAVEIN);
MMRESULT WINAPI waveInGetPosition(HWAVEIN,LPMMTIME,UINT);
MMRESULT WINAPI waveInGetID(HWAVEIN,LPUINT);
MMRESULT WINAPI waveInMessage(HWAVEIN,UINT,DWORD,DWORD);
UINT WINAPI midiOutGetNumDevs(void);
MMRESULT WINAPI midiStreamOpen(LPHMIDISTRM,LPUINT,DWORD,DWORD,DWORD,DWORD);
MMRESULT WINAPI midiStreamClose(HMIDISTRM);
MMRESULT WINAPI midiStreamProperty(HMIDISTRM,LPBYTE,DWORD);
MMRESULT WINAPI midiStreamPosition(HMIDISTRM,LPMMTIME,UINT);
MMRESULT WINAPI midiStreamOut(HMIDISTRM,LPMIDIHDR,UINT);
MMRESULT WINAPI midiStreamPause(HMIDISTRM);
MMRESULT WINAPI midiStreamRestart(HMIDISTRM);
MMRESULT WINAPI midiStreamStop(HMIDISTRM);
MMRESULT WINAPI midiConnect(HMIDI,HMIDIOUT,PVOID);
MMRESULT WINAPI midiDisconnect(HMIDI,HMIDIOUT,PVOID);
MMRESULT WINAPI midiOutGetDevCapsA(UINT,LPMIDIOUTCAPSA,UINT);
MMRESULT WINAPI midiOutGetDevCapsW(UINT,LPMIDIOUTCAPSW,UINT);
MMRESULT WINAPI midiOutGetVolume(HMIDIOUT,PDWORD);
MMRESULT WINAPI midiOutSetVolume(HMIDIOUT,DWORD);
MMRESULT WINAPI midiOutGetErrorTextA(MMRESULT,LPSTR,UINT);
MMRESULT WINAPI midiOutGetErrorTextW(MMRESULT,LPWSTR,UINT);
MMRESULT WINAPI midiOutOpen(LPHMIDIOUT,UINT,DWORD,DWORD,DWORD);
MMRESULT WINAPI midiOutClose(HMIDIOUT);
MMRESULT WINAPI midiOutPrepareHeader(HMIDIOUT,LPMIDIHDR,UINT);
MMRESULT WINAPI midiOutUnprepareHeader(HMIDIOUT,LPMIDIHDR,UINT);
MMRESULT WINAPI midiOutShortMsg(HMIDIOUT,DWORD);
MMRESULT WINAPI midiOutLongMsg(HMIDIOUT,LPMIDIHDR,UINT);
MMRESULT WINAPI midiOutReset(HMIDIOUT);
MMRESULT WINAPI midiOutCachePatches(HMIDIOUT,UINT,LPWORD,UINT);
MMRESULT WINAPI midiOutCacheDrumPatches(HMIDIOUT,UINT,LPWORD,UINT);
MMRESULT WINAPI midiOutGetID(HMIDIOUT,LPUINT);
MMRESULT WINAPI midiOutMessage(HMIDIOUT,UINT,DWORD,DWORD);
UINT WINAPI midiInGetNumDevs(void);
MMRESULT WINAPI midiInGetDevCapsA(UINT,LPMIDIINCAPSA,UINT);
MMRESULT WINAPI midiInGetDevCapsW(UINT,LPMIDIINCAPSW,UINT);
MMRESULT WINAPI midiInGetErrorTextA(MMRESULT,LPSTR,UINT);
MMRESULT WINAPI midiInGetErrorTextW(MMRESULT,LPWSTR,UINT);
MMRESULT WINAPI midiInOpen(LPHMIDIIN,UINT,DWORD,DWORD,DWORD);
MMRESULT WINAPI midiInClose(HMIDIIN);
MMRESULT WINAPI midiInPrepareHeader(HMIDIIN,LPMIDIHDR,UINT);
MMRESULT WINAPI midiInUnprepareHeader(HMIDIIN,LPMIDIHDR,UINT);
MMRESULT WINAPI midiInAddBuffer(HMIDIIN,LPMIDIHDR,UINT);
MMRESULT WINAPI midiInStart(HMIDIIN);
MMRESULT WINAPI midiInStop(HMIDIIN);
MMRESULT WINAPI midiInReset(HMIDIIN);
MMRESULT WINAPI midiInGetID(HMIDIIN,LPUINT);
MMRESULT WINAPI midiInMessage(HMIDIIN,UINT,DWORD,DWORD);
UINT WINAPI auxGetNumDevs(void);
MMRESULT WINAPI auxGetDevCapsA(UINT,LPAUXCAPSA,UINT);
MMRESULT WINAPI auxGetDevCapsW(UINT,LPAUXCAPSW,UINT);
MMRESULT WINAPI auxSetVolume(UINT,DWORD);
MMRESULT WINAPI auxGetVolume(UINT,PDWORD);
MMRESULT WINAPI auxOutMessage(UINT,UINT,DWORD,DWORD);
UINT WINAPI mixerGetNumDevs(void);
MMRESULT WINAPI mixerGetDevCapsA(UINT,LPMIXERCAPSA,UINT);
MMRESULT WINAPI mixerGetDevCapsW(UINT,LPMIXERCAPSW,UINT);
MMRESULT WINAPI mixerOpen(LPHMIXER,UINT,DWORD,DWORD,DWORD);
MMRESULT WINAPI mixerClose(HMIXER);
DWORD WINAPI mixerMessage(HMIXER,UINT,DWORD,DWORD);
MMRESULT WINAPI mixerGetLineInfoA(HMIXEROBJ,LPMIXERLINEA,DWORD);
MMRESULT WINAPI mixerGetLineInfoW(HMIXEROBJ,LPMIXERLINEW,DWORD);
MMRESULT WINAPI mixerGetID(HMIXEROBJ,PUINT,DWORD);
MMRESULT WINAPI mixerGetLineControlsA(HMIXEROBJ,LPMIXERLINECONTROLSA,DWORD);
MMRESULT WINAPI mixerGetLineControlsW(HMIXEROBJ,LPMIXERLINECONTROLSW,DWORD);
MMRESULT WINAPI mixerGetControlDetailsA(HMIXEROBJ,LPMIXERCONTROLDETAILS,DWORD);
MMRESULT WINAPI mixerGetControlDetailsW(HMIXEROBJ,LPMIXERCONTROLDETAILS,DWORD);
MMRESULT WINAPI mixerSetControlDetails(HMIXEROBJ,LPMIXERCONTROLDETAILS,DWORD);
MMRESULT WINAPI timeGetSystemTime(LPMMTIME,UINT);
DWORD WINAPI timeGetTime(void);
MMRESULT WINAPI timeSetEvent(UINT,UINT,LPTIMECALLBACK,DWORD,UINT);
MMRESULT WINAPI timeKillEvent(UINT);
MMRESULT WINAPI timeGetDevCaps(LPTIMECAPS,UINT);
MMRESULT WINAPI timeBeginPeriod(UINT);
MMRESULT WINAPI timeEndPeriod(UINT);
UINT WINAPI joyGetNumDevs(void);
MMRESULT WINAPI joyGetDevCapsA(UINT,LPJOYCAPSA,UINT);
MMRESULT WINAPI joyGetDevCapsW(UINT,LPJOYCAPSW,UINT);
MMRESULT WINAPI joyGetPos(UINT,LPJOYINFO);
MMRESULT WINAPI joyGetPosEx(UINT,LPJOYINFOEX);
MMRESULT WINAPI joyGetThreshold(UINT,LPUINT);
MMRESULT WINAPI joyReleaseCapture(UINT);
MMRESULT WINAPI joySetCapture(HWND,UINT,UINT,BOOL);
MMRESULT WINAPI joySetThreshold(UINT,UINT);
FOURCC WINAPI mmioStringToFOURCCA(LPCSTR,UINT);
FOURCC WINAPI mmioStringToFOURCCW(LPCWSTR,UINT);
LPMMIOPROC WINAPI mmioInstallIOProcA(FOURCC,LPMMIOPROC,DWORD);
LPMMIOPROC WINAPI mmioInstallIOProcW(FOURCC,LPMMIOPROC,DWORD);
HMMIO WINAPI mmioOpenA(LPSTR,LPMMIOINFO,DWORD);
HMMIO WINAPI mmioOpenW(LPWSTR,LPMMIOINFO,DWORD);
MMRESULT WINAPI mmioRenameA(LPCSTR,LPCSTR,LPCMMIOINFO,DWORD);
MMRESULT WINAPI mmioRenameW(LPCWSTR,LPCWSTR,LPCMMIOINFO,DWORD);
MMRESULT WINAPI mmioClose(HMMIO,UINT);
LONG WINAPI mmioRead(HMMIO,HPSTR,LONG);
LONG WINAPI mmioWrite(HMMIO,LPCSTR,LONG);
LONG WINAPI mmioSeek(HMMIO,LONG,int);
MMRESULT WINAPI mmioGetInfo(HMMIO,LPMMIOINFO,UINT);
MMRESULT WINAPI mmioSetInfo(HMMIO,LPCMMIOINFO,UINT);
MMRESULT WINAPI mmioSetBuffer(HMMIO,LPSTR,LONG,UINT);
MMRESULT WINAPI mmioFlush(HMMIO,UINT);
MMRESULT WINAPI mmioAdvance(HMMIO,LPMMIOINFO,UINT);
LRESULT WINAPI mmioSendMessage(HMMIO,UINT,LPARAM,LPARAM);
MMRESULT WINAPI mmioDescend(HMMIO,LPMMCKINFO,const MMCKINFO*,UINT);
MMRESULT WINAPI mmioAscend(HMMIO,LPMMCKINFO,UINT);
MMRESULT WINAPI mmioCreateChunk(HMMIO,LPMMCKINFO,UINT);
MCIERROR WINAPI mciSendCommandA(MCIDEVICEID,UINT,DWORD,DWORD);
MCIERROR WINAPI mciSendCommandW(MCIDEVICEID,UINT,DWORD,DWORD);
MCIERROR WINAPI mciSendStringA(LPCSTR,LPSTR,UINT,HWND);
MCIERROR WINAPI mciSendStringW(LPCWSTR,LPWSTR,UINT,HWND);
MCIDEVICEID WINAPI mciGetDeviceIDA(LPCSTR);
MCIDEVICEID WINAPI mciGetDeviceIDW(LPCWSTR);
MCIDEVICEID WINAPI mciGetDeviceIDFromElementIDA(DWORD,LPCSTR);
MCIDEVICEID WINAPI mciGetDeviceIDFromElementIDW(DWORD,LPCWSTR);
BOOL WINAPI mciGetErrorStringA(MCIERROR,LPSTR,UINT);
BOOL WINAPI mciGetErrorStringW(MCIERROR,LPWSTR,UINT);
BOOL WINAPI mciSetYieldProc(MCIDEVICEID,YIELDPROC,DWORD);
HTASK WINAPI mciGetCreatorTask(MCIDEVICEID);
YIELDPROC WINAPI mciGetYieldProc(MCIDEVICEID,PDWORD);

typedef struct tagMCI_SEQ_SET_PARMS {
	DWORD dwCallback;
	DWORD dwTimeFormat;
	DWORD dwAudio;
	DWORD dwTempo;
	DWORD dwPort;
	DWORD dwSlave;
	DWORD dwMaster;
	DWORD dwOffset;
} MCI_SEQ_SET_PARMS,*PMCI_SEQ_SET_PARMS,*LPMCI_SEQ_SET_PARMS;
typedef struct tagMCI_ANIM_OPEN_PARMSA {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCSTR lpstrDeviceType;
	LPCSTR lpstrElementName;
	LPCSTR lpstrAlias;
	DWORD dwStyle;
	HWND hWndParent;
} MCI_ANIM_OPEN_PARMSA,*PMCI_ANIM_OPEN_PARMSA,*LPMCI_ANIM_OPEN_PARMSA;
typedef struct tagMCI_ANIM_OPEN_PARMSW {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCWSTR lpstrDeviceType;
	LPCWSTR lpstrElementName;
	LPCWSTR lpstrAlias;
	DWORD dwStyle;
	HWND hWndParent;
} MCI_ANIM_OPEN_PARMSW,*PMCI_ANIM_OPEN_PARMSW,*LPMCI_ANIM_OPEN_PARMSW;
typedef struct tagMCI_ANIM_PLAY_PARMS {
	DWORD dwCallback;
	DWORD dwFrom;
	DWORD dwTo;
	DWORD dwSpeed;
} MCI_ANIM_PLAY_PARMS,*PMCI_ANIM_PLAY_PARMS,*LPMCI_ANIM_PLAY_PARMS;
typedef struct tagMCI_ANIM_STEP_PARMS {
	DWORD dwCallback;
	DWORD dwFrames;
} MCI_ANIM_STEP_PARMS,*PMCI_ANIM_STEP_PARMS,*LPMCI_ANIM_STEP_PARMS;
typedef struct tagMCI_ANIM_WINDOW_PARMSA {
	DWORD dwCallback;
	HWND hWnd;
	UINT nCmdShow;
	LPCSTR lpstrText;
} MCI_ANIM_WINDOW_PARMSA,*PMCI_ANIM_WINDOW_PARMSA,*LPMCI_ANIM_WINDOW_PARMSA;
typedef struct tagMCI_ANIM_WINDOW_PARMSW {
	DWORD dwCallback;
	HWND hWnd;
	UINT nCmdShow;
	LPCWSTR lpstrText;
} MCI_ANIM_WINDOW_PARMSW,*PMCI_ANIM_WINDOW_PARMSW,*LPMCI_ANIM_WINDOW_PARMSW;
typedef struct tagMCI_ANIM_RECT_PARMS {
	DWORD dwCallback;
#ifdef MCI_USE_OFFEXT
	POINT ptOffset;
	POINT ptExtent;
#else
	RECT rc;
#endif
} MCI_ANIM_RECT_PARMS,*PMCI_ANIM_RECT_PARMS,*LPMCI_ANIM_RECT_PARMS;
typedef struct tagMCI_ANIM_UPDATE_PARMS {
	DWORD dwCallback;
	RECT rc;
	HDC hDC;
} MCI_ANIM_UPDATE_PARMS,*PMCI_ANIM_UPDATE_PARMS,*LPMCI_ANIM_UPDATE_PARMS;
typedef struct tagMCI_OVLY_OPEN_PARMSA {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCSTR lpstrDeviceType;
	LPCSTR lpstrElementName;
	LPCSTR lpstrAlias;
	DWORD dwStyle;
	HWND hWndParent;
} MCI_OVLY_OPEN_PARMSA,*PMCI_OVLY_OPEN_PARMSA,*LPMCI_OVLY_OPEN_PARMSA;
typedef struct tagMCI_OVLY_OPEN_PARMSW {
	DWORD dwCallback;
	MCIDEVICEID wDeviceID;
	LPCWSTR lpstrDeviceType;
	LPCWSTR lpstrElementName;
	LPCWSTR lpstrAlias;
	DWORD dwStyle;
	HWND hWndParent;
} MCI_OVLY_OPEN_PARMSW,*PMCI_OVLY_OPEN_PARMSW,*LPMCI_OVLY_OPEN_PARMSW;
typedef struct tagMCI_OVLY_WINDOW_PARMSA {
	DWORD dwCallback;
	HWND hWnd;
	UINT nCmdShow;
	LPCSTR lpstrText;
} MCI_OVLY_WINDOW_PARMSA,*PMCI_OVLY_WINDOW_PARMSA,*LPMCI_OVLY_WINDOW_PARMSA;
typedef struct tagMCI_OVLY_WINDOW_PARMSW {
	DWORD dwCallback;
	HWND hWnd;
	UINT nCmdShow;
	LPCWSTR lpstrText;
} MCI_OVLY_WINDOW_PARMSW,*PMCI_OVLY_WINDOW_PARMSW,*LPMCI_OVLY_WINDOW_PARMSW;
typedef struct tagMCI_OVLY_RECT_PARMS {
	DWORD dwCallback;
#ifdef MCI_USE_OFFEXT
	POINT ptOffset;
	POINT ptExtent;
#else
	RECT rc;
#endif
} MCI_OVLY_RECT_PARMS,*PMCI_OVLY_RECT_PARMS,*LPMCI_OVLY_RECT_PARMS;
typedef struct tagMCI_OVLY_SAVE_PARMSA {
	DWORD dwCallback;
	LPCSTR lpfilename;
	RECT rc;
} MCI_OVLY_SAVE_PARMSA,*PMCI_OVLY_SAVE_PARMSA,*LPMCI_OVLY_SAVE_PARMSA;
typedef struct tagMCI_OVLY_SAVE_PARMSW {
	DWORD dwCallback;
	LPCWSTR lpfilename;
	RECT rc;
} MCI_OVLY_SAVE_PARMSW,*PMCI_OVLY_SAVE_PARMSW,*LPMCI_OVLY_SAVE_PARMSW;
typedef struct tagMCI_OVLY_LOAD_PARMSA {
	DWORD dwCallback;
	LPCSTR lpfilename;
	RECT rc;
} MCI_OVLY_LOAD_PARMSA,*PMCI_OVLY_LOAD_PARMSA,*LPMCI_OVLY_LOAD_PARMSA;
typedef struct tagMCI_OVLY_LOAD_PARMSW {
	DWORD dwCallback;
	LPCWSTR lpfilename;
	RECT rc;
} MCI_OVLY_LOAD_PARMSW,*PMCI_OVLY_LOAD_PARMSW,*LPMCI_OVLY_LOAD_PARMSW;

#ifdef UNICODE
typedef WAVEOUTCAPSW WAVEOUTCAPS,*PWAVEOUTCAPS,*LPWAVEOUTCAPS;
typedef WAVEINCAPSW WAVEINCAPS,*PWAVEINCAPS,*LPWAVEINCAPS;
typedef MIDIOUTCAPSW MIDIOUTCAPS,*PMIDIOUTCAPS,*LPMIDIOUTCAPS;
typedef MIDIINCAPSW MIDIINCAPS,*PMIDIINCAPS,*LPMIDIINCAPS;
typedef AUXCAPSW AUXCAPS,*PAUXCAPS,*LPAUXCAPS;
typedef MIXERCAPSW MIXERCAPS,*PMIXERCAPS,*LPMIXERCAPS;
typedef MIXERLINEW MIXERLINE,*PMIXERLINE,*LPMIXERLINE;
typedef MIXERCONTROLA MIXERCONTROL,*PMIXERCONTROL,*LPMIXERCONTROL;
typedef MIXERLINECONTROLSW MIXERLINECONTROLS,*PMIXERLINECONTROLS,*LPMIXERLINECONTROLS;
typedef MIXERCONTROLDETAILS_LISTTEXTW MIXERCONTROLDETAILS_LISTTEXT,*PMIXERCONTROLDETAILS_LISTTEXT,*LPMIXERCONTROLDETAILS_LISTTEXT;
typedef JOYCAPSW JOYCAPS,*PJOYCAPS,*LPJOYCAPS;
typedef MCI_OPEN_PARMSW MCI_OPEN_PARMS,*PMCI_OPEN_PARMS,*LPMCI_OPEN_PARMS;
typedef MCI_INFO_PARMSW MCI_INFO_PARMS,*LPMCI_INFO_PARMS;
typedef MCI_SYSINFO_PARMSW MCI_SYSINFO_PARMS,*PMCI_SYSINFO_PARMS,*LPMCI_SYSINFO_PARMS;
typedef MCI_SAVE_PARMSW MCI_SAVE_PARMS,*PMCI_SAVE_PARMS,*LPMCI_SAVE_PARMS;
typedef MCI_LOAD_PARMSW MCI_LOAD_PARMS,*PMCI_LOAD_PARMS,*LPMCI_LOAD_PARMS;
typedef MCI_VD_ESCAPE_PARMSW MCI_VD_ESCAPE_PARMS,*PMCI_VD_ESCAPE_PARMS,*LPMCI_VD_ESCAPE_PARMS;
typedef MCI_WAVE_OPEN_PARMSW MCI_WAVE_OPEN_PARMS,*PMCI_WAVE_OPEN_PARMS,*LPMCI_WAVE_OPEN_PARMS;
typedef MCI_ANIM_OPEN_PARMSW MCI_ANIM_OPEN_PARMS,*PMCI_ANIM_OPEN_PARMS,*LPMCI_ANIM_OPEN_PARMS;
typedef MCI_ANIM_WINDOW_PARMSW MCI_ANIM_WINDOW_PARMS,*PMCI_ANIM_WINDOW_PARMS,*LPMCI_ANIM_WINDOW_PARMS;
typedef MCI_OVLY_OPEN_PARMSW MCI_OVLY_OPEN_PARMS,*PMCI_OVLY_OPEN_PARMS,*LPMCI_OVLY_OPEN_PARMS;
typedef MCI_OVLY_WINDOW_PARMSW MCI_OVLY_WINDOW_PARMS,*PMCI_OVLY_WINDOW_PARMS,*LPMCI_OVLY_WINDOW_PARMS;
typedef MCI_OVLY_SAVE_PARMSW MCI_OVLY_SAVE_PARMS,*PMCI_OVLY_SAVE_PARMS,*LPMCI_OVLY_SAVE_PARMS;
#define sndPlaySound sndPlaySoundW
#define PlaySound PlaySoundW
#define waveOutGetDevCaps waveOutGetDevCapsW
#define waveOutGetErrorText waveOutGetErrorTextW
#define waveInGetDevCaps waveInGetDevCapsW
#define waveInGetErrorText waveInGetErrorTextW
#define midiOutGetDevCaps midiOutGetDevCapsW
#define midiOutGetErrorText midiOutGetErrorTextW
#define midiInGetDevCaps midiInGetDevCapsW
#define midiInGetErrorText midiInGetErrorTextW
#define auxGetDevCaps auxGetDevCapsW
#define mixerGetDevCaps mixerGetDevCapsW
#define mixerGetLineInfo mixerGetLineInfoW
#define mixerGetLineControls mixerGetLineControlsW
#define mixerGetControlDetails mixerGetControlDetailsW
#define joyGetDevCaps joyGetDevCapsW
#define mmioInstallIOProc mmioInstallIOProcW
#define mmioStringToFOURCC mmioStringToFOURCCW
#define mmioOpen mmioOpenW
#define mmioRename mmioRenameW
#define mciSendCommand mciSendCommandW
#define mciSendString mciSendStringW
#define mciGetDeviceID mciGetDeviceIDW
#define mciGetDeviceIDFromElementID mciGetDeviceIDFromElementIDW
#define mciGetErrorString mciGetErrorStringW
#else
typedef WAVEOUTCAPSA WAVEOUTCAPS,*PWAVEOUTCAPS,*LPWAVEOUTCAPS;
typedef WAVEINCAPSA WAVEINCAPS,*PWAVEINCAPS,*LPWAVEINCAPS;
typedef MIDIOUTCAPSA MIDIOUTCAPS,*PMIDIOUTCAPS,*LPMIDIOUTCAPS;
typedef MIDIINCAPSA MIDIINCAPS,*PMIDIINCAPS,*LPMIDIINCAPS;
typedef AUXCAPSA AUXCAPS,*PAUXCAPS,*LPAUXCAPS;
typedef MIXERCAPSA MIXERCAPS,*PMIXERCAPS,*LPMIXERCAPS;
typedef MIXERLINEA MIXERLINE,*PMIXERLINE,*LPMIXERLINE;
typedef MIXERCONTROLA MIXERCONTROL,*PMIXERCONTROL,*LPMIXERCONTROL;
typedef MIXERLINECONTROLSA MIXERLINECONTROLS,*PMIXERLINECONTROLS,*LPMIXERLINECONTROLS;
typedef MIXERCONTROLDETAILS_LISTTEXTA MIXERCONTROLDETAILS_LISTTEXT,*PMIXERCONTROLDETAILS_LISTTEXT,*LPMIXERCONTROLDETAILS_LISTTEXT;
typedef JOYCAPSA JOYCAPS,*PJOYCAPS,*LPJOYCAPS;
typedef MCI_OPEN_PARMSA MCI_OPEN_PARMS,*PMCI_OPEN_PARMS,*LPMCI_OPEN_PARMS;
typedef MCI_INFO_PARMSA MCI_INFO_PARMS,*LPMCI_INFO_PARMS;
typedef MCI_SYSINFO_PARMSA MCI_SYSINFO_PARMS,*PMCI_SYSINFO_PARMS,*LPMCI_SYSINFO_PARMS;
typedef MCI_SAVE_PARMSA MCI_SAVE_PARMS,*PMCI_SAVE_PARMS,*LPMCI_SAVE_PARMS;
typedef MCI_LOAD_PARMSA MCI_LOAD_PARMS,*PMCI_LOAD_PARMS,*LPMCI_LOAD_PARMS;
typedef MCI_VD_ESCAPE_PARMSA MCI_VD_ESCAPE_PARMS,*PMCI_VD_ESCAPE_PARMS,*LPMCI_VD_ESCAPE_PARMS;
typedef MCI_WAVE_OPEN_PARMSA MCI_WAVE_OPEN_PARMS,*PMCI_WAVE_OPEN_PARMS,*LPMCI_WAVE_OPEN_PARMS;
typedef MCI_ANIM_OPEN_PARMSA MCI_ANIM_OPEN_PARMS,*PMCI_ANIM_OPEN_PARMS,*LPMCI_ANIM_OPEN_PARMS;
typedef MCI_ANIM_WINDOW_PARMSA MCI_ANIM_WINDOW_PARMS,*PMCI_ANIM_WINDOW_PARMS,*LPMCI_ANIM_WINDOW_PARMS;
typedef MCI_OVLY_OPEN_PARMSA MCI_OVLY_OPEN_PARMS,*PMCI_OVLY_OPEN_PARMS,*LPMCI_OVLY_OPEN_PARMS;
typedef MCI_OVLY_WINDOW_PARMSA MCI_OVLY_WINDOW_PARMS,*PMCI_OVLY_WINDOW_PARMS,*LPMCI_OVLY_WINDOW_PARMS;
typedef MCI_OVLY_SAVE_PARMSA MCI_OVLY_SAVE_PARMS,*PMCI_OVLY_SAVE_PARMS,*LPMCI_OVLY_SAVE_PARMS;
#define sndPlaySound sndPlaySoundA
#define PlaySound PlaySoundA
#define waveOutGetDevCaps waveOutGetDevCapsA
#define waveOutGetErrorText waveOutGetErrorTextA
#define waveInGetDevCaps waveInGetDevCapsA
#define waveInGetErrorText waveInGetErrorTextA
#define midiOutGetDevCaps midiOutGetDevCapsA
#define midiOutGetErrorText midiOutGetErrorTextA
#define midiInGetDevCaps midiInGetDevCapsA
#define midiInGetErrorText midiInGetErrorTextA
#define auxGetDevCaps auxGetDevCapsA
#define mixerGetDevCaps mixerGetDevCapsA
#define mixerGetLineInfo mixerGetLineInfoA
#define mixerGetLineControls mixerGetLineControlsA
#define mixerGetControlDetails mixerGetControlDetailsA
#define joyGetDevCaps joyGetDevCapsA
#define mmioInstallIOProc mmioInstallIOProcA
#define mmioStringToFOURCC mmioStringToFOURCCA
#define mmioOpen mmioOpenA
#define mmioRename mmioRenameA
#define mciSendCommand mciSendCommandA
#define mciSendString mciSendStringA
#define mciGetDeviceID mciGetDeviceIDA
#define mciGetDeviceIDFromElementID mciGetDeviceIDFromElementIDA
#define mciGetErrorString mciGetErrorStringA
#endif
#ifdef __cplusplus
}
#endif
#pragma pack(pop)
#endif
