#ifndef _IPIFCONS_H
#define _IPIFCONS_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#define MIB_IF_ADMIN_STATUS_UP 1
#define MIB_IF_ADMIN_STATUS_DOWN 2
#define MIB_IF_ADMIN_STATUS_TESTING 3
#define MIB_IF_OPER_STATUS_NON_OPERATIONAL 0
#define MIB_IF_OPER_STATUS_UNREACHABLE 1
#define MIB_IF_OPER_STATUS_DISCONNECTED 2
#define MIB_IF_OPER_STATUS_CONNECTING 3
#define MIB_IF_OPER_STATUS_CONNECTED 4
#define MIB_IF_OPER_STATUS_OPERATIONAL 5
#define MIB_IF_TYPE_OTHER 1
#define MIB_IF_TYPE_ETHERNET 6
#define MIB_IF_TYPE_TOKENRING 9
#define MIB_IF_TYPE_FDDI 15
#define MIB_IF_TYPE_PPP 23
#define MIB_IF_TYPE_LOOPBACK 24
#define MIB_IF_TYPE_SLIP 28
#endif /* _IPIFCONS_H */
