#ifndef _WININET_H
#define _WININET_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#include <windows.h>
#ifdef __cplusplus
extern "C" {
#endif
#define INTERNET_INVALID_PORT_NUMBER 0
#define INTERNET_DEFAULT_FTP_PORT 21
#define INTERNET_DEFAULT_GOPHER_PORT 70
#define INTERNET_DEFAULT_HTTP_PORT 80
#define INTERNET_DEFAULT_HTTPS_PORT 443
#define INTERNET_DEFAULT_SOCKS_PORT 1080
#define MAX_CACHE_ENTRY_INFO_SIZE 4096
#define INTERNET_MAX_HOST_NAME_LENGTH 256
#define INTERNET_MAX_USER_NAME_LENGTH 128
#define INTERNET_MAX_PASSWORD_LENGTH 128
#define INTERNET_MAX_PORT_NUMBER_LENGTH 5
#define INTERNET_MAX_PORT_NUMBER_VALUE 65535
#define INTERNET_MAX_PATH_LENGTH 2048
#define INTERNET_MAX_SCHEME_LENGTH 32
#define INTERNET_MAX_URL_LENGTH (INTERNET_MAX_SCHEME_LENGTH+sizeof("://")+INTERNET_MAX_PATH_LENGTH)
#define INTERNET_KEEP_ALIVE_UNKNOWN ((DWORD)-1)
#define INTERNET_KEEP_ALIVE_ENABLED 1
#define INTERNET_KEEP_ALIVE_DISABLED 0
#define INTERNET_REQFLAG_FROM_CACHE 1
#define INTERNET_REQFLAG_ASYNC 2
#define INTERNET_FLAG_RELOAD 0x80000000
#define INTERNET_FLAG_RAW_DATA 0x40000000
#define INTERNET_FLAG_EXISTING_CONNECT 0x20000000
#define INTERNET_FLAG_ASYNC 0x10000000
#define INTERNET_FLAG_PASSIVE 0x08000000
#define INTERNET_FLAG_NO_CACHE_WRITE 0x04000000
#define INTERNET_FLAG_DONT_CACHE INTERNET_FLAG_NO_CACHE_WRITE
#define INTERNET_FLAG_MAKE_PERSISTENT 0x02000000
#define INTERNET_FLAG_OFFLINE 0x1000000
#define INTERNET_FLAG_SECURE 0x800000
#define INTERNET_FLAG_KEEP_CONNECTION 0x400000
#define INTERNET_FLAG_NO_AUTO_REDIRECT 0x200000
#define INTERNET_FLAG_READ_PREFETCH 0x100000
#define INTERNET_FLAG_NO_COOKIES 0x80000
#define INTERNET_FLAG_NO_AUTH 0x40000
#define INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP 0x8000
#define INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS 0x4000
#define INTERNET_FLAG_IGNORE_CERT_DATE_INVALID 0x2000
#define INTERNET_FLAG_IGNORE_CERT_CN_INVALID 0x1000
#define INTERNET_FLAG_MUST_CACHE_REQUEST 16
#define INTERNET_FLAG_RESYNCHRONIZE 0x800
#define INTERNET_FLAG_HYPERLINK 0x400
#define INTERNET_FLAG_NO_UI 0x200
#define INTERNET_FLAG_PRAGMA_NOCACHE 0x100
#define INTERNET_FLAG_TRANSFER_ASCII FTP_TRANSFER_TYPE_ASCII
#define INTERNET_FLAG_TRANSFER_BINARY FTP_TRANSFER_TYPE_BINARY
#define SECURITY_INTERNET_MASK (INTERNET_FLAG_IGNORE_CERT_CN_INVALID|INTERNET_FLAG_IGNORE_CERT_DATE_INVALID|INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS|INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP)
#define SECURITY_SET_MASK SECURITY_INTERNET_MASK
#define INTERNET_FLAGS_MASK (INTERNET_FLAG_RELOAD|INTERNET_FLAG_RAW_DATA|INTERNET_FLAG_EXISTING_CONNECT|\
INTERNET_FLAG_ASYNC|INTERNET_FLAG_PASSIVE|INTERNET_FLAG_NO_CACHE_WRITE|INTERNET_FLAG_MAKE_PERSISTENT|INTERNET_FLAG_OFFLINE|\
INTERNET_FLAG_SECURE|INTERNET_FLAG_KEEP_CONNECTION|INTERNET_FLAG_NO_AUTO_REDIRECT|INTERNET_FLAG_READ_PREFETCH |\
INTERNET_FLAG_NO_COOKIES|INTERNET_FLAG_NO_AUTH|SECURITY_INTERNET_MASK|INTERNET_FLAG_TRANSFER_ASCII|INTERNET_FLAG_TRANSFER_BINARY\
|INTERNET_FLAG_RESYNCHRONIZE|INTERNET_FLAG_MUST_CACHE_REQUEST|INTERNET_FLAG_HYPERLINK|INTERNET_FLAG_NO_UI)
#define INTERNET_OPTIONS_MASK (~INTERNET_FLAGS_MASK)
#define INTERNET_NO_CALLBACK 0
#define INTERNET_RFC1123_FORMAT 0
#define INTERNET_RFC1123_BUFSIZE 30
#define ICU_ESCAPE 0x80000000
#define ICU_USERNAME 0x40000000
#define ICU_NO_ENCODE 0x20000000
#define ICU_DECODE 0x10000000
#define ICU_NO_META 0x08000000
#define ICU_ENCODE_SPACES_ONLY 0x04000000
#define ICU_BROWSER_MODE 0x02000000
#define INTERNET_OPEN_TYPE_PRECONFIG 0
#define INTERNET_OPEN_TYPE_DIRECT 1
#define INTERNET_OPEN_TYPE_PROXY 3
#define PRE_CONFIG_INTERNET_ACCESS INTERNET_OPEN_TYPE_PRECONFIG
#define LOCAL_INTERNET_ACCESS INTERNET_OPEN_TYPE_DIRECT
#define GATEWAY_INTERNET_ACCESS 2
#define CERN_PROXY_INTERNET_ACCESS INTERNET_OPEN_TYPE_PROXY
#define ISO_GLOBAL 1
#define ISO_REGISTRY 2
#define ISO_VALID_FLAGS (ISO_GLOBAL | ISO_REGISTRY)
#define INTERNET_OPTION_CALLBACK 1
#define INTERNET_OPTION_CONNECT_TIMEOUT 2
#define INTERNET_OPTION_CONNECT_RETRIES 3
#define INTERNET_OPTION_CONNECT_BACKOFF 4
#define INTERNET_OPTION_SEND_TIMEOUT 5
#define INTERNET_OPTION_CONTROL_SEND_TIMEOUT INTERNET_OPTION_SEND_TIMEOUT
#define INTERNET_OPTION_RECEIVE_TIMEOUT 6
#define INTERNET_OPTION_CONTROL_RECEIVE_TIMEOUT INTERNET_OPTION_RECEIVE_TIMEOUT
#define INTERNET_OPTION_DATA_SEND_TIMEOUT 7
#define INTERNET_OPTION_DATA_RECEIVE_TIMEOUT 8
#define INTERNET_OPTION_HANDLE_TYPE 9
#define INTERNET_OPTION_CONTEXT_VALUE 10
#define INTERNET_OPTION_LISTEN_TIMEOUT 11
#define INTERNET_OPTION_READ_BUFFER_SIZE 12
#define INTERNET_OPTION_WRITE_BUFFER_SIZE 13
#define INTERNET_OPTION_ASYNC_ID 15
#define INTERNET_OPTION_ASYNC_PRIORITY 16
#define INTERNET_OPTION_PARENT_HANDLE 21
#define INTERNET_OPTION_KEEP_CONNECTION 22
#define INTERNET_OPTION_REQUEST_FLAGS 23
#define INTERNET_OPTION_EXTENDED_ERROR 24
#define INTERNET_OPTION_OFFLINE_MODE 26
#define INTERNET_OPTION_CACHE_STREAM_HANDLE 27
#define INTERNET_OPTION_USERNAME 28
#define INTERNET_OPTION_PASSWORD 29
#define INTERNET_OPTION_ASYNC 30
#define INTERNET_OPTION_SECURITY_FLAGS 31
#define INTERNET_OPTION_SECURITY_CERTIFICATE_STRUCT 32
#define INTERNET_OPTION_DATAFILE_NAME 33
#define INTERNET_OPTION_URL 34
#define INTERNET_OPTION_SECURITY_CERTIFICATE 35
#define INTERNET_OPTION_SECURITY_KEY_BITNESS 36
#define INTERNET_OPTION_REFRESH 37
#define INTERNET_OPTION_PROXY 38
#define INTERNET_OPTION_SETTINGS_CHANGED 39
#define INTERNET_OPTION_VERSION 40
#define INTERNET_OPTION_USER_AGENT 41
#define INTERNET_OPTION_END_BROWSER_SESSION 42
#define INTERNET_OPTION_PROXY_USERNAME 43
#define INTERNET_OPTION_PROXY_PASSWORD 44
#define INTERNET_FIRST_OPTION INTERNET_OPTION_CALLBACK
#define INTERNET_LAST_OPTION INTERNET_OPTION_USER_AGENT
#define INTERNET_PRIORITY_FOREGROUND 1000
#define INTERNET_HANDLE_TYPE_INTERNET 1
#define INTERNET_HANDLE_TYPE_CONNECT_FTP 2
#define INTERNET_HANDLE_TYPE_CONNECT_GOPHER 3
#define INTERNET_HANDLE_TYPE_CONNECT_HTTP 4
#define INTERNET_HANDLE_TYPE_FTP_FIND 5
#define INTERNET_HANDLE_TYPE_FTP_FIND_HTML 6
#define INTERNET_HANDLE_TYPE_FTP_FILE 7
#define INTERNET_HANDLE_TYPE_FTP_FILE_HTML 8
#define INTERNET_HANDLE_TYPE_GOPHER_FIND 9
#define INTERNET_HANDLE_TYPE_GOPHER_FIND_HTML 10
#define INTERNET_HANDLE_TYPE_GOPHER_FILE 11
#define INTERNET_HANDLE_TYPE_GOPHER_FILE_HTML 12
#define INTERNET_HANDLE_TYPE_HTTP_REQUEST 13
#define SECURITY_FLAG_SECURE 1
#define SECURITY_FLAG_SSL 2
#define SECURITY_FLAG_SSL3 4
#define SECURITY_FLAG_PCT 8
#define SECURITY_FLAG_PCT4 16
#define SECURITY_FLAG_IETFSSL4 0x20
#define SECURITY_FLAG_IGNORE_REVOCATION 0x00000080
#define SECURITY_FLAG_IGNORE_UNKNOWN_CA 0x00000100
#define SECURITY_FLAG_IGNORE_WRONG_USAGE 0x00000200
#define SECURITY_FLAG_40BIT 0x10000000
#define SECURITY_FLAG_128BIT 0x20000000
#define SECURITY_FLAG_56BIT 0x40000000
#define SECURITY_FLAG_UNKNOWNBIT 0x80000000
#define SECURITY_FLAG_NORMALBITNESS SECURITY_FLAG_40BIT
#define SECURITY_FLAG_IGNORE_CERT_CN_INVALID INTERNET_FLAG_IGNORE_CERT_CN_INVALID
#define SECURITY_FLAG_IGNORE_CERT_DATE_INVALID INTERNET_FLAG_IGNORE_CERT_DATE_INVALID
#define SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTPS INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTPS
#define SECURITY_FLAG_IGNORE_REDIRECT_TO_HTTP INTERNET_FLAG_IGNORE_REDIRECT_TO_HTTP
#define INTERNET_SERVICE_FTP 1
#define INTERNET_SERVICE_GOPHER 2
#define INTERNET_SERVICE_HTTP 3
#define INTERNET_STATUS_RESOLVING_NAME 10
#define INTERNET_STATUS_NAME_RESOLVED 11
#define INTERNET_STATUS_CONNECTING_TO_SERVER 20
#define INTERNET_STATUS_CONNECTED_TO_SERVER 21
#define INTERNET_STATUS_SENDING_REQUEST 30
#define INTERNET_STATUS_REQUEST_SENT 31
#define INTERNET_STATUS_RECEIVING_RESPONSE 40
#define INTERNET_STATUS_RESPONSE_RECEIVED 41
#define INTERNET_STATUS_CTL_RESPONSE_RECEIVED 42
#define INTERNET_STATUS_PREFETCH 43
#define INTERNET_STATUS_CLOSING_CONNECTION 50
#define INTERNET_STATUS_CONNECTION_CLOSED 51
#define INTERNET_STATUS_HANDLE_CREATED 60
#define INTERNET_STATUS_HANDLE_CLOSING 70
#define INTERNET_STATUS_REQUEST_COMPLETE 100
#define INTERNET_STATUS_REDIRECT 110
#define INTERNET_INVALID_STATUS_CALLBACK ((INTERNET_STATUS_CALLBACK)(-1L))
#define FTP_TRANSFER_TYPE_UNKNOWN 0
#define FTP_TRANSFER_TYPE_ASCII 1
#define FTP_TRANSFER_TYPE_BINARY 2
#define FTP_TRANSFER_TYPE_MASK (FTP_TRANSFER_TYPE_ASCII | FTP_TRANSFER_TYPE_BINARY)
#define MAX_GOPHER_DISPLAY_TEXT 128
#define MAX_GOPHER_SELECTOR_TEXT 256
#define MAX_GOPHER_HOST_NAME INTERNET_MAX_HOST_NAME_LENGTH
#define MAX_GOPHER_LOCATOR_LENGTH (1+MAX_GOPHER_DISPLAY_TEXT+1+MAX_GOPHER_SELECTOR_TEXT+1+MAX_GOPHER_HOST_NAME+1+INTERNET_MAX_PORT_NUMBER_LENGTH+1+1+2)
#define GOPHER_TYPE_TEXT_FILE 1
#define GOPHER_TYPE_DIRECTORY 2
#define GOPHER_TYPE_CSO 4
#define GOPHER_TYPE_ERROR 8
#define GOPHER_TYPE_MAC_BINHEX 16
#define GOPHER_TYPE_DOS_ARCHIVE 32
#define GOPHER_TYPE_UNIX_UUENCODED 64
#define GOPHER_TYPE_INDEX_SERVER 128
#define GOPHER_TYPE_TELNET 256
#define GOPHER_TYPE_BINARY 512
#define GOPHER_TYPE_REDUNDANT 1024
#define GOPHER_TYPE_TN3270 0x800
#define GOPHER_TYPE_GIF 0x1000
#define GOPHER_TYPE_IMAGE 0x2000
#define GOPHER_TYPE_BITMAP 0x4000
#define GOPHER_TYPE_MOVIE 0x8000
#define GOPHER_TYPE_SOUND 0x10000
#define GOPHER_TYPE_HTML 0x20000
#define GOPHER_TYPE_PDF 0x40000
#define GOPHER_TYPE_CALENDAR 0x80000
#define GOPHER_TYPE_INLINE 0x100000
#define GOPHER_TYPE_UNKNOWN 0x20000000
#define GOPHER_TYPE_ASK 0x40000000
#define GOPHER_TYPE_GOPHER_PLUS 0x80000000
#define IS_GOPHER_FILE(t) (BOOL)(((t)&GOPHER_TYPE_FILE_MASK)?TRUE:FALSE)
#define IS_GOPHER_DIRECTORY(t) (BOOL)(((t)&GOPHER_TYPE_DIRECTORY)?TRUE:FALSE)
#define IS_GOPHER_PHONE_SERVER(t) (BOOL)(((t)&GOPHER_TYPE_CSO)?TRUE:FALSE)
#define IS_GOPHER_ERROR(t) (BOOL)(((t)&GOPHER_TYPE_ERROR)?TRUE:FALSE)
#define IS_GOPHER_INDEX_SERVER(t) (BOOL)(((t)&GOPHER_TYPE_INDEX_SERVER)?TRUE:FALSE)
#define IS_GOPHER_TELNET_SESSION(t) (BOOL)(((t)&GOPHER_TYPE_TELNET)?TRUE:FALSE)
#define IS_GOPHER_BACKUP_SERVER(t) (BOOL)(((t)&GOPHER_TYPE_REDUNDANT)?TRUE:FALSE)
#define IS_GOPHER_TN3270_SESSION(t) (BOOL)(((t)&GOPHER_TYPE_TN3270)?TRUE:FALSE)
#define IS_GOPHER_ASK(t) (BOOL)(((t)&GOPHER_TYPE_ASK)?TRUE:FALSE)
#define IS_GOPHER_PLUS(t) (BOOL)(((t)&GOPHER_TYPE_GOPHER_PLUS)?TRUE:FALSE)
#define IS_GOPHER_TYPE_KNOWN(t) (BOOL)(((t)&GOPHER_TYPE_UNKNOWN)?FALSE:TRUE)
#define GOPHER_TYPE_FILE_MASK (GOPHER_TYPE_TEXT_FILE|GOPHER_TYPE_MAC_BINHEX|GOPHER_TYPE_DOS_ARCHIVE|\
GOPHER_TYPE_UNIX_UUENCODED|GOPHER_TYPE_BINARY|GOPHER_TYPE_GIF|GOPHER_TYPE_IMAGE|GOPHER_TYPE_BITMAP\
|GOPHER_TYPE_MOVIE|GOPHER_TYPE_SOUND|GOPHER_TYPE_HTML|GOPHER_TYPE_PDF|GOPHER_TYPE_CALENDAR|GOPHER_TYPE_INLINE)
#define MAX_GOPHER_CATEGORY_NAME 128
#define MAX_GOPHER_ATTRIBUTE_NAME 128
#define MIN_GOPHER_ATTRIBUTE_LENGTH 256
#define GOPHER_INFO_CATEGORY TEXT("+INFO")
#define GOPHER_ADMIN_CATEGORY TEXT("+ADMIN")
#define GOPHER_VIEWS_CATEGORY TEXT("+VIEWS")
#define GOPHER_ABSTRACT_CATEGORY TEXT("+ABSTRACT")
#define GOPHER_VERONICA_CATEGORY TEXT("+VERONICA")
#define GOPHER_ADMIN_ATTRIBUTE TEXT("Admin")
#define GOPHER_MOD_DATE_ATTRIBUTE TEXT("Mod-Date")
#define GOPHER_TTL_ATTRIBUTE TEXT("TTL")
#define GOPHER_SCORE_ATTRIBUTE TEXT("Score")
#define GOPHER_RANGE_ATTRIBUTE TEXT("Score-range")
#define GOPHER_SITE_ATTRIBUTE TEXT("Site")
#define GOPHER_ORG_ATTRIBUTE TEXT("Org")
#define GOPHER_LOCATION_ATTRIBUTE TEXT("Loc")
#define GOPHER_GEOG_ATTRIBUTE TEXT("Geog")
#define GOPHER_TIMEZONE_ATTRIBUTE TEXT("TZ")
#define GOPHER_PROVIDER_ATTRIBUTE TEXT("Provider")
#define GOPHER_VERSION_ATTRIBUTE TEXT("Version")
#define GOPHER_ABSTRACT_ATTRIBUTE TEXT("Abstract")
#define GOPHER_VIEW_ATTRIBUTE TEXT("View")
#define GOPHER_TREEWALK_ATTRIBUTE TEXT("treewalk")
#define GOPHER_ATTRIBUTE_ID_BASE 0xabcccc00
#define GOPHER_CATEGORY_ID_ALL (GOPHER_ATTRIBUTE_ID_BASE+1)
#define GOPHER_CATEGORY_ID_INFO (GOPHER_ATTRIBUTE_ID_BASE+2)
#define GOPHER_CATEGORY_ID_ADMIN (GOPHER_ATTRIBUTE_ID_BASE+3)
#define GOPHER_CATEGORY_ID_VIEWS (GOPHER_ATTRIBUTE_ID_BASE+4)
#define GOPHER_CATEGORY_ID_ABSTRACT (GOPHER_ATTRIBUTE_ID_BASE+5)
#define GOPHER_CATEGORY_ID_VERONICA (GOPHER_ATTRIBUTE_ID_BASE+6)
#define GOPHER_CATEGORY_ID_ASK (GOPHER_ATTRIBUTE_ID_BASE+7)
#define GOPHER_CATEGORY_ID_UNKNOWN (GOPHER_ATTRIBUTE_ID_BASE+8)
#define GOPHER_ATTRIBUTE_ID_ALL (GOPHER_ATTRIBUTE_ID_BASE+9)
#define GOPHER_ATTRIBUTE_ID_ADMIN (GOPHER_ATTRIBUTE_ID_BASE+10)
#define GOPHER_ATTRIBUTE_ID_MOD_DATE (GOPHER_ATTRIBUTE_ID_BASE+11)
#define GOPHER_ATTRIBUTE_ID_TTL (GOPHER_ATTRIBUTE_ID_BASE+12)
#define GOPHER_ATTRIBUTE_ID_SCORE (GOPHER_ATTRIBUTE_ID_BASE+13)
#define GOPHER_ATTRIBUTE_ID_RANGE (GOPHER_ATTRIBUTE_ID_BASE+14)
#define GOPHER_ATTRIBUTE_ID_SITE (GOPHER_ATTRIBUTE_ID_BASE+15)
#define GOPHER_ATTRIBUTE_ID_ORG (GOPHER_ATTRIBUTE_ID_BASE+16)
#define GOPHER_ATTRIBUTE_ID_LOCATION (GOPHER_ATTRIBUTE_ID_BASE+17)
#define GOPHER_ATTRIBUTE_ID_GEOG (GOPHER_ATTRIBUTE_ID_BASE+18)
#define GOPHER_ATTRIBUTE_ID_TIMEZONE (GOPHER_ATTRIBUTE_ID_BASE+19)
#define GOPHER_ATTRIBUTE_ID_PROVIDER (GOPHER_ATTRIBUTE_ID_BASE+20)
#define GOPHER_ATTRIBUTE_ID_VERSION (GOPHER_ATTRIBUTE_ID_BASE+21)
#define GOPHER_ATTRIBUTE_ID_ABSTRACT (GOPHER_ATTRIBUTE_ID_BASE+22)
#define GOPHER_ATTRIBUTE_ID_VIEW (GOPHER_ATTRIBUTE_ID_BASE+23)
#define GOPHER_ATTRIBUTE_ID_TREEWALK (GOPHER_ATTRIBUTE_ID_BASE+24)
#define GOPHER_ATTRIBUTE_ID_UNKNOWN (GOPHER_ATTRIBUTE_ID_BASE+25)
#define HTTP_MAJOR_VERSION 1
#define HTTP_MINOR_VERSION 0
#define HTTP_VERSION TEXT("HTTP/1.0")
#define HTTP_QUERY_MIME_VERSION 0
#define HTTP_QUERY_CONTENT_TYPE 1
#define HTTP_QUERY_CONTENT_TRANSFER_ENCODING 2
#define HTTP_QUERY_CONTENT_ID 3
#define HTTP_QUERY_CONTENT_DESCRIPTION 4
#define HTTP_QUERY_CONTENT_LENGTH 5
#define HTTP_QUERY_CONTENT_LANGUAGE 6
#define HTTP_QUERY_ALLOW 7
#define HTTP_QUERY_PUBLIC 8
#define HTTP_QUERY_DATE 9
#define HTTP_QUERY_EXPIRES 10
#define HTTP_QUERY_LAST_MODIFIED 11
#define HTTP_QUERY_MESSAGE_ID 12
#define HTTP_QUERY_URI 13
#define HTTP_QUERY_DERIVED_FROM 14
#define HTTP_QUERY_COST 15
#define HTTP_QUERY_LINK 16
#define HTTP_QUERY_PRAGMA 17
#define HTTP_QUERY_VERSION 18
#define HTTP_QUERY_STATUS_CODE 19
#define HTTP_QUERY_STATUS_TEXT 20
#define HTTP_QUERY_RAW_HEADERS 21
#define HTTP_QUERY_RAW_HEADERS_CRLF 22
#define HTTP_QUERY_CONNECTION 23
#define HTTP_QUERY_ACCEPT 24
#define HTTP_QUERY_ACCEPT_CHARSET 25
#define HTTP_QUERY_ACCEPT_ENCODING 26
#define HTTP_QUERY_ACCEPT_LANGUAGE 27
#define HTTP_QUERY_AUTHORIZATION 28
#define HTTP_QUERY_CONTENT_ENCODING 29
#define HTTP_QUERY_FORWARDED 30
#define HTTP_QUERY_FROM 31
#define HTTP_QUERY_IF_MODIFIED_SINCE 32
#define HTTP_QUERY_LOCATION 33
#define HTTP_QUERY_ORIG_URI 34
#define HTTP_QUERY_REFERER 35
#define HTTP_QUERY_RETRY_AFTER 36
#define HTTP_QUERY_SERVER 37
#define HTTP_QUERY_TITLE 38
#define HTTP_QUERY_USER_AGENT 39
#define HTTP_QUERY_WWW_AUTHENTICATE 40
#define HTTP_QUERY_PROXY_AUTHENTICATE 41
#define HTTP_QUERY_ACCEPT_RANGES 42
#define HTTP_QUERY_SET_COOKIE 43
#define HTTP_QUERY_COOKIE 44
#define HTTP_QUERY_REQUEST_METHOD 45
#define HTTP_QUERY_MAX 45
#define HTTP_QUERY_CUSTOM 65535
#define HTTP_QUERY_FLAG_REQUEST_HEADERS 0x80000000
#define HTTP_QUERY_FLAG_SYSTEMTIME 0x40000000
#define HTTP_QUERY_FLAG_NUMBER 0x20000000
#define HTTP_QUERY_FLAG_COALESCE 0x10000000
#define HTTP_QUERY_MODIFIER_FLAGS_MASK (HTTP_QUERY_FLAG_REQUEST_HEADERS|HTTP_QUERY_FLAG_SYSTEMTIME|HTTP_QUERY_FLAG_NUMBER|HTTP_QUERY_FLAG_COALESCE)
#define HTTP_QUERY_HEADER_MASK (~HTTP_QUERY_MODIFIER_FLAGS_MASK)
#define HTTP_STATUS_OK 200
#define HTTP_STATUS_CREATED 201
#define HTTP_STATUS_ACCEPTED 202
#define HTTP_STATUS_PARTIAL 203
#define HTTP_STATUS_NO_CONTENT 204
#define HTTP_STATUS_AMBIGUOUS 300
#define HTTP_STATUS_MOVED 301
#define HTTP_STATUS_REDIRECT 302
#define HTTP_STATUS_REDIRECT_METHOD 303
#define HTTP_STATUS_NOT_MODIFIED 304
#define HTTP_STATUS_BAD_REQUEST 400
#define HTTP_STATUS_DENIED 401
#define HTTP_STATUS_PAYMENT_REQ 402
#define HTTP_STATUS_FORBIDDEN 403
#define HTTP_STATUS_NOT_FOUND 404
#define HTTP_STATUS_BAD_METHOD 405
#define HTTP_STATUS_NONE_ACCEPTABLE 406
#define HTTP_STATUS_PROXY_AUTH_REQ 407
#define HTTP_STATUS_REQUEST_TIMEOUT 408
#define HTTP_STATUS_CONFLICT 409
#define HTTP_STATUS_GONE 410
#define HTTP_STATUS_AUTH_REFUSED 411
#define HTTP_STATUS_SERVER_ERROR 500
#define HTTP_STATUS_NOT_SUPPORTED 501
#define HTTP_STATUS_BAD_GATEWAY 502
#define HTTP_STATUS_SERVICE_UNAVAIL 503
#define HTTP_STATUS_GATEWAY_TIMEOUT 504
#define INTERNET_PREFETCH_PROGRESS 0
#define INTERNET_PREFETCH_COMPLETE 1
#define INTERNET_PREFETCH_ABORTED 2
#define FLAGS_ERROR_UI_FILTER_FOR_ERRORS 0x01
#define FLAGS_ERROR_UI_FLAGS_CHANGE_OPTIONS 0x02
#define FLAGS_ERROR_UI_FLAGS_GENERATE_DATA 0x04
#define FLAGS_ERROR_UI_FLAGS_NO_UI 0x08
#define HTTP_ADDREQ_INDEX_MASK 0x0000FFFF
#define HTTP_ADDREQ_FLAGS_MASK 0xFFFF0000
#define HTTP_ADDREQ_FLAG_ADD_IF_NEW 0x10000000
#define HTTP_ADDREQ_FLAG_ADD 0x20000000
#define HTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA 0x40000000
#define HTTP_ADDREQ_FLAG_COALESCE_WITH_SEMICOLON 0x01000000
#define HTTP_ADDREQ_FLAG_COALESCE HTTP_ADDREQ_FLAG_COALESCE_WITH_COMMA
#define HTTP_ADDREQ_FLAG_REPLACE 0x80000000
#define INTERNET_ERROR_BASE 12000
#define ERROR_INTERNET_OUT_OF_HANDLES (INTERNET_ERROR_BASE+1)
#define ERROR_INTERNET_TIMEOUT (INTERNET_ERROR_BASE+2)
#define ERROR_INTERNET_EXTENDED_ERROR (INTERNET_ERROR_BASE+3)
#define ERROR_INTERNET_INTERNAL_ERROR (INTERNET_ERROR_BASE+4)
#define ERROR_INTERNET_INVALID_URL (INTERNET_ERROR_BASE+5)
#define ERROR_INTERNET_UNRECOGNIZED_SCHEME (INTERNET_ERROR_BASE+6)
#define ERROR_INTERNET_NAME_NOT_RESOLVED (INTERNET_ERROR_BASE+7)
#define ERROR_INTERNET_PROTOCOL_NOT_FOUND (INTERNET_ERROR_BASE+8)
#define ERROR_INTERNET_INVALID_OPTION (INTERNET_ERROR_BASE+9)
#define ERROR_INTERNET_BAD_OPTION_LENGTH (INTERNET_ERROR_BASE+10)
#define ERROR_INTERNET_OPTION_NOT_SETTABLE (INTERNET_ERROR_BASE+11)
#define ERROR_INTERNET_SHUTDOWN (INTERNET_ERROR_BASE+12)
#define ERROR_INTERNET_INCORRECT_USER_NAME (INTERNET_ERROR_BASE+13)
#define ERROR_INTERNET_INCORRECT_PASSWORD (INTERNET_ERROR_BASE+14)
#define ERROR_INTERNET_LOGIN_FAILURE (INTERNET_ERROR_BASE+15)
#define ERROR_INTERNET_INVALID_OPERATION (INTERNET_ERROR_BASE+16)
#define ERROR_INTERNET_OPERATION_CANCELLED (INTERNET_ERROR_BASE+17)
#define ERROR_INTERNET_INCORRECT_HANDLE_TYPE (INTERNET_ERROR_BASE+18)
#define ERROR_INTERNET_INCORRECT_HANDLE_STATE (INTERNET_ERROR_BASE+19)
#define ERROR_INTERNET_NOT_PROXY_REQUEST (INTERNET_ERROR_BASE+20)
#define ERROR_INTERNET_REGISTRY_VALUE_NOT_FOUND (INTERNET_ERROR_BASE+21)
#define ERROR_INTERNET_BAD_REGISTRY_PARAMETER (INTERNET_ERROR_BASE+22)
#define ERROR_INTERNET_NO_DIRECT_ACCESS (INTERNET_ERROR_BASE+23)
#define ERROR_INTERNET_NO_CONTEXT (INTERNET_ERROR_BASE+24)
#define ERROR_INTERNET_NO_CALLBACK (INTERNET_ERROR_BASE+25)
#define ERROR_INTERNET_REQUEST_PENDING (INTERNET_ERROR_BASE+26)
#define ERROR_INTERNET_INCORRECT_FORMAT (INTERNET_ERROR_BASE+27)
#define ERROR_INTERNET_ITEM_NOT_FOUND (INTERNET_ERROR_BASE+28)
#define ERROR_INTERNET_CANNOT_CONNECT (INTERNET_ERROR_BASE+29)
#define ERROR_INTERNET_CONNECTION_ABORTED (INTERNET_ERROR_BASE+30)
#define ERROR_INTERNET_CONNECTION_RESET (INTERNET_ERROR_BASE+31)
#define ERROR_INTERNET_FORCE_RETRY (INTERNET_ERROR_BASE+32)
#define ERROR_INTERNET_INVALID_PROXY_REQUEST (INTERNET_ERROR_BASE+33)
#define ERROR_INTERNET_NEED_UI (INTERNET_ERROR_BASE+34)
#define ERROR_INTERNET_HANDLE_EXISTS (INTERNET_ERROR_BASE+36)
#define ERROR_INTERNET_SEC_CERT_DATE_INVALID (INTERNET_ERROR_BASE+37)
#define ERROR_INTERNET_SEC_CERT_CN_INVALID (INTERNET_ERROR_BASE+38)
#define ERROR_INTERNET_HTTP_TO_HTTPS_ON_REDIR (INTERNET_ERROR_BASE+39)
#define ERROR_INTERNET_HTTPS_TO_HTTP_ON_REDIR (INTERNET_ERROR_BASE+40)
#define ERROR_INTERNET_MIXED_SECURITY (INTERNET_ERROR_BASE+41)
#define ERROR_INTERNET_CHG_POST_IS_NON_SECURE (INTERNET_ERROR_BASE+42)
#define ERROR_INTERNET_POST_IS_NON_SECURE (INTERNET_ERROR_BASE+43)
#define ERROR_INTERNET_CLIENT_AUTH_CERT_NEEDED (INTERNET_ERROR_BASE+44)
#define ERROR_INTERNET_INVALID_CA (INTERNET_ERROR_BASE+45)
#define ERROR_INTERNET_CLIENT_AUTH_NOT_SETUP (INTERNET_ERROR_BASE+46)
#define ERROR_INTERNET_ASYNC_THREAD_FAILED (INTERNET_ERROR_BASE+47)
#define ERROR_INTERNET_REDIRECT_SCHEME_CHANGE (INTERNET_ERROR_BASE+48)
#define ERROR_FTP_TRANSFER_IN_PROGRESS (INTERNET_ERROR_BASE+110)
#define ERROR_FTP_DROPPED (INTERNET_ERROR_BASE+111)
#define ERROR_GOPHER_PROTOCOL_ERROR (INTERNET_ERROR_BASE+130)
#define ERROR_GOPHER_NOT_FILE (INTERNET_ERROR_BASE+131)
#define ERROR_GOPHER_DATA_ERROR (INTERNET_ERROR_BASE+132)
#define ERROR_GOPHER_END_OF_DATA (INTERNET_ERROR_BASE+133)
#define ERROR_GOPHER_INVALID_LOCATOR (INTERNET_ERROR_BASE+134)
#define ERROR_GOPHER_INCORRECT_LOCATOR_TYPE (INTERNET_ERROR_BASE+135)
#define ERROR_GOPHER_NOT_GOPHER_PLUS (INTERNET_ERROR_BASE+136)
#define ERROR_GOPHER_ATTRIBUTE_NOT_FOUND (INTERNET_ERROR_BASE+137)
#define ERROR_GOPHER_UNKNOWN_LOCATOR (INTERNET_ERROR_BASE+138)
#define ERROR_HTTP_HEADER_NOT_FOUND (INTERNET_ERROR_BASE+150)
#define ERROR_HTTP_DOWNLEVEL_SERVER (INTERNET_ERROR_BASE+151)
#define ERROR_HTTP_INVALID_SERVER_RESPONSE (INTERNET_ERROR_BASE+152)
#define ERROR_HTTP_INVALID_HEADER (INTERNET_ERROR_BASE+153)
#define ERROR_HTTP_INVALID_QUERY_REQUEST (INTERNET_ERROR_BASE+154)
#define ERROR_HTTP_HEADER_ALREADY_EXISTS (INTERNET_ERROR_BASE+155)
#define ERROR_HTTP_REDIRECT_FAILED (INTERNET_ERROR_BASE+156)
#define ERROR_HTTP_NOT_REDIRECTED (INTERNET_ERROR_BASE+160)
#define ERROR_INTERNET_SECURITY_CHANNEL_ERROR (INTERNET_ERROR_BASE+157)
#define ERROR_INTERNET_UNABLE_TO_CACHE_FILE (INTERNET_ERROR_BASE+158)
#define ERROR_INTERNET_TCPIP_NOT_INSTALLED (INTERNET_ERROR_BASE+159)
#define INTERNET_ERROR_LAST ERROR_INTERNET_TCPIP_NOT_INSTALLED
#define URLCACHEAPI DECLSPEC_IMPORT
#define NORMAL_CACHE_ENTRY 1
#define STABLE_CACHE_ENTRY 2
#define STICKY_CACHE_ENTRY 4
#define SPARSE_CACHE_ENTRY 0x10000
#define OCX_CACHE_ENTRY 0x20000
#define COOKIE_CACHE_ENTRY 0x100000
#define URLHISTORY_CACHE_ENTRY 0x200000
#define CACHE_ENTRY_ATTRIBUTE_FC 4
#define CACHE_ENTRY_HITRATE_FC 0x10
#define CACHE_ENTRY_MODTIME_FC 0x40
#define CACHE_ENTRY_EXPTIME_FC 0x80
#define CACHE_ENTRY_ACCTIME_FC 0x100
#define CACHE_ENTRY_SYNCTIME_FC 0x200
#define CACHE_ENTRY_HEADERINFO_FC 0x400
#define IRF_ASYNC WININET_API_FLAG_ASYNC
#define IRF_SYNC WININET_API_FLAG_SYNC
#define IRF_USE_CONTEXT WININET_API_FLAG_USE_CONTEXT
#define IRF_NO_WAIT 8
#define HSR_ASYNC	WININET_API_FLAG_ASYNC
#define HSR_SYNC	WININET_API_FLAG_SYNC
#define HSR_USE_CONTEXT WININET_API_FLAG_USE_CONTEXT
#define HSR_INITIATE	8
#define HSR_DOWNLOAD	16
#define HSR_CHUNKED	32
#define INTERNET_DIAL_UNATTENDED	0x8000
#define INTERNET_DIALSTATE_DISCONNECTED	1
#define INTERENT_GOONLINE_REFRESH	1
#define INTERENT_GOONLINE_MASK	1
#define INTERNET_AUTODIAL_FORCE_ONLINE	1
#define INTERNET_AUTODIAL_FORCE_UNATTENDED	2
#define INTERNET_AUTODIAL_FAILIFSECURITYCHECK	4
#define INTERNET_CONNECTION_MODEM	1
#define INTERNET_CONNECTION_LAN	2
#define INTERNET_CONNECTION_PROXY	4
#define INTERNET_CONNECTION_MODEM_BUSY	8
#define CACHEGROUP_SEARCH_ALL	0
#define CACHEGROUP_SEARCH_BYURL	1
#define INTERNET_CACHE_GROUP_ADD	0
#define INTERNET_CACHE_GROUP_REMOVE	1

#ifndef RC_INVOKED
typedef PVOID HINTERNET;
typedef HINTERNET *LPHINTERNET;
typedef LONGLONG GROUPID;
typedef WORD INTERNET_PORT,*LPINTERNET_PORT;
typedef enum {
	INTERNET_SCHEME_PARTIAL = -2, INTERNET_SCHEME_UNKNOWN = -1,
	INTERNET_SCHEME_DEFAULT = 0, INTERNET_SCHEME_FTP, INTERNET_SCHEME_GOPHER,
	INTERNET_SCHEME_HTTP, INTERNET_SCHEME_HTTPS, INTERNET_SCHEME_FILE,
	INTERNET_SCHEME_NEWS, INTERNET_SCHEME_MAILTO, INTERNET_SCHEME_SOCKS,
	INTERNET_SCHEME_FIRST = INTERNET_SCHEME_FTP, INTERNET_SCHEME_LAST = INTERNET_SCHEME_SOCKS
} INTERNET_SCHEME,*LPINTERNET_SCHEME;
typedef struct { DWORD dwResult; DWORD dwError; } INTERNET_ASYNC_RESULT,*LPINTERNET_ASYNC_RESULT;
typedef struct { DWORD dwStatus; DWORD dwSize; } INTERNET_PREFETCH_STATUS,*LPINTERNET_PREFETCH_STATUS;
typedef struct {
	DWORD dwAccessType;
	LPCTSTR lpszProxy;
	LPCTSTR lpszProxyBypass;
} INTERNET_PROXY_INFO,*LPINTERNET_PROXY_INFO;
typedef struct {
	DWORD dwMajorVersion;
	DWORD dwMinorVersion;
} INTERNET_VERSION_INFO,*LPINTERNET_VERSION_INFO;
typedef struct {
	DWORD dwStructSize;
	LPSTR lpszScheme;
	DWORD dwSchemeLength;
	INTERNET_SCHEME nScheme;
	LPSTR lpszHostName;
	DWORD dwHostNameLength;
	INTERNET_PORT nPort;
	LPSTR lpszUserName;
	DWORD dwUserNameLength;
	LPSTR lpszPassword;
	DWORD dwPasswordLength;
	LPSTR lpszUrlPath;
	DWORD dwUrlPathLength;
	LPSTR lpszExtraInfo;
	DWORD dwExtraInfoLength;
} URL_COMPONENTSA,*LPURL_COMPONENTSA;
typedef struct {
	DWORD dwStructSize;
	LPWSTR lpszScheme;
	DWORD dwSchemeLength;
	INTERNET_SCHEME nScheme;
	LPWSTR lpszHostName;
	DWORD dwHostNameLength;
	INTERNET_PORT nPort;
	LPWSTR lpszUserName;
	DWORD dwUserNameLength;
	LPWSTR lpszPassword;
	DWORD dwPasswordLength;
	LPWSTR lpszUrlPath;
	DWORD dwUrlPathLength;
	LPWSTR lpszExtraInfo;
	DWORD dwExtraInfoLength;
} URL_COMPONENTSW,*LPURL_COMPONENTSW;
typedef struct {
	FILETIME ftExpiry;
	FILETIME ftStart;
	LPTSTR lpszSubjectInfo;
	LPTSTR lpszIssuerInfo;
	LPTSTR lpszProtocolName;
	LPTSTR lpszSignatureAlgName;
	LPTSTR lpszEncryptionAlgName;
	DWORD dwKeySize;
} INTERNET_CERTIFICATE_INFO,*LPINTERNET_CERTIFICATE_INFO;
typedef VOID (CALLBACK * INTERNET_STATUS_CALLBACK)(HINTERNET,DWORD,DWORD,PVOID,DWORD);
typedef INTERNET_STATUS_CALLBACK * LPINTERNET_STATUS_CALLBACK;
typedef struct {
	CHAR DisplayString[MAX_GOPHER_DISPLAY_TEXT+1];
	DWORD GopherType;
	DWORD SizeLow;
	DWORD SizeHigh;
	FILETIME LastModificationTime;
	CHAR Locator[MAX_GOPHER_LOCATOR_LENGTH+1];
} GOPHER_FIND_DATAA,*LPGOPHER_FIND_DATAA;
typedef struct {
	WCHAR DisplayString[MAX_GOPHER_DISPLAY_TEXT+1];
	DWORD GopherType;
	DWORD SizeLow;
	DWORD SizeHigh;
	FILETIME LastModificationTime;
	WCHAR Locator[MAX_GOPHER_LOCATOR_LENGTH+1];
} GOPHER_FIND_DATAW,*LPGOPHER_FIND_DATAW;
typedef struct {
	LPCTSTR Comment;
	LPCTSTR EmailAddress;
} GOPHER_ADMIN_ATTRIBUTE_TYPE,*LPGOPHER_ADMIN_ATTRIBUTE_TYPE;
typedef struct {FILETIME DateAndTime;} GOPHER_MOD_DATE_ATTRIBUTE_TYPE,*LPGOPHER_MOD_DATE_ATTRIBUTE_TYPE;
typedef struct {DWORD Ttl;} GOPHER_TTL_ATTRIBUTE_TYPE,*LPGOPHER_TTL_ATTRIBUTE_TYPE;
typedef struct {INT Score;} GOPHER_SCORE_ATTRIBUTE_TYPE,*LPGOPHER_SCORE_ATTRIBUTE_TYPE;
typedef struct {
	INT LowerBound;
	INT UpperBound;
} GOPHER_SCORE_RANGE_ATTRIBUTE_TYPE,*LPGOPHER_SCORE_RANGE_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Site;} GOPHER_SITE_ATTRIBUTE_TYPE,*LPGOPHER_SITE_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Organization;} GOPHER_ORGANIZATION_ATTRIBUTE_TYPE,*LPGOPHER_ORGANIZATION_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Location;} GOPHER_LOCATION_ATTRIBUTE_TYPE,*LPGOPHER_LOCATION_ATTRIBUTE_TYPE;
typedef struct {
	INT DegreesNorth;
	INT MinutesNorth;
	INT SecondsNorth;
	INT DegreesEast;
	INT MinutesEast;
	INT SecondsEast;
} GOPHER_GEOGRAPHICAL_LOCATION_ATTRIBUTE_TYPE,*LPGOPHER_GEOGRAPHICAL_LOCATION_ATTRIBUTE_TYPE;
typedef struct {INT Zone;} GOPHER_TIMEZONE_ATTRIBUTE_TYPE,*LPGOPHER_TIMEZONE_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Provider;} GOPHER_PROVIDER_ATTRIBUTE_TYPE,*LPGOPHER_PROVIDER_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Version;} GOPHER_VERSION_ATTRIBUTE_TYPE,*LPGOPHER_VERSION_ATTRIBUTE_TYPE;
typedef struct {
	LPCTSTR ShortAbstract;
	LPCTSTR AbstractFile;
} GOPHER_ABSTRACT_ATTRIBUTE_TYPE,*LPGOPHER_ABSTRACT_ATTRIBUTE_TYPE;
typedef struct {
	LPCTSTR ContentType;
	LPCTSTR Language;
	DWORD Size;
} GOPHER_VIEW_ATTRIBUTE_TYPE,*LPGOPHER_VIEW_ATTRIBUTE_TYPE;
typedef struct {BOOL TreeWalk;} GOPHER_VERONICA_ATTRIBUTE_TYPE,*LPGOPHER_VERONICA_ATTRIBUTE_TYPE;
typedef struct {
	LPCTSTR QuestionType;
	LPCTSTR QuestionText;
} GOPHER_ASK_ATTRIBUTE_TYPE,*LPGOPHER_ASK_ATTRIBUTE_TYPE;
typedef struct {LPCTSTR Text;} GOPHER_UNKNOWN_ATTRIBUTE_TYPE,*LPGOPHER_UNKNOWN_ATTRIBUTE_TYPE;
typedef struct {
	DWORD CategoryId;
	DWORD AttributeId;
	union {
		GOPHER_ADMIN_ATTRIBUTE_TYPE Admin;
		GOPHER_MOD_DATE_ATTRIBUTE_TYPE ModDate;
		GOPHER_TTL_ATTRIBUTE_TYPE Ttl;
		GOPHER_SCORE_ATTRIBUTE_TYPE Score;
		GOPHER_SCORE_RANGE_ATTRIBUTE_TYPE ScoreRange;
		GOPHER_SITE_ATTRIBUTE_TYPE Site;
		GOPHER_ORGANIZATION_ATTRIBUTE_TYPE Organization;
		GOPHER_LOCATION_ATTRIBUTE_TYPE Location;
		GOPHER_GEOGRAPHICAL_LOCATION_ATTRIBUTE_TYPE GeographicalLocation;
		GOPHER_TIMEZONE_ATTRIBUTE_TYPE TimeZone;
		GOPHER_PROVIDER_ATTRIBUTE_TYPE Provider;
		GOPHER_VERSION_ATTRIBUTE_TYPE Version;
		GOPHER_ABSTRACT_ATTRIBUTE_TYPE Abstract;
		GOPHER_VIEW_ATTRIBUTE_TYPE View;
		GOPHER_VERONICA_ATTRIBUTE_TYPE Veronica;
		GOPHER_ASK_ATTRIBUTE_TYPE Ask;
		GOPHER_UNKNOWN_ATTRIBUTE_TYPE Unknown;
	} AttributeType;
} GOPHER_ATTRIBUTE_TYPE,*LPGOPHER_ATTRIBUTE_TYPE;
typedef BOOL(CALLBACK *GOPHER_ATTRIBUTE_ENUMERATOR)(LPGOPHER_ATTRIBUTE_TYPE,DWORD);
typedef struct _INTERNET_CACHE_ENTRY_INFOA {
	DWORD dwStructSize;
	LPSTR lpszSourceUrlName;
	LPSTR lpszLocalFileName;
	DWORD CacheEntryType;
	DWORD dwUseCount;
	DWORD dwHitRate;
	DWORD dwSizeLow;
	DWORD dwSizeHigh;
	FILETIME LastModifiedTime;
	FILETIME ExpireTime;
	FILETIME LastAccessTime;
	FILETIME LastSyncTime;
	PBYTE lpHeaderInfo;
	DWORD dwHeaderInfoSize;
	LPSTR lpszFileExtension;
	DWORD dwReserved;
} INTERNET_CACHE_ENTRY_INFOA,*LPINTERNET_CACHE_ENTRY_INFOA;
typedef struct _INTERNET_CACHE_ENTRY_INFOW {
	DWORD dwStructSize;
	LPSTR lpszSourceUrlName;
	LPWSTR lpszLocalFileName;
	DWORD CacheEntryType;
	DWORD dwUseCount;
	DWORD dwHitRate;
	DWORD dwSizeLow;
	DWORD dwSizeHigh;
	FILETIME LastModifiedTime;
	FILETIME ExpireTime;
	FILETIME LastAccessTime;
	FILETIME LastSyncTime;
	PBYTE lpHeaderInfo;
	DWORD dwHeaderInfoSize;
	LPWSTR lpszFileExtension;
	DWORD dwReserved;
} INTERNET_CACHE_ENTRY_INFOW,*LPINTERNET_CACHE_ENTRY_INFOW;
typedef struct _INTERNET_BUFFERSA {
	DWORD dwStructSize;
	struct _INTERNET_BUFFERSA *Next;
	LPCSTR lpcszHeader;
	DWORD dwHeadersLength;
	DWORD dwHeadersTotal;
	LPVOID lpvBuffer;
	DWORD dwBufferLength;
	DWORD dwBufferTotal;
	DWORD dwOffsetLow;
	DWORD dwOffsetHigh;
} INTERNET_BUFFERSA,*LPINTERNET_BUFFERSA;
typedef struct _INTERNET_BUFFERSW {
	DWORD dwStructSize;
	struct _INTERNET_BUFFERS *Next;
	LPCWSTR  lpcszHeader;
	DWORD dwHeadersLength;
	DWORD dwHeadersTotal;
	LPVOID lpvBuffer;
	DWORD dwBufferLength;
	DWORD dwBufferTotal;
	DWORD dwOffsetLow;
	DWORD dwOffsetHigh;
} INTERNET_BUFFERSW,*LPINTERNET_BUFFERSW;

#define GROUP_OWNER_STORAGE_SIZE 4
#define GROUPNAME_MAX_LENGTH 120
typedef struct _INTERNET_CACHE_GROUP_INFOA {
	DWORD dwGroupSize;
	DWORD dwGroupFlags;
	DWORD dwGroupType;
	DWORD dwDiskUsage;
	DWORD dwDiskQuota;
	DWORD dwOwnerStorage[GROUP_OWNER_STORAGE_SIZE];
	CHAR  szGroupName[GROUPNAME_MAX_LENGTH];
} INTERNET_CACHE_GROUP_INFOA, * LPINTERNET_CACHE_GROUP_INFOA;
typedef struct _INTERNET_CACHE_GROUP_INFOW {
	DWORD dwGroupSize;
	DWORD dwGroupFlags;
	DWORD dwGroupType;
	DWORD dwDiskUsage;
	DWORD dwDiskQuota;
	DWORD dwOwnerStorage[GROUP_OWNER_STORAGE_SIZE];
	WCHAR szGroupName[GROUPNAME_MAX_LENGTH];
} INTERNET_CACHE_GROUP_INFOW, * LPINTERNET_CACHE_GROUP_INFOW;

#ifdef UNICODE
typedef URL_COMPONENTSW URL_COMPONENTS;
typedef LPURL_COMPONENTSW LPURL_COMPONENTS;
typedef GOPHER_FIND_DATAW GOPHER_FIND_DATA;
typedef LPGOPHER_FIND_DATAW LPGOPHER_FIND_DATA;
typedef INTERNET_CACHE_ENTRY_INFOW INTERNET_CACHE_ENTRY_INFO;
typedef LPINTERNET_CACHE_ENTRY_INFOW LPINTERNET_CACHE_ENTRY_INFO;
typedef INTERNET_BUFFERSW INTERNET_BUFFERS,*LPINTERNET_BUFFERS;
typedef INTERNET_CACHE_GROUP_INFOW INTERNET_CACHE_GROUP_INFO;
typedef LPINTERNET_CACHE_GROUP_INFOW LPINTERNET_CACHE_GROUP_INFO;
#else
typedef URL_COMPONENTSA URL_COMPONENTS;
typedef LPURL_COMPONENTSA LPURL_COMPONENTS;
typedef GOPHER_FIND_DATAA GOPHER_FIND_DATA;
typedef LPGOPHER_FIND_DATAA LPGOPHER_FIND_DATA;
typedef INTERNET_CACHE_ENTRY_INFOA INTERNET_CACHE_ENTRY_INFO;
typedef LPINTERNET_CACHE_ENTRY_INFOA LPINTERNET_CACHE_ENTRY_INFO;
typedef INTERNET_BUFFERSA INTERNET_BUFFERS,*LPINTERNET_BUFFERS;
typedef INTERNET_CACHE_GROUP_INFOA INTERNET_CACHE_GROUP_INFO;
typedef LPINTERNET_CACHE_GROUP_INFOA LPINTERNET_CACHE_GROUP_INFO;
#endif
BOOL WINAPI InternetTimeFromSystemTime(SYSTEMTIME*,DWORD,LPSTR,DWORD);
BOOL WINAPI InternetTimeToSystemTime(LPCSTR,SYSTEMTIME*,DWORD);
BOOL WINAPI InternetDebugGetLocalTime(SYSTEMTIME*,PDWORD);
BOOL WINAPI InternetCrackUrlA(LPCSTR,DWORD,DWORD,LPURL_COMPONENTSA);
BOOL WINAPI InternetCrackUrlW(LPCWSTR,DWORD,DWORD,LPURL_COMPONENTSW);
BOOL WINAPI InternetCreateUrlA(LPURL_COMPONENTSA,DWORD,LPSTR,PDWORD);
BOOL WINAPI InternetCreateUrlW(LPURL_COMPONENTSW,DWORD,LPWSTR,PDWORD);
BOOL WINAPI InternetCanonicalizeUrlA(LPCSTR,LPSTR,PDWORD,DWORD);
BOOL WINAPI InternetCanonicalizeUrlW(LPCWSTR,LPWSTR,PDWORD,DWORD);
BOOL WINAPI InternetCombineUrlA(LPCSTR,LPCSTR,LPSTR,PDWORD,DWORD);
BOOL WINAPI InternetCombineUrlW(LPCWSTR,LPCWSTR,LPWSTR,PDWORD,DWORD);
HINTERNET WINAPI InternetOpenA(LPCSTR,DWORD,LPCSTR,LPCSTR,DWORD);
HINTERNET WINAPI InternetOpenW(LPCWSTR,DWORD,LPCWSTR,LPCWSTR,DWORD);
BOOL WINAPI InternetCloseHandle(HINTERNET);
HINTERNET WINAPI InternetConnectA(HINTERNET,LPCSTR,INTERNET_PORT,LPCSTR,LPCSTR,DWORD,DWORD,DWORD);
HINTERNET WINAPI InternetConnectW(HINTERNET,LPCWSTR,INTERNET_PORT,LPCWSTR,LPCWSTR,DWORD,DWORD,DWORD);
HINTERNET WINAPI InternetOpenUrlA(HINTERNET,LPCSTR,LPCSTR,DWORD,DWORD,DWORD);
HINTERNET WINAPI InternetOpenUrlW(HINTERNET,LPCWSTR,LPCWSTR,DWORD,DWORD,DWORD);
BOOL WINAPI InternetReadFile(HINTERNET,PVOID,DWORD,PDWORD);
DWORD WINAPI InternetSetFilePointer( HINTERNET,LONG,PVOID,DWORD,DWORD);
BOOL WINAPI InternetWriteFile(HINTERNET,LPCVOID,DWORD,PDWORD);
BOOL WINAPI InternetQueryDataAvailable( HINTERNET,PDWORD,DWORD,DWORD);
BOOL WINAPI InternetFindNextFileA(HINTERNET,PVOID);
BOOL WINAPI InternetFindNextFileW(HINTERNET,PVOID);
BOOL WINAPI InternetQueryOptionA(HINTERNET,DWORD,PVOID,PDWORD);
BOOL WINAPI InternetQueryOptionW(HINTERNET,DWORD,PVOID,PDWORD);
BOOL WINAPI InternetSetOptionA(HINTERNET,DWORD,PVOID,DWORD);
BOOL WINAPI InternetSetOptionW(HINTERNET,DWORD,PVOID,DWORD);
BOOL WINAPI InternetSetOptionExA(HINTERNET,DWORD,PVOID,DWORD,DWORD);
BOOL WINAPI InternetSetOptionExW(HINTERNET,DWORD,PVOID,DWORD,DWORD);
BOOL WINAPI InternetGetLastResponseInfoA(PDWORD,LPSTR,PDWORD);
BOOL WINAPI InternetGetLastResponseInfoW(PDWORD,LPWSTR,PDWORD);
INTERNET_STATUS_CALLBACK WINAPI InternetSetStatusCallback(HINTERNET,INTERNET_STATUS_CALLBACK);
HINTERNET WINAPI FtpFindFirstFileA(HINTERNET,LPCSTR,LPWIN32_FIND_DATA,DWORD,DWORD);
HINTERNET WINAPI FtpFindFirstFileW(HINTERNET,LPCWSTR,LPWIN32_FIND_DATA,DWORD,DWORD);
BOOL WINAPI FtpGetFileA(HINTERNET,LPCSTR,LPCSTR,BOOL,DWORD,DWORD,DWORD);
BOOL WINAPI FtpGetFileW( HINTERNET,LPCWSTR,LPCWSTR,BOOL,DWORD,DWORD,DWORD);
BOOL WINAPI FtpPutFileA(HINTERNET,LPCSTR,LPCSTR,DWORD,DWORD);
BOOL WINAPI FtpPutFileW(HINTERNET,LPCWSTR,LPCWSTR,DWORD,DWORD);
BOOL WINAPI FtpDeleteFileA(HINTERNET,LPCSTR);
BOOL WINAPI FtpDeleteFileW(HINTERNET,LPCWSTR);
BOOL WINAPI FtpRenameFileA(HINTERNET, LPCSTR,LPCSTR);
BOOL WINAPI FtpRenameFileW(HINTERNET,LPCWSTR,LPCWSTR);
HINTERNET WINAPI FtpOpenFileA(HINTERNET,LPCSTR,DWORD,DWORD,DWORD);
HINTERNET WINAPI FtpOpenFileW(HINTERNET,LPCWSTR,DWORD,DWORD,DWORD);
BOOL WINAPI FtpCreateDirectoryA(HINTERNET,LPCSTR);
BOOL WINAPI FtpCreateDirectoryW(HINTERNET,LPCWSTR);
BOOL WINAPI FtpRemoveDirectoryA(HINTERNET,LPCSTR);
BOOL WINAPI FtpRemoveDirectoryW(HINTERNET,LPCWSTR);
BOOL WINAPI FtpSetCurrentDirectoryA(HINTERNET,LPCSTR);
BOOL WINAPI FtpSetCurrentDirectoryW(HINTERNET,LPCWSTR);
BOOL WINAPI FtpGetCurrentDirectoryA(HINTERNET,LPSTR,PDWORD);
BOOL WINAPI FtpGetCurrentDirectoryW(HINTERNET,LPWSTR,PDWORD);
BOOL WINAPI FtpCommandA(HINTERNET,BOOL,DWORD,LPCSTR,DWORD);
BOOL WINAPI FtpCommandW( HINTERNET,BOOL,DWORD,LPCWSTR,DWORD);
BOOL WINAPI GopherCreateLocatorA(LPCSTR,INTERNET_PORT,LPCSTR,LPCSTR,DWORD,LPSTR,PDWORD);
BOOL WINAPI GopherCreateLocatorW(LPCWSTR,INTERNET_PORT,LPCWSTR,LPCWSTR,DWORD,LPWSTR,PDWORD);
BOOL WINAPI GopherGetLocatorTypeA(LPCSTR,PDWORD);
BOOL WINAPI GopherGetLocatorTypeW(LPCWSTR,PDWORD);
HINTERNET WINAPI GopherFindFirstFileA(HINTERNET,LPCSTR,LPCSTR,LPGOPHER_FIND_DATAA,DWORD,DWORD);
HINTERNET WINAPI GopherFindFirstFileW(HINTERNET,LPCWSTR,LPCWSTR,LPGOPHER_FIND_DATAW,DWORD,DWORD);
HINTERNET WINAPI GopherOpenFileA(HINTERNET,LPCSTR,LPCSTR,DWORD,DWORD);
HINTERNET WINAPI GopherOpenFileW(HINTERNET,LPCWSTR,LPCWSTR,DWORD,DWORD);
BOOL WINAPI GopherGetAttributeA(HINTERNET,LPCSTR,LPCSTR,LPBYTE,DWORD,PDWORD,GOPHER_ATTRIBUTE_ENUMERATOR,DWORD);
BOOL WINAPI GopherGetAttributeW(HINTERNET,LPCWSTR,
LPCWSTR, LPBYTE, DWORD, PDWORD, GOPHER_ATTRIBUTE_ENUMERATOR, DWORD);
HINTERNET WINAPI HttpOpenRequestA(HINTERNET,LPCSTR,LPCSTR,LPCSTR,LPCSTR,LPCSTR *,DWORD,DWORD);
HINTERNET WINAPI HttpOpenRequestW(HINTERNET,LPCWSTR,LPCWSTR,LPCWSTR,LPCWSTR,LPCWSTR FAR * lplpszAcceptTypes , DWORD dwFlags, DWORD dwContext);
BOOL WINAPI HttpAddRequestHeadersA(HINTERNET,LPCSTR,DWORD,DWORD);
BOOL WINAPI HttpAddRequestHeadersW(HINTERNET,LPCWSTR,DWORD,DWORD);
BOOL WINAPI HttpSendRequestA(HINTERNET,LPCSTR,DWORD,PVOID,DWORD);
BOOL WINAPI HttpSendRequestW(HINTERNET,LPCWSTR,DWORD,PVOID,DWORD);
BOOL WINAPI HttpQueryInfoA(HINTERNET,DWORD,PVOID,PDWORD,PDWORD);
BOOL WINAPI HttpQueryInfoW(HINTERNET,DWORD,PVOID,PDWORD,PDWORD);
BOOL WINAPI InternetSetCookieA(LPCSTR,LPCSTR,LPCSTR);
BOOL WINAPI InternetSetCookieW(LPCSTR,LPCWSTR,LPCWSTR);
BOOL WINAPI InternetGetCookieA(LPCSTR,LPCSTR,LPSTR,PDWORD);
BOOL WINAPI InternetGetCookieW(LPCSTR,LPCWSTR,LPWSTR,PDWORD);
DWORD WINAPI InternetAttemptConnect(DWORD);
DWORD WINAPI InternetErrorDlg(HWND,HINTERNET,DWORD,DWORD,PVOID *);
DWORD WINAPI InternetConfirmZoneCrossing(HWND,LPSTR,LPSTR,BOOL);
BOOL WINAPI CreateUrlCacheEntryA(LPCSTR,DWORD,LPCSTR,LPSTR,DWORD);
BOOL WINAPI CreateUrlCacheEntryW(LPCSTR,DWORD,LPCSTR,LPWSTR,DWORD);
BOOL WINAPI CommitUrlCacheEntryA(LPCSTR,LPCSTR,FILETIME,FILETIME,DWORD,LPBYTE,DWORD,LPCSTR,DWORD);
BOOL WINAPI CommitUrlCacheEntryW(LPCSTR,LPCWSTR,FILETIME,FILETIME,DWORD,LPBYTE,DWORD,LPCWSTR,DWORD);
BOOL WINAPI RetrieveUrlCacheEntryFileA(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOA,PDWORD,DWORD);
BOOL WINAPI RetrieveUrlCacheEntryFileW(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOW,PDWORD,DWORD);
BOOL WINAPI UnlockUrlCacheEntryFile(LPCSTR,DWORD);
HANDLE WINAPI RetrieveUrlCacheEntryStreamA(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOA,PDWORD,BOOL,DWORD);
HANDLE WINAPI RetrieveUrlCacheEntryStreamW( LPCSTR,LPINTERNET_CACHE_ENTRY_INFOW,PDWORD,BOOL,DWORD);
BOOL WINAPI ReadUrlCacheEntryStream(HANDLE,DWORD,PVOID,PDWORD,DWORD);
BOOL WINAPI UnlockUrlCacheEntryStream( HANDLE,DWORD);
BOOL WINAPI GetUrlCacheEntryInfoA(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOA,PDWORD);
BOOL WINAPI GetUrlCacheEntryInfoW(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOW,PDWORD);
BOOL WINAPI SetUrlCacheEntryInfoA(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOA,DWORD);
BOOL WINAPI SetUrlCacheEntryInfoW(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOW,DWORD);
HANDLE WINAPI FindFirstUrlCacheEntryA(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOA,PDWORD);
HANDLE WINAPI FindFirstUrlCacheEntryW(LPCSTR,LPINTERNET_CACHE_ENTRY_INFOW,PDWORD);
BOOL WINAPI FindNextUrlCacheEntryA(HANDLE,LPINTERNET_CACHE_ENTRY_INFOA,PDWORD);
BOOL WINAPI FindNextUrlCacheEntryW(HANDLE,LPINTERNET_CACHE_ENTRY_INFOW,PDWORD);
BOOL WINAPI FindCloseUrlCache(HANDLE);
BOOL WINAPI DeleteUrlCacheEntry(LPCSTR);
DWORD AuthenticateUser(PVOID*,LPSTR,LPSTR,DWORD,LPSTR,DWORD,LPSTR,LPSTR);
BOOL WINAPI HttpSendRequestExA(HINTERNET,LPINTERNET_BUFFERSA,LPINTERNET_BUFFERSA,DWORD,DWORD);
BOOL WINAPI HttpSendRequestExW(HINTERNET,LPINTERNET_BUFFERSW,LPINTERNET_BUFFERSW,DWORD,DWORD);
BOOL WINAPI HttpEndRequestA(HINTERNET,LPINTERNET_BUFFERSA,DWORD,DWORD);
BOOL WINAPI HttpEndRequestW(HINTERNET,LPINTERNET_BUFFERSW,DWORD,DWORD);
DWORD WINAPI InternetDial(HWND,LPTSTR,DWORD,LPDWORD,DWORD);
DWORD WINAPI InternetHangUp(DWORD,DWORD);
BOOL WINAPI InternetGoOnline(LPTSTR,HWND,DWORD);
BOOL WINAPI InternetAutodial(DWORD,DWORD);
BOOL WINAPI InternetAutodialHangup(DWORD);
BOOL WINAPI InternetGetConnectedState(LPDWORD,DWORD);
BOOL WINAPI InternetSetDialState(LPCTSTR,DWORD,DWORD);
BOOL WINAPI InternetReadFileExA(HINTERNET,LPINTERNET_BUFFERSA,DWORD,DWORD_PTR);
BOOL WINAPI InternetReadFileExW(HINTERNET,LPINTERNET_BUFFERSW,DWORD,DWORD_PTR);
GROUPID WINAPI CreateUrlCacheGroup(DWORD,LPVOID);
BOOL WINAPI DeleteUrlCacheGroup(GROUPID,DWORD,LPVOID);
HANDLE WINAPI FindFirstUrlCacheGroup(DWORD,DWORD,LPVOID,DWORD,GROUPID*,LPVOID);
BOOL WINAPI FindNextUrlCacheGroup(HANDLE,GROUPID*,LPVOID);
BOOL WINAPI GetUrlCacheGroupAttributeA(GROUPID,DWORD,DWORD,LPINTERNET_CACHE_GROUP_INFOA,LPDWORD,LPVOID);
BOOL WINAPI GetUrlCacheGroupAttributeW(GROUPID,DWORD,DWORD,LPINTERNET_CACHE_GROUP_INFOW,LPDWORD,LPVOID);
BOOL WINAPI SetUrlCacheGroupAttributeA(GROUPID,DWORD,DWORD,LPINTERNET_CACHE_GROUP_INFOA,LPVOID);
BOOL WINAPI SetUrlCacheGroupAttributeW(GROUPID,DWORD,DWORD,LPINTERNET_CACHE_GROUP_INFOW,LPVOID);

#ifdef UNICODE
#define InternetCrackUrl InternetCrackUrlW
#define InternetCreateUrl InternetCreateUrlW
#define InternetCanonicalizeUrl InternetCanonicalizeUrlW
#define InternetCombineUrl InternetCombineUrlW
#define InternetOpen InternetOpenW
#define InternetConnect InternetConnectW
#define InternetOpenUrl InternetOpenUrlW
#define InternetFindNextFile InternetFindNextFileW
#define InternetQueryOption InternetQueryOptionW
#define InternetSetOption InternetSetOptionW
#define InternetSetOptionEx InternetSetOptionExW
#define InternetGetLastResponseInfo InternetGetLastResponseInfoW
#define InternetReadFileEx  InternetReadFileExW
#define FtpFindFirstFile FtpFindFirstFileW
#define FtpGetFile FtpGetFileW
#define FtpPutFile FtpPutFileW
#define FtpDeleteFile FtpDeleteFileW
#define FtpRenameFile FtpRenameFileW
#define FtpOpenFile FtpOpenFileW
#define FtpCreateDirectory FtpCreateDirectoryW
#define FtpRemoveDirectory FtpRemoveDirectoryW
#define FtpSetCurrentDirectory FtpSetCurrentDirectoryW
#define FtpGetCurrentDirectory FtpGetCurrentDirectoryW
#define FtpCommand FtpCommandW
#define GopherGetLocatorType GopherGetLocatorTypeW
#define GopherCreateLocator GopherCreateLocatorW
#define GopherFindFirstFile GopherFindFirstFileW
#define GopherOpenFile GopherOpenFileW
#define GopherGetAttribute GopherGetAttributeW
#define HttpSendRequest HttpSendRequestW
#define HttpOpenRequest HttpOpenRequestW
#define HttpAddRequestHeaders HttpAddRequestHeadersW
#define HttpQueryInfo HttpQueryInfoW
#define InternetSetCookie InternetSetCookieW
#define InternetGetCookie InternetGetCookieW
#define CreateUrlCacheEntry CreateUrlCacheEntryW
#define RetrieveUrlCacheEntryStream RetrieveUrlCacheEntryStreamW
#define FindNextUrlCacheEntry FindNextUrlCacheEntryW
#define CommitUrlCacheEntry CommitUrlCacheEntryW
#define GetUrlCacheEntryInfo GetUrlCacheEntryInfoW
#define SetUrlCacheEntryInfo SetUrlCacheEntryInfoW
#define FindFirstUrlCacheEntry FindFirstUrlCacheEntryW
#define RetrieveUrlCacheEntryFile RetrieveUrlCacheEntryFileW
#define HttpSendRequestEx	 HttpSendRequestExW
#define HttpEndRequest		 HttpEndRequestW
#define GetUrlCacheGroupAttribute  GetUrlCacheGroupAttributeW
#define SetUrlCacheGroupAttribute  SetUrlCacheGroupAttributeW
#else
#define GopherGetAttribute GopherGetAttributeA
#define InternetCrackUrl InternetCrackUrlA
#define InternetCreateUrl InternetCreateUrlA
#define InternetCanonicalizeUrl InternetCanonicalizeUrlA
#define InternetCombineUrl InternetCombineUrlA
#define InternetOpen InternetOpenA
#define InternetConnect InternetConnectA
#define InternetOpenUrl InternetOpenUrlA
#define InternetFindNextFile InternetFindNextFileA
#define InternetQueryOption InternetQueryOptionA
#define InternetSetOption InternetSetOptionA
#define InternetSetOptionEx InternetSetOptionExA
#define InternetGetLastResponseInfo InternetGetLastResponseInfoA
#define InternetReadFileEx  InternetReadFileExA
#define FtpFindFirstFile FtpFindFirstFileA
#define FtpGetFile FtpGetFileA
#define FtpPutFile FtpPutFileA
#define FtpDeleteFile FtpDeleteFileA
#define FtpRenameFile FtpRenameFileA
#define FtpOpenFile FtpOpenFileA
#define FtpCreateDirectory FtpCreateDirectoryA
#define FtpRemoveDirectory FtpRemoveDirectoryA
#define FtpSetCurrentDirectory FtpSetCurrentDirectoryA
#define FtpGetCurrentDirectory FtpGetCurrentDirectoryA
#define FtpCommand FtpCommandA
#define GopherGetLocatorType GopherGetLocatorTypeA
#define GopherCreateLocator GopherCreateLocatorA
#define GopherFindFirstFile GopherFindFirstFileA
#define GopherOpenFile GopherOpenFileA
#define HttpSendRequest HttpSendRequestA
#define HttpOpenRequest HttpOpenRequestA
#define HttpAddRequestHeaders HttpAddRequestHeadersA
#define HttpQueryInfo HttpQueryInfoA
#define InternetSetCookie InternetSetCookieA
#define InternetGetCookie InternetGetCookieA
#define CreateUrlCacheEntry CreateUrlCacheEntryA
#define RetrieveUrlCacheEntryStream RetrieveUrlCacheEntryStreamA
#define FindNextUrlCacheEntry FindNextUrlCacheEntryA
#define CommitUrlCacheEntry CommitUrlCacheEntryA
#define GetUrlCacheEntryInfo GetUrlCacheEntryInfoA
#define SetUrlCacheEntryInfo SetUrlCacheEntryInfoA
#define FindFirstUrlCacheEntry FindFirstUrlCacheEntryA
#define RetrieveUrlCacheEntryFile RetrieveUrlCacheEntryFileA
#define HttpSendRequestEx	 HttpSendRequestExA
#define HttpEndRequest		 HttpEndRequestA
#define GetUrlCacheGroupAttribute  GetUrlCacheGroupAttributeA
#define SetUrlCacheGroupAttribute  SetUrlCacheGroupAttributeA
#endif /* UNICODE */
#endif /* RC_INVOKED */
#ifdef __cplusplus
}
#endif
#endif
