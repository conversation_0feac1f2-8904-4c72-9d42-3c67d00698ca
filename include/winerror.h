#ifndef _WINERROR_H
#define _WINERROR_H
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#define ERROR_SUCCESS 0L
#define NO_ERROR 0L
#define ERROR_INVALID_FUNCTION 1L
#define ERROR_FILE_NOT_FOUND 2L
#define ERROR_PATH_NOT_FOUND 3L
#define ERROR_TOO_MANY_OPEN_FILES 4L
#define ERROR_ACCESS_DENIED 5L
#define ERROR_INVALID_HANDLE 6L
#define ERROR_ARENA_TRASHED 7L
#define ERROR_NOT_ENOUGH_MEMORY 8L
#define ERROR_INVALID_BLOCK 9L
#define ERROR_BAD_ENVIRONMENT 10L
#define ERROR_BAD_FORMAT 11L
#define ERROR_INVALID_ACCESS 12L
#define ERROR_INVALID_DATA 13L
#define ERROR_OUTOFMEMORY 14L
#define ERROR_INVALID_DRIVE 15L
#define ERROR_CURRENT_DIRECTORY 16L
#define ERROR_NOT_SAME_DEVICE 17L
#define ERROR_NO_MORE_FILES 18L
#define ERROR_WRITE_PROTECT 19L
#define ERROR_BAD_UNIT 20L
#define ERROR_NOT_READY 21L
#define ERROR_BAD_COMMAND 22L
#define ERROR_CRC 23L
#define ERROR_BAD_LENGTH 24L
#define ERROR_SEEK 25L
#define ERROR_NOT_DOS_DISK 26L
#define ERROR_SECTOR_NOT_FOUND 27L
#define ERROR_OUT_OF_PAPER 28L
#define ERROR_WRITE_FAULT 29L
#define ERROR_READ_FAULT 30L
#define ERROR_GEN_FAILURE 31L
#define ERROR_SHARING_VIOLATION 32L
#define ERROR_LOCK_VIOLATION 33L
#define ERROR_WRONG_DISK 34L
#define ERROR_SHARING_BUFFER_EXCEEDED 36L
#define ERROR_HANDLE_EOF 38L
#define ERROR_HANDLE_DISK_FULL 39L
#define ERROR_NOT_SUPPORTED 50L
#define ERROR_REM_NOT_LIST 51L
#define ERROR_DUP_NAME 52L
#define ERROR_BAD_NETPATH 53L
#define ERROR_NETWORK_BUSY 54L
#define ERROR_DEV_NOT_EXIST 55L
#define ERROR_TOO_MANY_CMDS 56L
#define ERROR_ADAP_HDW_ERR 57L
#define ERROR_BAD_NET_RESP 58L
#define ERROR_UNEXP_NET_ERR 59L
#define ERROR_BAD_REM_ADAP 60L
#define ERROR_PRINTQ_FULL 61L
#define ERROR_NO_SPOOL_SPACE 62L
#define ERROR_PRINT_CANCELLED 63L
#define ERROR_NETNAME_DELETED 64L
#define ERROR_NETWORK_ACCESS_DENIED 65L
#define ERROR_BAD_DEV_TYPE 66L
#define ERROR_BAD_NET_NAME 67L
#define ERROR_TOO_MANY_NAMES 68L
#define ERROR_TOO_MANY_SESS 69L
#define ERROR_SHARING_PAUSED 70L
#define ERROR_REQ_NOT_ACCEP 71L
#define ERROR_REDIR_PAUSED 72L
#define ERROR_FILE_EXISTS 80L
#define ERROR_CANNOT_MAKE 82L
#define ERROR_FAIL_I24 83L
#define ERROR_OUT_OF_STRUCTURES 84L
#define ERROR_ALREADY_ASSIGNED 85L
#define ERROR_INVALID_PASSWORD 86L
#define ERROR_INVALID_PARAMETER 87L
#define ERROR_NET_WRITE_FAULT 88L
#define ERROR_NO_PROC_SLOTS 89L
#define ERROR_TOO_MANY_SEMAPHORES 100L
#define ERROR_EXCL_SEM_ALREADY_OWNED 101L
#define ERROR_SEM_IS_SET 102L
#define ERROR_TOO_MANY_SEM_REQUESTS 103L
#define ERROR_INVALID_AT_INTERRUPT_TIME 104L
#define ERROR_SEM_OWNER_DIED 105L
#define ERROR_SEM_USER_LIMIT 106L
#define ERROR_DISK_CHANGE 107L
#define ERROR_DRIVE_LOCKED 108L
#define ERROR_BROKEN_PIPE 109L
#define ERROR_OPEN_FAILED 110L
#define ERROR_BUFFER_OVERFLOW 111L
#define ERROR_DISK_FULL 112L
#define ERROR_NO_MORE_SEARCH_HANDLES 113L
#define ERROR_INVALID_TARGET_HANDLE 114L
#define ERROR_INVALID_CATEGORY 117L
#define ERROR_INVALID_VERIFY_SWITCH 118L
#define ERROR_BAD_DRIVER_LEVEL 119L
#define ERROR_CALL_NOT_IMPLEMENTED 120L
#define ERROR_SEM_TIMEOUT 121L
#define ERROR_INSUFFICIENT_BUFFER 122L
#define ERROR_INVALID_NAME 123L
#define ERROR_INVALID_LEVEL 124L
#define ERROR_NO_VOLUME_LABEL 125L
#define ERROR_MOD_NOT_FOUND 126L
#define ERROR_PROC_NOT_FOUND 127L
#define ERROR_WAIT_NO_CHILDREN 128L
#define ERROR_CHILD_NOT_COMPLETE 129L
#define ERROR_DIRECT_ACCESS_HANDLE 130L
#define ERROR_NEGATIVE_SEEK 131L
#define ERROR_SEEK_ON_DEVICE 132L
#define ERROR_IS_JOIN_TARGET 133L
#define ERROR_IS_JOINED 134L
#define ERROR_IS_SUBSTED 135L
#define ERROR_NOT_JOINED 136L
#define ERROR_NOT_SUBSTED 137L
#define ERROR_JOIN_TO_JOIN 138L
#define ERROR_SUBST_TO_SUBST 139L
#define ERROR_JOIN_TO_SUBST 140L
#define ERROR_SUBST_TO_JOIN 141L
#define ERROR_BUSY_DRIVE 142L
#define ERROR_SAME_DRIVE 143L
#define ERROR_DIR_NOT_ROOT 144L
#define ERROR_DIR_NOT_EMPTY 145L
#define ERROR_IS_SUBST_PATH 146L
#define ERROR_IS_JOIN_PATH 147L
#define ERROR_PATH_BUSY 148L
#define ERROR_IS_SUBST_TARGET 149L
#define ERROR_SYSTEM_TRACE 150L
#define ERROR_INVALID_EVENT_COUNT 151L
#define ERROR_TOO_MANY_MUXWAITERS 152L
#define ERROR_INVALID_LIST_FORMAT 153L
#define ERROR_LABEL_TOO_LONG 154L
#define ERROR_TOO_MANY_TCBS 155L
#define ERROR_SIGNAL_REFUSED 156L
#define ERROR_DISCARDED 157L
#define ERROR_NOT_LOCKED 158L
#define ERROR_BAD_THREADID_ADDR 159L
#define ERROR_BAD_ARGUMENTS 160L
#define ERROR_BAD_PATHNAME 161L
#define ERROR_SIGNAL_PENDING 162L
#define ERROR_MAX_THRDS_REACHED 164L
#define ERROR_LOCK_FAILED 167L
#define ERROR_BUSY 170L
#define ERROR_CANCEL_VIOLATION 173L
#define ERROR_ATOMIC_LOCKS_NOT_SUPPORTED 174L
#define ERROR_INVALID_SEGMENT_NUMBER 180L
#define ERROR_INVALID_ORDINAL 182L
#define ERROR_ALREADY_EXISTS 183L
#define ERROR_INVALID_FLAG_NUMBER 186L
#define ERROR_SEM_NOT_FOUND 187L
#define ERROR_INVALID_STARTING_CODESEG 188L
#define ERROR_INVALID_STACKSEG 189L
#define ERROR_INVALID_MODULETYPE 190L
#define ERROR_INVALID_EXE_SIGNATURE 191L
#define ERROR_EXE_MARKED_INVALID 192L
#define ERROR_BAD_EXE_FORMAT 193L
#define ERROR_ITERATED_DATA_EXCEEDS_64k 194L
#define ERROR_INVALID_MINALLOCSIZE 195L
#define ERROR_DYNLINK_FROM_INVALID_RING 196L
#define ERROR_IOPL_NOT_ENABLED 197L
#define ERROR_INVALID_SEGDPL 198L
#define ERROR_AUTODATASEG_EXCEEDS_64k 199L
#define ERROR_RING2SEG_MUST_BE_MOVABLE 200L
#define ERROR_RELOC_CHAIN_XEEDS_SEGLIM 201L
#define ERROR_INFLOOP_IN_RELOC_CHAIN 202L
#define ERROR_ENVVAR_NOT_FOUND 203L
#define ERROR_NO_SIGNAL_SENT 205L
#define ERROR_FILENAME_EXCED_RANGE 206L
#define ERROR_RING2_STACK_IN_USE 207L
#define ERROR_META_EXPANSION_TOO_LONG 208L
#define ERROR_INVALID_SIGNAL_NUMBER 209L
#define ERROR_THREAD_1_INACTIVE 210L
#define ERROR_LOCKED 212L
#define ERROR_TOO_MANY_MODULES 214L
#define ERROR_NESTING_NOT_ALLOWED 215L
#define ERROR_EXE_MACHINE_TYPE_MISMATCH 216L
#define ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY 217L
#define ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY 218L
#define ERROR_BAD_PIPE 230L
#define ERROR_PIPE_BUSY 231L
#define ERROR_NO_DATA 232L
#define ERROR_PIPE_NOT_CONNECTED 233L
#define ERROR_MORE_DATA 234L
#define ERROR_VC_DISCONNECTED 240L
#define ERROR_INVALID_EA_NAME 254L
#define ERROR_EA_LIST_INCONSISTENT 255L
#ifndef WAIT_TIMEOUT /* also in winbase.h */
#define WAIT_TIMEOUT 258L
#endif
#define ERROR_NO_MORE_ITEMS 259L
#define ERROR_CANNOT_COPY 266L
#define ERROR_DIRECTORY 267L
#define ERROR_EAS_DIDNT_FIT 275L
#define ERROR_EA_FILE_CORRUPT 276L
#define ERROR_EA_TABLE_FULL 277L
#define ERROR_INVALID_EA_HANDLE 278L
#define ERROR_EAS_NOT_SUPPORTED 282L
#define ERROR_NOT_OWNER 288L
#define ERROR_TOO_MANY_POSTS 298L
#define ERROR_PARTIAL_COPY 299L
#define ERROR_OPLOCK_NOT_GRANTED 300L
#define ERROR_INVALID_OPLOCK_PROTOCOL 301L
#define ERROR_DISK_TOO_FRAGMENTED 302L
#define ERROR_DELETE_PENDING 303L
#define ERROR_MR_MID_NOT_FOUND 317L
#define ERROR_SCOPE_NOT_FOUND 318L
#define ERROR_INVALID_ADDRESS 487L
#define ERROR_ARITHMETIC_OVERFLOW 534L
#define ERROR_PIPE_CONNECTED 535L
#define ERROR_PIPE_LISTENING 536L
#define ERROR_EA_ACCESS_DENIED 994L
#define ERROR_OPERATION_ABORTED 995L
#define ERROR_IO_INCOMPLETE 996L
#define ERROR_IO_PENDING 997L
#define ERROR_NOACCESS 998L
#define ERROR_SWAPERROR 999L
#define ERROR_STACK_OVERFLOW 1001L
#define ERROR_INVALID_MESSAGE 1002L
#define ERROR_CAN_NOT_COMPLETE 1003L
#define ERROR_INVALID_FLAGS 1004L
#define ERROR_UNRECOGNIZED_VOLUME 1005L
#define ERROR_FILE_INVALID 1006L
#define ERROR_FULLSCREEN_MODE 1007L
#define ERROR_NO_TOKEN 1008L
#define ERROR_BADDB 1009L
#define ERROR_BADKEY 1010L
#define ERROR_CANTOPEN 1011L
#define ERROR_CANTREAD 1012L
#define ERROR_CANTWRITE 1013L
#define ERROR_REGISTRY_RECOVERED 1014L
#define ERROR_REGISTRY_CORRUPT 1015L
#define ERROR_REGISTRY_IO_FAILED 1016L
#define ERROR_NOT_REGISTRY_FILE 1017L
#define ERROR_KEY_DELETED 1018L
#define ERROR_NO_LOG_SPACE 1019L
#define ERROR_KEY_HAS_CHILDREN 1020L
#define ERROR_CHILD_MUST_BE_VOLATILE 1021L
#define ERROR_NOTIFY_ENUM_DIR 1022L
#define ERROR_DEPENDENT_SERVICES_RUNNING 1051L
#define ERROR_INVALID_SERVICE_CONTROL 1052L
#define ERROR_SERVICE_REQUEST_TIMEOUT 1053L
#define ERROR_SERVICE_NO_THREAD 1054L
#define ERROR_SERVICE_DATABASE_LOCKED 1055L
#define ERROR_SERVICE_ALREADY_RUNNING 1056L
#define ERROR_INVALID_SERVICE_ACCOUNT 1057L
#define ERROR_SERVICE_DISABLED 1058L
#define ERROR_CIRCULAR_DEPENDENCY 1059L
#define ERROR_SERVICE_DOES_NOT_EXIST 1060L
#define ERROR_SERVICE_CANNOT_ACCEPT_CTRL 1061L
#define ERROR_SERVICE_NOT_ACTIVE 1062L
#define ERROR_FAILED_SERVICE_CONTROLLER_CONNECT 1063L
#define ERROR_EXCEPTION_IN_SERVICE 1064L
#define ERROR_DATABASE_DOES_NOT_EXIST 1065L
#define ERROR_SERVICE_SPECIFIC_ERROR 1066L
#define ERROR_PROCESS_ABORTED 1067L
#define ERROR_SERVICE_DEPENDENCY_FAIL 1068L
#define ERROR_SERVICE_LOGON_FAILED 1069L
#define ERROR_SERVICE_START_HANG 1070L
#define ERROR_INVALID_SERVICE_LOCK 1071L
#define ERROR_SERVICE_MARKED_FOR_DELETE 1072L
#define ERROR_SERVICE_EXISTS 1073L
#define ERROR_ALREADY_RUNNING_LKG 1074L
#define ERROR_SERVICE_DEPENDENCY_DELETED 1075L
#define ERROR_BOOT_ALREADY_ACCEPTED 1076L
#define ERROR_SERVICE_NEVER_STARTED 1077L
#define ERROR_DUPLICATE_SERVICE_NAME 1078L
#define ERROR_DIFFERENT_SERVICE_ACCOUNT 1079L
#define ERROR_CANNOT_DETECT_DRIVER_FAILURE 1080L
#define ERROR_CANNOT_DETECT_PROCESS_ABORT 1081L
#define ERROR_NO_RECOVERY_PROGRAM 1082L
#define ERROR_SERVICE_NOT_IN_EXE 1083L
#define ERROR_NOT_SAFEBOOT_SERVICE 1084L
#define ERROR_END_OF_MEDIA 1100L
#define ERROR_FILEMARK_DETECTED 1101L
#define ERROR_BEGINNING_OF_MEDIA 1102L
#define ERROR_SETMARK_DETECTED 1103L
#define ERROR_NO_DATA_DETECTED 1104L
#define ERROR_PARTITION_FAILURE 1105L
#define ERROR_INVALID_BLOCK_LENGTH 1106L
#define ERROR_DEVICE_NOT_PARTITIONED 1107L
#define ERROR_UNABLE_TO_LOCK_MEDIA 1108L
#define ERROR_UNABLE_TO_UNLOAD_MEDIA 1109L
#define ERROR_MEDIA_CHANGED 1110L
#define ERROR_BUS_RESET 1111L
#define ERROR_NO_MEDIA_IN_DRIVE 1112L
#define ERROR_NO_UNICODE_TRANSLATION 1113L
#define ERROR_DLL_INIT_FAILED 1114L
#define ERROR_SHUTDOWN_IN_PROGRESS 1115L
#define ERROR_NO_SHUTDOWN_IN_PROGRESS 1116L
#define ERROR_IO_DEVICE 1117L
#define ERROR_SERIAL_NO_DEVICE 1118L
#define ERROR_IRQ_BUSY 1119L
#define ERROR_MORE_WRITES 1120L
#define ERROR_COUNTER_TIMEOUT 1121L
#define ERROR_FLOPPY_ID_MARK_NOT_FOUND 1122L
#define ERROR_FLOPPY_WRONG_CYLINDER 1123L
#define ERROR_FLOPPY_UNKNOWN_ERROR 1124L
#define ERROR_FLOPPY_BAD_REGISTERS 1125L
#define ERROR_DISK_RECALIBRATE_FAILED 1126L
#define ERROR_DISK_OPERATION_FAILED 1127L
#define ERROR_DISK_RESET_FAILED 1128L
#define ERROR_EOM_OVERFLOW 1129L
#define ERROR_NOT_ENOUGH_SERVER_MEMORY 1130L
#define ERROR_POSSIBLE_DEADLOCK 1131L
#define ERROR_MAPPED_ALIGNMENT 1132L
#define ERROR_SET_POWER_STATE_VETOED 1140L
#define ERROR_SET_POWER_STATE_FAILED 1141L
#define ERROR_TOO_MANY_LINKS 1142L
#define ERROR_OLD_WIN_VERSION 1150L
#define ERROR_APP_WRONG_OS 1151L
#define ERROR_SINGLE_INSTANCE_APP 1152L
#define ERROR_RMODE_APP 1153L
#define ERROR_INVALID_DLL 1154L
#define ERROR_NO_ASSOCIATION 1155L
#define ERROR_DDE_FAIL 1156L
#define ERROR_DLL_NOT_FOUND 1157L
#define ERROR_NO_MORE_USER_HANDLES 1158L
#define ERROR_MESSAGE_SYNC_ONLY 1159L
#define ERROR_SOURCE_ELEMENT_EMPTY 1160L
#define ERROR_DESTINATION_ELEMENT_FULL 1161L
#define ERROR_ILLEGAL_ELEMENT_ADDRESS 1162L
#define ERROR_MAGAZINE_NOT_PRESENT 1163L
#define ERROR_DEVICE_REINITIALIZATION_NEEDED 1164L
#define ERROR_DEVICE_REQUIRES_CLEANING 1165L
#define ERROR_DEVICE_DOOR_OPEN 1166L
#define ERROR_DEVICE_NOT_CONNECTED 1167L
#define ERROR_NOT_FOUND 1168L
#define ERROR_NO_MATCH 1169L
#define ERROR_SET_NOT_FOUND 1170L
#define ERROR_POINT_NOT_FOUND 1171L
#define ERROR_NO_TRACKING_SERVICE 1172L
#define ERROR_NO_VOLUME_ID 1173L
#define ERROR_UNABLE_TO_REMOVE_REPLACED 1175L
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT 1176L
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT_2 1177L
#define ERROR_JOURNAL_DELETE_IN_PROGRESS 1178L
#define ERROR_JOURNAL_NOT_ACTIVE 1179L
#define ERROR_POTENTIAL_FILE_FOUND 1180L
#define ERROR_JOURNAL_ENTRY_DELETED 1181L
#define ERROR_BAD_DEVICE 1200L
#define ERROR_CONNECTION_UNAVAIL 1201L
#define ERROR_DEVICE_ALREADY_REMEMBERED 1202L
#define ERROR_NO_NET_OR_BAD_PATH 1203L
#define ERROR_BAD_PROVIDER 1204L
#define ERROR_CANNOT_OPEN_PROFILE 1205L
#define ERROR_BAD_PROFILE 1206L
#define ERROR_NOT_CONTAINER 1207L
#define ERROR_EXTENDED_ERROR 1208L
#define ERROR_INVALID_GROUPNAME 1209L
#define ERROR_INVALID_COMPUTERNAME 1210L
#define ERROR_INVALID_EVENTNAME 1211L
#define ERROR_INVALID_DOMAINNAME 1212L
#define ERROR_INVALID_SERVICENAME 1213L
#define ERROR_INVALID_NETNAME 1214L
#define ERROR_INVALID_SHARENAME 1215L
#define ERROR_INVALID_PASSWORDNAME 1216L
#define ERROR_INVALID_MESSAGENAME 1217L
#define ERROR_INVALID_MESSAGEDEST 1218L
#define ERROR_SESSION_CREDENTIAL_CONFLICT 1219L
#define ERROR_REMOTE_SESSION_LIMIT_EXCEEDED 1220L
#define ERROR_DUP_DOMAINNAME 1221L
#define ERROR_NO_NETWORK 1222L
#define ERROR_CANCELLED 1223L
#define ERROR_USER_MAPPED_FILE 1224L
#define ERROR_CONNECTION_REFUSED 1225L
#define ERROR_GRACEFUL_DISCONNECT 1226L
#define ERROR_ADDRESS_ALREADY_ASSOCIATED 1227L
#define ERROR_ADDRESS_NOT_ASSOCIATED 1228L
#define ERROR_CONNECTION_INVALID 1229L
#define ERROR_CONNECTION_ACTIVE 1230L
#define ERROR_NETWORK_UNREACHABLE 1231L
#define ERROR_HOST_UNREACHABLE 1232L
#define ERROR_PROTOCOL_UNREACHABLE 1233L
#define ERROR_PORT_UNREACHABLE 1234L
#define ERROR_REQUEST_ABORTED 1235L
#define ERROR_CONNECTION_ABORTED 1236L
#define ERROR_RETRY 1237L
#define ERROR_CONNECTION_COUNT_LIMIT 1238L
#define ERROR_LOGIN_TIME_RESTRICTION 1239L
#define ERROR_LOGIN_WKSTA_RESTRICTION 1240L
#define ERROR_INCORRECT_ADDRESS 1241L
#define ERROR_ALREADY_REGISTERED 1242L
#define ERROR_SERVICE_NOT_FOUND 1243L
#define ERROR_NOT_AUTHENTICATED 1244L
#define ERROR_NOT_LOGGED_ON 1245L
#define ERROR_CONTINUE 1246L
#define ERROR_ALREADY_INITIALIZED 1247L
#define ERROR_NO_MORE_DEVICES 1248L
#define ERROR_NO_SUCH_SITE 1249L
#define ERROR_DOMAIN_CONTROLLER_EXISTS 1250L
#define ERROR_ONLY_IF_CONNECTED 1251L
#define ERROR_OVERRIDE_NOCHANGES 1252L
#define ERROR_BAD_USER_PROFILE 1253L
#define ERROR_NOT_SUPPORTED_ON_SBS 1254L
#define ERROR_SERVER_SHUTDOWN_IN_PROGRESS 1255L
#define ERROR_HOST_DOWN 1256L
#define ERROR_NON_ACCOUNT_SID 1257L
#define ERROR_NON_DOMAIN_SID 1258L
#define ERROR_APPHELP_BLOCK 1259L
#define ERROR_ACCESS_DISABLED_BY_POLICY 1260L
#define ERROR_REG_NAT_CONSUMPTION 1261L
#define ERROR_CSCSHARE_OFFLINE 1262L
#define ERROR_PKINIT_FAILURE 1263L
#define ERROR_SMARTCARD_SUBSYSTEM_FAILURE 1264L
#define ERROR_DOWNGRADE_DETECTED 1265L
#define SEC_E_SMARTCARD_CERT_REVOKED 1266L
#define SEC_E_ISSUING_CA_UNTRUSTED 1267L
#define SEC_E_REVOCATION_OFFLINE_C 1268L
#define SEC_E_PKINIT_CLIENT_FAILUR 1269L
#define SEC_E_SMARTCARD_CERT_EXPIRED 1270L
#define ERROR_MACHINE_LOCKED 1271L
#define ERROR_CALLBACK_SUPPLIED_INVALID_DATA 1273L
#define ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED 1274L
#define ERROR_DRIVER_BLOCKED 1275L
#define ERROR_INVALID_IMPORT_OF_NON_DLL 1276L
#define ERROR_ACCESS_DISABLED_WEBBLADE 1277L
#define ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER 1278L
#define ERROR_RECOVERY_FAILURE 1279L
#define ERROR_ALREADY_FIBER 1280L
#define ERROR_ALREADY_THREAD 1281L
#define ERROR_STACK_BUFFER_OVERRUN 1282L
#define ERROR_PARAMETER_QUOTA_EXCEEDED 1283L
#define ERROR_DEBUGGER_INACTIVE 1284L
#define ERROR_NOT_ALL_ASSIGNED 1300L
#define ERROR_SOME_NOT_MAPPED 1301L
#define ERROR_NO_QUOTAS_FOR_ACCOUNT 1302L
#define ERROR_LOCAL_USER_SESSION_KEY 1303L
#define ERROR_NULL_LM_PASSWORD 1304L
#define ERROR_UNKNOWN_REVISION 1305L
#define ERROR_REVISION_MISMATCH 1306L
#define ERROR_INVALID_OWNER 1307L
#define ERROR_INVALID_PRIMARY_GROUP 1308L
#define ERROR_NO_IMPERSONATION_TOKEN 1309L
#define ERROR_CANT_DISABLE_MANDATORY 1310L
#define ERROR_NO_LOGON_SERVERS 1311L
#define ERROR_NO_SUCH_LOGON_SESSION 1312L
#define ERROR_NO_SUCH_PRIVILEGE 1313L
#define ERROR_PRIVILEGE_NOT_HELD 1314L
#define ERROR_INVALID_ACCOUNT_NAME 1315L
#define ERROR_USER_EXISTS 1316L
#define ERROR_NO_SUCH_USER 1317L
#define ERROR_GROUP_EXISTS 1318L
#define ERROR_NO_SUCH_GROUP 1319L
#define ERROR_MEMBER_IN_GROUP 1320L
#define ERROR_MEMBER_NOT_IN_GROUP 1321L
#define ERROR_LAST_ADMIN 1322L
#define ERROR_WRONG_PASSWORD 1323L
#define ERROR_ILL_FORMED_PASSWORD 1324L
#define ERROR_PASSWORD_RESTRICTION 1325L
#define ERROR_LOGON_FAILURE 1326L
#define ERROR_ACCOUNT_RESTRICTION 1327L
#define ERROR_INVALID_LOGON_HOURS 1328L
#define ERROR_INVALID_WORKSTATION 1329L
#define ERROR_PASSWORD_EXPIRED 1330L
#define ERROR_ACCOUNT_DISABLED 1331L
#define ERROR_NONE_MAPPED 1332L
#define ERROR_TOO_MANY_LUIDS_REQUESTED 1333L
#define ERROR_LUIDS_EXHAUSTED 1334L
#define ERROR_INVALID_SUB_AUTHORITY 1335L
#define ERROR_INVALID_ACL 1336L
#define ERROR_INVALID_SID 1337L
#define ERROR_INVALID_SECURITY_DESCR 1338L
#define ERROR_BAD_INHERITANCE_ACL 1340L
#define ERROR_SERVER_DISABLED 1341L
#define ERROR_SERVER_NOT_DISABLED 1342L
#define ERROR_INVALID_ID_AUTHORITY 1343L
#define ERROR_ALLOTTED_SPACE_EXCEEDED 1344L
#define ERROR_INVALID_GROUP_ATTRIBUTES 1345L
#define ERROR_BAD_IMPERSONATION_LEVEL 1346L
#define ERROR_CANT_OPEN_ANONYMOUS 1347L
#define ERROR_BAD_VALIDATION_CLASS 1348L
#define ERROR_BAD_TOKEN_TYPE 1349L
#define ERROR_NO_SECURITY_ON_OBJECT 1350L
#define ERROR_CANT_ACCESS_DOMAIN_INFO 1351L
#define ERROR_INVALID_SERVER_STATE 1352L
#define ERROR_INVALID_DOMAIN_STATE 1353L
#define ERROR_INVALID_DOMAIN_ROLE 1354L
#define ERROR_NO_SUCH_DOMAIN 1355L
#define ERROR_DOMAIN_EXISTS 1356L
#define ERROR_DOMAIN_LIMIT_EXCEEDED 1357L
#define ERROR_INTERNAL_DB_CORRUPTION 1358L
#define ERROR_INTERNAL_ERROR 1359L
#define ERROR_GENERIC_NOT_MAPPED 1360L
#define ERROR_BAD_DESCRIPTOR_FORMAT 1361L
#define ERROR_NOT_LOGON_PROCESS 1362L
#define ERROR_LOGON_SESSION_EXISTS 1363L
#define ERROR_NO_SUCH_PACKAGE 1364L
#define ERROR_BAD_LOGON_SESSION_STATE 1365L
#define ERROR_LOGON_SESSION_COLLISION 1366L
#define ERROR_INVALID_LOGON_TYPE 1367L
#define ERROR_CANNOT_IMPERSONATE 1368L
#define ERROR_RXACT_INVALID_STATE 1369L
#define ERROR_RXACT_COMMIT_FAILURE 1370L
#define ERROR_SPECIAL_ACCOUNT 1371L
#define ERROR_SPECIAL_GROUP 1372L
#define ERROR_SPECIAL_USER 1373L
#define ERROR_MEMBERS_PRIMARY_GROUP 1374L
#define ERROR_TOKEN_ALREADY_IN_USE 1375L
#define ERROR_NO_SUCH_ALIAS 1376L
#define ERROR_MEMBER_NOT_IN_ALIAS 1377L
#define ERROR_MEMBER_IN_ALIAS 1378L
#define ERROR_ALIAS_EXISTS 1379L
#define ERROR_LOGON_NOT_GRANTED 1380L
#define ERROR_TOO_MANY_SECRETS 1381L
#define ERROR_SECRET_TOO_LONG 1382L
#define ERROR_INTERNAL_DB_ERROR 1383L
#define ERROR_TOO_MANY_CONTEXT_IDS 1384L
#define ERROR_LOGON_TYPE_NOT_GRANTED 1385L
#define ERROR_NT_CROSS_ENCRYPTION_REQUIRED 1386L
#define ERROR_NO_SUCH_MEMBER 1387L
#define ERROR_INVALID_MEMBER 1388L
#define ERROR_TOO_MANY_SIDS 1389L
#define ERROR_LM_CROSS_ENCRYPTION_REQUIRED 1390L
#define ERROR_NO_INHERITANCE 1391L
#define ERROR_FILE_CORRUPT 1392L
#define ERROR_DISK_CORRUPT 1393L
#define ERROR_NO_USER_SESSION_KEY 1394L
#define ERROR_LICENSE_QUOTA_EXCEEDED 1395L
#define ERROR_WRONG_TARGET_NAME 1396L
#define ERROR_MUTUAL_AUTH_FAILED 1397L
#define ERROR_TIME_SKEW 1398L
#define ERROR_CURRENT_DOMAIN_NOT_ALLOWED 1399L
#define ERROR_INVALID_WINDOW_HANDLE 1400L
#define ERROR_INVALID_MENU_HANDLE 1401L
#define ERROR_INVALID_CURSOR_HANDLE 1402L
#define ERROR_INVALID_ACCEL_HANDLE 1403L
#define ERROR_INVALID_HOOK_HANDLE 1404L
#define ERROR_INVALID_DWP_HANDLE 1405L
#define ERROR_TLW_WITH_WSCHILD 1406L
#define ERROR_CANNOT_FIND_WND_CLASS 1407L
#define ERROR_WINDOW_OF_OTHER_THREAD 1408L
#define ERROR_HOTKEY_ALREADY_REGISTERED 1409L
#define ERROR_CLASS_ALREADY_EXISTS 1410L
#define ERROR_CLASS_DOES_NOT_EXIST 1411L
#define ERROR_CLASS_HAS_WINDOWS 1412L
#define ERROR_INVALID_INDEX 1413L
#define ERROR_INVALID_ICON_HANDLE 1414L
#define ERROR_PRIVATE_DIALOG_INDEX 1415L
#define ERROR_LISTBOX_ID_NOT_FOUND 1416L
#define ERROR_NO_WILDCARD_CHARACTERS 1417L
#define ERROR_CLIPBOARD_NOT_OPEN 1418L
#define ERROR_HOTKEY_NOT_REGISTERED 1419L
#define ERROR_WINDOW_NOT_DIALOG 1420L
#define ERROR_CONTROL_ID_NOT_FOUND 1421L
#define ERROR_INVALID_COMBOBOX_MESSAGE 1422L
#define ERROR_WINDOW_NOT_COMBOBOX 1423L
#define ERROR_INVALID_EDIT_HEIGHT 1424L
#define ERROR_DC_NOT_FOUND 1425L
#define ERROR_INVALID_HOOK_FILTER 1426L
#define ERROR_INVALID_FILTER_PROC 1427L
#define ERROR_HOOK_NEEDS_HMOD 1428L
#define ERROR_GLOBAL_ONLY_HOOK 1429L
#define ERROR_JOURNAL_HOOK_SET 1430L
#define ERROR_HOOK_NOT_INSTALLED 1431L
#define ERROR_INVALID_LB_MESSAGE 1432L
#define ERROR_SETCOUNT_ON_BAD_LB 1433L
#define ERROR_LB_WITHOUT_TABSTOPS 1434L
#define ERROR_DESTROY_OBJECT_OF_OTHER_THREAD 1435L
#define ERROR_CHILD_WINDOW_MENU 1436L
#define ERROR_NO_SYSTEM_MENU 1437L
#define ERROR_INVALID_MSGBOX_STYLE 1438L
#define ERROR_INVALID_SPI_VALUE 1439L
#define ERROR_SCREEN_ALREADY_LOCKED 1440L
#define ERROR_HWNDS_HAVE_DIFF_PARENT 1441L
#define ERROR_NOT_CHILD_WINDOW 1442L
#define ERROR_INVALID_GW_COMMAND 1443L
#define ERROR_INVALID_THREAD_ID 1444L
#define ERROR_NON_MDICHILD_WINDOW 1445L
#define ERROR_POPUP_ALREADY_ACTIVE 1446L
#define ERROR_NO_SCROLLBARS 1447L
#define ERROR_INVALID_SCROLLBAR_RANGE 1448L
#define ERROR_INVALID_SHOWWIN_COMMAND 1449L
#define ERROR_NO_SYSTEM_RESOURCES 1450L
#define ERROR_NONPAGED_SYSTEM_RESOURCES 1451L
#define ERROR_PAGED_SYSTEM_RESOURCES 1452L
#define ERROR_WORKING_SET_QUOTA 1453L
#define ERROR_PAGEFILE_QUOTA 1454L
#define ERROR_COMMITMENT_LIMIT 1455L
#define ERROR_MENU_ITEM_NOT_FOUND 1456L
#define ERROR_INVALID_KEYBOARD_HANDLE 1457L
#define ERROR_HOOK_TYPE_NOT_ALLOWED 1458L
#define ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION 1459L
#define ERROR_TIMEOUT 1460L
#define ERROR_INVALID_MONITOR_HANDLE 1461L
#define ERROR_EVENTLOG_FILE_CORRUPT 1500L
#define ERROR_EVENTLOG_CANT_START 1501L
#define ERROR_LOG_FILE_FULL 1502L
#define ERROR_EVENTLOG_FILE_CHANGED 1503L
#define ERROR_INSTALL_SERVICE_FAILURE 1601L
#define ERROR_INSTALL_USEREXIT 1602L
#define ERROR_INSTALL_FAILURE 1603L
#define ERROR_INSTALL_SUSPEND 1604L
#define ERROR_UNKNOWN_PRODUCT 1605L
#define ERROR_UNKNOWN_FEATURE 1606L
#define ERROR_UNKNOWN_COMPONENT 1607L
#define ERROR_UNKNOWN_PROPERTY 1608L
#define ERROR_INVALID_HANDLE_STATE 1609L
#define ERROR_BAD_CONFIGURATION 1610L
#define ERROR_INDEX_ABSENT 1611L
#define ERROR_INSTALL_SOURCE_ABSENT 1612L
#define ERROR_INSTALL_PACKAGE_VERSION 1613L
#define ERROR_PRODUCT_UNINSTALLED 1614L
#define ERROR_BAD_QUERY_SYNTAX 1615L
#define ERROR_INVALID_FIELD 1616L
#define ERROR_DEVICE_REMOVED 1617L
#define ERROR_INSTALL_ALREADY_RUNNING 1618L
#define ERROR_INSTALL_PACKAGE_OPEN_FAILED 1619L
#define ERROR_INSTALL_PACKAGE_INVALID 1620L
#define ERROR_INSTALL_UI_FAILURE 1621L
#define ERROR_INSTALL_LOG_FAILURE 1622L
#define ERROR_INSTALL_LANGUAGE_UNSUPPORTED 1623L
#define ERROR_INSTALL_TRANSFORM_FAILURE 1624L
#define ERROR_INSTALL_PACKAGE_REJECTED 1625L
#define ERROR_FUNCTION_NOT_CALLED 1626L
#define ERROR_FUNCTION_FAILED 1627L
#define ERROR_INVALID_TABLE 1628L
#define ERROR_DATATYPE_MISMATCH 1629L
#define ERROR_UNSUPPORTED_TYPE 1630L
#define ERROR_CREATE_FAILED 1631L
#define ERROR_INSTALL_TEMP_UNWRITABLE 1632L
#define ERROR_INSTALL_PLATFORM_UNSUPPORTED 1633L
#define ERROR_INSTALL_NOTUSED 1634L
#define ERROR_PATCH_PACKAGE_OPEN_FAILED 1635L
#define ERROR_PATCH_PACKAGE_INVALID 1636L
#define ERROR_PATCH_PACKAGE_UNSUPPORTED 1637L
#define ERROR_PRODUCT_VERSION 1638L
#define ERROR_INVALID_COMMAND_LINE 1639L
#define ERROR_INSTALL_REMOTE_DISALLOWED 1640L
#define ERROR_SUCCESS_REBOOT_INITIATED 1641L
#define ERROR_PATCH_TARGET_NOT_FOUND 1642L
#define ERROR_PATCH_PACKAGE_REJECTED 1643L
#define ERROR_INSTALL_TRANSFORM_REJECTED 1644L
#define ERROR_INSTALL_REMOTE_PROHIBITED 1645L
#define RPC_S_INVALID_STRING_BINDING 1700L
#define RPC_S_WRONG_KIND_OF_BINDING 1701L
#define RPC_S_INVALID_BINDING 1702L
#define RPC_S_PROTSEQ_NOT_SUPPORTED 1703L
#define RPC_S_INVALID_RPC_PROTSEQ 1704L
#define RPC_S_INVALID_STRING_UUID 1705L
#define RPC_S_INVALID_ENDPOINT_FORMAT 1706L
#define RPC_S_INVALID_NET_ADDR 1707L
#define RPC_S_NO_ENDPOINT_FOUND 1708L
#define RPC_S_INVALID_TIMEOUT 1709L
#define RPC_S_OBJECT_NOT_FOUND 1710L
#define RPC_S_ALREADY_REGISTERED 1711L
#define RPC_S_TYPE_ALREADY_REGISTERED 1712L
#define RPC_S_ALREADY_LISTENING 1713L
#define RPC_S_NO_PROTSEQS_REGISTERED 1714L
#define RPC_S_NOT_LISTENING 1715L
#define RPC_S_UNKNOWN_MGR_TYPE 1716L
#define RPC_S_UNKNOWN_IF 1717L
#define RPC_S_NO_BINDINGS 1718L
#define RPC_S_NO_PROTSEQS 1719L
#define RPC_S_CANT_CREATE_ENDPOINT 1720L
#define RPC_S_OUT_OF_RESOURCES 1721L
#define RPC_S_SERVER_UNAVAILABLE 1722L
#define RPC_S_SERVER_TOO_BUSY 1723L
#define RPC_S_INVALID_NETWORK_OPTIONS 1724L
#define RPC_S_NO_CALL_ACTIVE 1725L
#define RPC_S_CALL_FAILED 1726L
#define RPC_S_CALL_FAILED_DNE 1727L
#define RPC_S_PROTOCOL_ERROR 1728L
#define RPC_S_UNSUPPORTED_TRANS_SYN 1730L
#define RPC_S_UNSUPPORTED_TYPE 1732L
#define RPC_S_INVALID_TAG 1733L
#define RPC_S_INVALID_BOUND 1734L
#define RPC_S_NO_ENTRY_NAME 1735L
#define RPC_S_INVALID_NAME_SYNTAX 1736L
#define RPC_S_UNSUPPORTED_NAME_SYNTAX 1737L
#define RPC_S_UUID_NO_ADDRESS 1739L
#define RPC_S_DUPLICATE_ENDPOINT 1740L
#define RPC_S_UNKNOWN_AUTHN_TYPE 1741L
#define RPC_S_MAX_CALLS_TOO_SMALL 1742L
#define RPC_S_STRING_TOO_LONG 1743L
#define RPC_S_PROTSEQ_NOT_FOUND 1744L
#define RPC_S_PROCNUM_OUT_OF_RANGE 1745L
#define RPC_S_BINDING_HAS_NO_AUTH 1746L
#define RPC_S_UNKNOWN_AUTHN_SERVICE 1747L
#define RPC_S_UNKNOWN_AUTHN_LEVEL 1748L
#define RPC_S_INVALID_AUTH_IDENTITY 1749L
#define RPC_S_UNKNOWN_AUTHZ_SERVICE 1750L
#define EPT_S_INVALID_ENTRY 1751L
#define EPT_S_CANT_PERFORM_OP 1752L
#define EPT_S_NOT_REGISTERED 1753L
#define RPC_S_NOTHING_TO_EXPORT 1754L
#define RPC_S_INCOMPLETE_NAME 1755L
#define RPC_S_INVALID_VERS_OPTION 1756L
#define RPC_S_NO_MORE_MEMBERS 1757L
#define RPC_S_NOT_ALL_OBJS_UNEXPORTED 1758L
#define RPC_S_INTERFACE_NOT_FOUND 1759L
#define RPC_S_ENTRY_ALREADY_EXISTS 1760L
#define RPC_S_ENTRY_NOT_FOUND 1761L
#define RPC_S_NAME_SERVICE_UNAVAILABLE 1762L
#define RPC_S_INVALID_NAF_ID 1763L
#define RPC_S_CANNOT_SUPPORT 1764L
#define RPC_S_NO_CONTEXT_AVAILABLE 1765L
#define RPC_S_INTERNAL_ERROR 1766L
#define RPC_S_ZERO_DIVIDE 1767L
#define RPC_S_ADDRESS_ERROR 1768L
#define RPC_S_FP_DIV_ZERO 1769L
#define RPC_S_FP_UNDERFLOW 1770L
#define RPC_S_FP_OVERFLOW 1771L
#define RPC_X_NO_MORE_ENTRIES 1772L
#define RPC_X_SS_CHAR_TRANS_OPEN_FAIL 1773L
#define RPC_X_SS_CHAR_TRANS_SHORT_FILE 1774L
#define RPC_X_SS_IN_NULL_CONTEXT 1775L
#define RPC_X_SS_CONTEXT_DAMAGED 1777L
#define RPC_X_SS_HANDLES_MISMATCH 1778L
#define RPC_X_SS_CANNOT_GET_CALL_HANDLE 1779L
#define RPC_X_NULL_REF_POINTER 1780L
#define RPC_X_ENUM_VALUE_OUT_OF_RANGE 1781L
#define RPC_X_BYTE_COUNT_TOO_SMALL 1782L
#define RPC_X_BAD_STUB_DATA 1783L
#define ERROR_INVALID_USER_BUFFER 1784L
#define ERROR_UNRECOGNIZED_MEDIA 1785L
#define ERROR_NO_TRUST_LSA_SECRET 1786L
#define ERROR_NO_TRUST_SAM_ACCOUNT 1787L
#define ERROR_TRUSTED_DOMAIN_FAILURE 1788L
#define ERROR_TRUSTED_RELATIONSHIP_FAILURE 1789L
#define ERROR_TRUST_FAILURE 1790L
#define RPC_S_CALL_IN_PROGRESS 1791L
#define ERROR_NETLOGON_NOT_STARTED 1792L
#define ERROR_ACCOUNT_EXPIRED 1793L
#define ERROR_REDIRECTOR_HAS_OPEN_HANDLES 1794L
#define ERROR_PRINTER_DRIVER_ALREADY_INSTALLED 1795L
#define ERROR_UNKNOWN_PORT 1796L
#define ERROR_UNKNOWN_PRINTER_DRIVER 1797L
#define ERROR_UNKNOWN_PRINTPROCESSOR 1798L
#define ERROR_INVALID_SEPARATOR_FILE 1799L
#define ERROR_INVALID_PRIORITY 1800L
#define ERROR_INVALID_PRINTER_NAME 1801L
#define ERROR_PRINTER_ALREADY_EXISTS 1802L
#define ERROR_INVALID_PRINTER_COMMAND 1803L
#define ERROR_INVALID_DATATYPE 1804L
#define ERROR_INVALID_ENVIRONMENT 1805L
#define RPC_S_NO_MORE_BINDINGS 1806L
#define ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT 1807L
#define ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT 1808L
#define ERROR_NOLOGON_SERVER_TRUST_ACCOUNT 1809L
#define ERROR_DOMAIN_TRUST_INCONSISTENT 1810L
#define ERROR_SERVER_HAS_OPEN_HANDLES 1811L
#define ERROR_RESOURCE_DATA_NOT_FOUND 1812L
#define ERROR_RESOURCE_TYPE_NOT_FOUND 1813L
#define ERROR_RESOURCE_NAME_NOT_FOUND 1814L
#define ERROR_RESOURCE_LANG_NOT_FOUND 1815L
#define ERROR_NOT_ENOUGH_QUOTA 1816L
#define RPC_S_NO_INTERFACES 1817L
#define RPC_S_CALL_CANCELLED 1818L
#define RPC_S_BINDING_INCOMPLETE 1819L
#define RPC_S_COMM_FAILURE 1820L
#define RPC_S_UNSUPPORTED_AUTHN_LEVEL 1821L
#define RPC_S_NO_PRINC_NAME 1822L
#define RPC_S_NOT_RPC_ERROR 1823L
#define RPC_S_UUID_LOCAL_ONLY 1824L
#define RPC_S_SEC_PKG_ERROR 1825L
#define RPC_S_NOT_CANCELLED 1826L
#define RPC_X_INVALID_ES_ACTION 1827L
#define RPC_X_WRONG_ES_VERSION 1828L
#define RPC_X_WRONG_STUB_VERSION 1829L
#define RPC_X_INVALID_PIPE_OBJECT 1830L
#define RPC_X_WRONG_PIPE_ORDER 1831L
#define RPC_X_WRONG_PIPE_VERSION 1832L
#define RPC_S_GROUP_MEMBER_NOT_FOUND 1898L
#define EPT_S_CANT_CREATE 1899L
#define RPC_S_INVALID_OBJECT 1900L
#define ERROR_INVALID_TIME 1901L
#define ERROR_INVALID_FORM_NAME 1902L
#define ERROR_INVALID_FORM_SIZE 1903L
#define ERROR_ALREADY_WAITING 1904L
#define ERROR_PRINTER_DELETED 1905L
#define ERROR_INVALID_PRINTER_STATE 1906L
#define ERROR_PASSWORD_MUST_CHANGE 1907L
#define ERROR_DOMAIN_CONTROLLER_NOT_FOUND 1908L
#define ERROR_ACCOUNT_LOCKED_OUT 1909L
#define OR_INVALID_OXID 1910L
#define OR_INVALID_OID 1911L
#define OR_INVALID_SET 1912L
#define RPC_S_SEND_INCOMPLETE 1913L
#define RPC_S_INVALID_ASYNC_HANDLE 1914L
#define RPC_S_INVALID_ASYNC_CALL 1915L
#define RPC_X_PIPE_CLOSED 1916L
#define RPC_X_PIPE_DISCIPLINE_ERROR 1917L
#define RPC_X_PIPE_EMPTY 1918L
#define ERROR_NO_SITENAME 1919L
#define ERROR_CANT_ACCESS_FILE 1920L
#define ERROR_CANT_RESOLVE_FILENAME 1921L
#define RPC_S_ENTRY_TYPE_MISMATCH 1922L
#define RPC_S_NOT_ALL_OBJS_EXPORTED 1923L
#define RPC_S_INTERFACE_NOT_EXPORTED 1924L
#define RPC_S_PROFILE_NOT_ADDED 1925L
#define RPC_S_PRF_ELT_NOT_ADDED 1926L
#define RPC_S_PRF_ELT_NOT_REMOVED 1927L
#define RPC_S_GRP_ELT_NOT_ADDED 1928L
#define RPC_S_GRP_ELT_NOT_REMOVED 1929L
#define ERROR_KM_DRIVER_BLOCKED 1930L
#define ERROR_CONTEXT_EXPIRED 1931L
#define ERROR_PER_USER_TRUST_QUOTA_EXCEEDED 1932L
#define ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED 1933L
#define ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED 1934L
#define ERROR_INVALID_PIXEL_FORMAT 2000L
#define ERROR_BAD_DRIVER 2001L
#define ERROR_INVALID_WINDOW_STYLE 2002L
#define ERROR_METAFILE_NOT_SUPPORTED 2003L
#define ERROR_TRANSFORM_NOT_SUPPORTED 2004L
#define ERROR_CLIPPING_NOT_SUPPORTED 2005L
#define ERROR_INVALID_CMM 2010L
#define ERROR_INVALID_PROFILE 2011L
#define ERROR_TAG_NOT_FOUND 2012L
#define ERROR_TAG_NOT_PRESENT 2013L
#define ERROR_DUPLICATE_TAG 2014L
#define ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE 2015L
#define ERROR_PROFILE_NOT_FOUND 2016L
#define ERROR_INVALID_COLORSPACE 2017L
#define ERROR_ICM_NOT_ENABLED 2018L
#define ERROR_DELETING_ICM_XFORM 2019L
#define ERROR_INVALID_TRANSFORM 2020L
#define ERROR_COLORSPACE_MISMATCH 2021L
#define ERROR_INVALID_COLORINDEX 2022L
#define ERROR_CONNECTED_OTHER_PASSWORD 2108L
#define ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT 2109L
#define ERROR_BAD_USERNAME 2202L
#define ERROR_NOT_CONNECTED 2250L
#define ERROR_OPEN_FILES 2401L
#define ERROR_ACTIVE_CONNECTIONS 2402L
#define ERROR_DEVICE_IN_USE 2404L
#define ERROR_UNKNOWN_PRINT_MONITOR 3000L
#define ERROR_PRINTER_DRIVER_IN_USE 3001L
#define ERROR_SPOOL_FILE_NOT_FOUND 3002L
#define ERROR_SPL_NO_STARTDOC 3003L
#define ERROR_SPL_NO_ADDJOB 3004L
#define ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED 3005L
#define ERROR_PRINT_MONITOR_ALREADY_INSTALLED 3006L
#define ERROR_INVALID_PRINT_MONITOR 3007L
#define ERROR_PRINT_MONITOR_IN_USE 3008L
#define ERROR_PRINTER_HAS_JOBS_QUEUED 3009L
#define ERROR_SUCCESS_REBOOT_REQUIRED 3010L
#define ERROR_SUCCESS_RESTART_REQUIRED 3011L
#define ERROR_PRINTER_NOT_FOUND 3012L
#define ERROR_PRINTER_DRIVER_WARNED 3013L
#define ERROR_PRINTER_DRIVER_BLOCKED 3014L
#define ERROR_WINS_INTERNAL 4000L
#define ERROR_CAN_NOT_DEL_LOCAL_WINS 4001L
#define ERROR_STATIC_INIT 4002L
#define ERROR_INC_BACKUP 4003L
#define ERROR_FULL_BACKUP 4004L
#define ERROR_REC_NON_EXISTENT 4005L
#define ERROR_RPL_NOT_ALLOWED 4006L
#define ERROR_DHCP_ADDRESS_CONFLICT 4100L
#define ERROR_WMI_GUID_NOT_FOUND 4200L
#define ERROR_WMI_INSTANCE_NOT_FOUND 4201L
#define ERROR_WMI_ITEMID_NOT_FOUND 4202L
#define ERROR_WMI_TRY_AGAIN 4203L
#define ERROR_WMI_DP_NOT_FOUND 4204L
#define ERROR_WMI_UNRESOLVED_INSTANCE_REF 4205L
#define ERROR_WMI_ALREADY_ENABLED 4206L
#define ERROR_WMI_GUID_DISCONNECTED 4207L
#define ERROR_WMI_SERVER_UNAVAILABLE 4208L
#define ERROR_WMI_DP_FAILED 4209L
#define ERROR_WMI_INVALID_MOF 4210L
#define ERROR_WMI_INVALID_REGINFO 4211L
#define ERROR_WMI_ALREADY_DISABLED 4212L
#define ERROR_WMI_READ_ONLY 4213L
#define ERROR_WMI_SET_FAILURE 4214L
#define ERROR_INVALID_MEDIA 4300L
#define ERROR_INVALID_LIBRARY 4301L
#define ERROR_INVALID_MEDIA_POOL 4302L
#define ERROR_DRIVE_MEDIA_MISMATCH 4303L
#define ERROR_MEDIA_OFFLINE 4304L
#define ERROR_LIBRARY_OFFLINE 4305L
#define ERROR_EMPTY 4306L
#define ERROR_NOT_EMPTY 4307L
#define ERROR_MEDIA_UNAVAILABLE 4308L
#define ERROR_RESOURCE_DISABLED 4309L
#define ERROR_INVALID_CLEANER 4310L
#define ERROR_UNABLE_TO_CLEAN 4311L
#define ERROR_OBJECT_NOT_FOUND 4312L
#define ERROR_DATABASE_FAILURE 4313L
#define ERROR_DATABASE_FULL 4314L
#define ERROR_MEDIA_INCOMPATIBLE 4315L
#define ERROR_RESOURCE_NOT_PRESENT 4316L
#define ERROR_INVALID_OPERATION 4317L
#define ERROR_MEDIA_NOT_AVAILABLE 4318L
#define ERROR_DEVICE_NOT_AVAILABLE 4319L
#define ERROR_REQUEST_REFUSED 4320L
#define ERROR_INVALID_DRIVE_OBJECT 4321L
#define ERROR_LIBRARY_FULL 4322L
#define ERROR_MEDIUM_NOT_ACCESSIBLE 4323L
#define ERROR_UNABLE_TO_LOAD_MEDIUM 4324L
#define ERROR_UNABLE_TO_INVENTORY_DRIVE 4325L
#define ERROR_UNABLE_TO_INVENTORY_SLOT 4326L
#define ERROR_UNABLE_TO_INVENTORY_TRANSPORT 4327L
#define ERROR_TRANSPORT_FULL 4328L
#define ERROR_CONTROLLING_IEPORT 4329L
#define ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA 4330L
#define ERROR_CLEANER_SLOT_SET 4331L
#define ERROR_CLEANER_SLOT_NOT_SET 4332L
#define ERROR_CLEANER_CARTRIDGE_SPENT 4333L
#define ERROR_UNEXPECTED_OMID 4334L
#define ERROR_CANT_DELETE_LAST_ITEM 4335L
#define ERROR_MESSAGE_EXCEEDS_MAX_SIZE 4336L
#define ERROR_VOLUME_CONTAINS_SYS_FILES 4337L
#define ERROR_INDIGENOUS_TYPE 4338L
#define ERROR_NO_SUPPORTING_DRIVES 4339L
#define ERROR_CLEANER_CARTRIDGE_INSTALLED 4340L
#define ERROR_FILE_OFFLINE 4350L
#define ERROR_REMOTE_STORAGE_NOT_ACTIVE 4351L
#define ERROR_REMOTE_STORAGE_MEDIA_ERROR 4352L
#define ERROR_NOT_A_REPARSE_POINT 4390L
#define ERROR_REPARSE_ATTRIBUTE_CONFLICT 4391L
#define ERROR_INVALID_REPARSE_DATA 4392L
#define ERROR_REPARSE_TAG_INVALID 4393L
#define ERROR_REPARSE_TAG_MISMATCH 4394L
#define ERROR_VOLUME_NOT_SIS_ENABLED 4500L
#define ERROR_DEPENDENT_RESOURCE_EXISTS 5001L
#define ERROR_DEPENDENCY_NOT_FOUND 5002L
#define ERROR_DEPENDENCY_ALREADY_EXISTS 5003L
#define ERROR_RESOURCE_NOT_ONLINE 5004L
#define ERROR_HOST_NODE_NOT_AVAILABLE 5005L
#define ERROR_RESOURCE_NOT_AVAILABLE 5006L
#define ERROR_RESOURCE_NOT_FOUND 5007L
#define ERROR_SHUTDOWN_CLUSTER 5008L
#define ERROR_CANT_EVICT_ACTIVE_NODE 5009L
#define ERROR_OBJECT_ALREADY_EXISTS 5010L
#define ERROR_OBJECT_IN_LIST 5011L
#define ERROR_GROUP_NOT_AVAILABLE 5012L
#define ERROR_GROUP_NOT_FOUND 5013L
#define ERROR_GROUP_NOT_ONLINE 5014L
#define ERROR_HOST_NODE_NOT_RESOURCE_OWNER 5015L
#define ERROR_HOST_NODE_NOT_GROUP_OWNER 5016L
#define ERROR_RESMON_CREATE_FAILED 5017L
#define ERROR_RESMON_ONLINE_FAILED 5018L
#define ERROR_RESOURCE_ONLINE 5019L
#define ERROR_QUORUM_RESOURCE 5020L
#define ERROR_NOT_QUORUM_CAPABLE 5021L
#define ERROR_CLUSTER_SHUTTING_DOWN 5022L
#define ERROR_INVALID_STATE 5023L
#define ERROR_RESOURCE_PROPERTIES_STORED 5024L
#define ERROR_NOT_QUORUM_CLASS 5025L
#define ERROR_CORE_RESOURCE 5026L
#define ERROR_QUORUM_RESOURCE_ONLINE_FAILED 5027L
#define ERROR_QUORUMLOG_OPEN_FAILED 5028L
#define ERROR_CLUSTERLOG_CORRUPT 5029L
#define ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE 5030L
#define ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE 5031L
#define ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND 5032L
#define ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE 5033L
#define ERROR_QUORUM_OWNER_ALIVE 5034L
#define ERROR_NETWORK_NOT_AVAILABLE 5035L
#define ERROR_NODE_NOT_AVAILABLE 5036L
#define ERROR_ALL_NODES_NOT_AVAILABLE 5037L
#define ERROR_RESOURCE_FAILED 5038L
#define ERROR_CLUSTER_INVALID_NODE 5039L
#define ERROR_CLUSTER_NODE_EXISTS 5040L
#define ERROR_CLUSTER_JOIN_IN_PROGRESS 5041L
#define ERROR_CLUSTER_NODE_NOT_FOUND 5042L
#define ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND 5043L
#define ERROR_CLUSTER_NETWORK_EXISTS 5044L
#define ERROR_CLUSTER_NETWORK_NOT_FOUND 5045L
#define ERROR_CLUSTER_NETINTERFACE_EXISTS 5046L
#define ERROR_CLUSTER_NETINTERFACE_NOT_FOUND 5047L
#define ERROR_CLUSTER_INVALID_REQUEST 5048L
#define ERROR_CLUSTER_INVALID_NETWORK_PROVIDER 5049L
#define ERROR_CLUSTER_NODE_DOWN 5050L
#define ERROR_CLUSTER_NODE_UNREACHABLE 5051L
#define ERROR_CLUSTER_NODE_NOT_MEMBER 5052L
#define ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS 5053L
#define ERROR_CLUSTER_INVALID_NETWORK 5054L
#define ERROR_CLUSTER_NODE_UP 5056L
#define ERROR_CLUSTER_IPADDR_IN_USE 5057L
#define ERROR_CLUSTER_NODE_NOT_PAUSED 5058L
#define ERROR_CLUSTER_NO_SECURITY_CONTEXT 5059L
#define ERROR_CLUSTER_NETWORK_NOT_INTERNAL 5060L
#define ERROR_CLUSTER_NODE_ALREADY_UP 5061L
#define ERROR_CLUSTER_NODE_ALREADY_DOWN 5062L
#define ERROR_CLUSTER_NETWORK_ALREADY_ONLINE 5063L
#define ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE 5064L
#define ERROR_CLUSTER_NODE_ALREADY_MEMBER 5065L
#define ERROR_CLUSTER_LAST_INTERNAL_NETWORK 5066L
#define ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS 5067L
#define ERROR_INVALID_OPERATION_ON_QUORUM 5068L
#define ERROR_DEPENDENCY_NOT_ALLOWED 5069L
#define ERROR_CLUSTER_NODE_PAUSED 5070L
#define ERROR_NODE_CANT_HOST_RESOURCE 5071L
#define ERROR_CLUSTER_NODE_NOT_READY 5072L
#define ERROR_CLUSTER_NODE_SHUTTING_DOWN 5073L
#define ERROR_CLUSTER_JOIN_ABORTED 5074L
#define ERROR_CLUSTER_INCOMPATIBLE_VERSIONS 5075L
#define ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED 5076L
#define ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED 5077L
#define ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND 5078L
#define ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED 5079L
#define ERROR_CLUSTER_RESNAME_NOT_FOUND 5080L
#define ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED 5081L
#define ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST 5082L
#define ERROR_CLUSTER_DATABASE_SEQMISMATCH 5083L
#define ERROR_RESMON_INVALID_STATE 5084L
#define ERROR_CLUSTER_GUM_NOT_LOCKER 5085L
#define ERROR_QUORUM_DISK_NOT_FOUND 5086L
#define ERROR_DATABASE_BACKUP_CORRUPT 5087L
#define ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT 5088L
#define ERROR_RESOURCE_PROPERTY_UNCHANGEABLE 5089L
#define ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE 5890L
#define ERROR_CLUSTER_QUORUMLOG_NOT_FOUND 5891L
#define ERROR_CLUSTER_MEMBERSHIP_HALT 5892L
#define ERROR_CLUSTER_INSTANCE_ID_MISMATCH 5893L
#define ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP 5894L
#define ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH 5895L
#define ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP 5896L
#define ERROR_CLUSTER_PARAMETER_MISMATCH 5897L
#define ERROR_NODE_CANNOT_BE_CLUSTERED 5898L
#define ERROR_CLUSTER_WRONG_OS_VERSION 5899L
#define ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME 5900L
#define ERROR_CLUSCFG_ALREADY_COMMITTED 5901L
#define ERROR_CLUSCFG_ROLLBACK_FAILED 5902L
#define ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT 5903L
#define ERROR_CLUSTER_OLD_VERSION 5904L
#define ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME 5905L
#define ERROR_ENCRYPTION_FAILED 6000L
#define ERROR_DECRYPTION_FAILED 6001L
#define ERROR_FILE_ENCRYPTED 6002L
#define ERROR_NO_RECOVERY_POLICY 6003L
#define ERROR_NO_EFS 6004L
#define ERROR_WRONG_EFS 6005L
#define ERROR_NO_USER_KEYS 6006L
#define ERROR_FILE_NOT_ENCRYPTED 6007L
#define ERROR_NOT_EXPORT_FORMAT 6008L
#define ERROR_FILE_READ_ONLY 6009L
#define ERROR_DIR_EFS_DISALLOWED 6010L
#define ERROR_EFS_SERVER_NOT_TRUSTED 6011L
#define ERROR_BAD_RECOVERY_POLICY 6012L
#define ERROR_EFS_ALG_BLOB_TOO_BIG 6013L
#define ERROR_VOLUME_NOT_SUPPORT_EFS 6014L
#define ERROR_EFS_DISABLED 6015L
#define ERROR_EFS_VERSION_NOT_SUPPORT 6016L
#define ERROR_NO_BROWSER_SERVERS_FOUND 6118L
#define SCHED_E_SERVICE_NOT_LOCALSYSTEM 6200L

#define ERROR_CTX_WINSTATION_NAME_INVALID 7001L
#define ERROR_CTX_INVALID_PD 7002L
#define ERROR_CTX_PD_NOT_FOUND 7003L
#define ERROR_CTX_WD_NOT_FOUND 7004L
#define ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY 7005L
#define ERROR_CTX_SERVICE_NAME_COLLISION 7006L
#define ERROR_CTX_CLOSE_PENDING 7007L
#define ERROR_CTX_NO_OUTBUF 7008L
#define ERROR_CTX_MODEM_INF_NOT_FOUND 7009L
#define ERROR_CTX_INVALID_MODEMNAME 7010L
#define ERROR_CTX_MODEM_RESPONSE_ERROR 7011L
#define ERROR_CTX_MODEM_RESPONSE_TIMEOUT 7012L
#define ERROR_CTX_MODEM_RESPONSE_NO_CARRIER 7013L
#define ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE 7014L
#define ERROR_CTX_MODEM_RESPONSE_BUSY 7015L
#define ERROR_CTX_MODEM_RESPONSE_VOICE 7016L
#define ERROR_CTX_TD_ERROR 7017L
#define ERROR_CTX_WINSTATION_NOT_FOUND 7022L
#define ERROR_CTX_WINSTATION_ALREADY_EXISTS 7023L
#define ERROR_CTX_WINSTATION_BUSY 7024L
#define ERROR_CTX_BAD_VIDEO_MODE 7025L
#define ERROR_CTX_GRAPHICS_INVALID 7035L
#define ERROR_CTX_LOGON_DISABLED 7037L
#define ERROR_CTX_NOT_CONSOLE 7038L
#define ERROR_CTX_CLIENT_QUERY_TIMEOUT 7040L
#define ERROR_CTX_CONSOLE_DISCONNECT 7041L
#define ERROR_CTX_CONSOLE_CONNECT 7042L
#define ERROR_CTX_SHADOW_DENIED 7044L
#define ERROR_CTX_WINSTATION_ACCESS_DENIED 7045L
#define ERROR_CTX_INVALID_WD 7049L
#define ERROR_CTX_SHADOW_INVALID 7050L
#define ERROR_CTX_SHADOW_DISABLED 7051L
#define ERROR_CTX_CLIENT_LICENSE_IN_USE 7052L
#define ERROR_CTX_CLIENT_LICENSE_NOT_SET 7053L
#define ERROR_CTX_LICENSE_NOT_AVAILABLE 7054L
#define ERROR_CTX_LICENSE_CLIENT_INVALID 7055L
#define ERROR_CTX_LICENSE_EXPIRED 7056L
#define ERROR_CTX_SHADOW_NOT_RUNNING 7057L
#define ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE 7058L
#define ERROR_ACTIVATION_COUNT_EXCEEDED 7059L

#define FRS_ERR_INVALID_API_SEQUENCE 8001L
#define FRS_ERR_STARTING_SERVICE 8002L
#define FRS_ERR_STOPPING_SERVICE 8003L
#define FRS_ERR_INTERNAL_API 8004L
#define FRS_ERR_INTERNAL 8005L
#define FRS_ERR_SERVICE_COMM 8006L
#define FRS_ERR_INSUFFICIENT_PRIV 8007L
#define FRS_ERR_AUTHENTICATION 8008L
#define FRS_ERR_PARENT_INSUFFICIENT_PRIV 8009L
#define FRS_ERR_PARENT_AUTHENTICATION 8010L
#define FRS_ERR_CHILD_TO_PARENT_COMM 8011L
#define FRS_ERR_PARENT_TO_CHILD_COMM 8012L
#define FRS_ERR_SYSVOL_POPULATE 8013L
#define FRS_ERR_SYSVOL_POPULATE_TIMEOUT 8014L
#define FRS_ERR_SYSVOL_IS_BUSY 8015L
#define FRS_ERR_SYSVOL_DEMOTE 8016L
#define FRS_ERR_INVALID_SERVICE_PARAMETER 8017L
#define ERROR_DS_NOT_INSTALLED 8200L
#define ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY 8201L
#define ERROR_DS_NO_ATTRIBUTE_OR_VALUE 8202L
#define ERROR_DS_INVALID_ATTRIBUTE_SYNTAX 8203L
#define ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED 8204L
#define ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS 8205L
#define ERROR_DS_BUSY 8206L
#define ERROR_DS_UNAVAILABLE 8207L
#define ERROR_DS_NO_RIDS_ALLOCATED 8208L
#define ERROR_DS_NO_MORE_RIDS 8209L
#define ERROR_DS_INCORRECT_ROLE_OWNER 8210L
#define ERROR_DS_RIDMGR_INIT_ERROR 8211L
#define ERROR_DS_OBJ_CLASS_VIOLATION 8212L
#define ERROR_DS_CANT_ON_NON_LEAF 8213L
#define ERROR_DS_CANT_ON_RDN 8214L
#define ERROR_DS_CANT_MOD_OBJ_CLASS 8215L
#define ERROR_DS_CROSS_DOM_MOVE_ERROR 8216L
#define ERROR_DS_GC_NOT_AVAILABLE 8217L
#define ERROR_SHARED_POLICY 8218L
#define ERROR_POLICY_OBJECT_NOT_FOUND 8219L
#define ERROR_POLICY_ONLY_IN_DS 8220L
#define ERROR_PROMOTION_ACTIVE 8221L
#define ERROR_NO_PROMOTION_ACTIVE 8222L
#define ERROR_DS_OPERATIONS_ERROR 8224L
#define ERROR_DS_PROTOCOL_ERROR 8225L
#define ERROR_DS_TIMELIMIT_EXCEEDED 8226L
#define ERROR_DS_SIZELIMIT_EXCEEDED 8227L
#define ERROR_DS_ADMIN_LIMIT_EXCEEDED 8228L
#define ERROR_DS_COMPARE_FALSE 8229L
#define ERROR_DS_COMPARE_TRUE 8230L
#define ERROR_DS_AUTH_METHOD_NOT_SUPPORTED 8231L
#define ERROR_DS_STRONG_AUTH_REQUIRED 8232L
#define ERROR_DS_INAPPROPRIATE_AUTH 8233L
#define ERROR_DS_AUTH_UNKNOWN 8234L
#define ERROR_DS_REFERRAL 8235L
#define ERROR_DS_UNAVAILABLE_CRIT_EXTENSION 8236L
#define ERROR_DS_CONFIDENTIALITY_REQUIRED 8237L
#define ERROR_DS_INAPPROPRIATE_MATCHING 8238L
#define ERROR_DS_CONSTRAINT_VIOLATION 8239L
#define ERROR_DS_NO_SUCH_OBJECT 8240L
#define ERROR_DS_ALIAS_PROBLEM 8241L
#define ERROR_DS_INVALID_DN_SYNTAX 8242L
#define ERROR_DS_IS_LEAF 8243L
#define ERROR_DS_ALIAS_DEREF_PROBLEM 8244L
#define ERROR_DS_UNWILLING_TO_PERFORM 8245L
#define ERROR_DS_LOOP_DETECT 8246L
#define ERROR_DS_NAMING_VIOLATION 8247L
#define ERROR_DS_OBJECT_RESULTS_TOO_LARGE 8248L
#define ERROR_DS_AFFECTS_MULTIPLE_DSAS 8249L
#define ERROR_DS_SERVER_DOWN 8250L
#define ERROR_DS_LOCAL_ERROR 8251L
#define ERROR_DS_ENCODING_ERROR 8252L
#define ERROR_DS_DECODING_ERROR 8253L
#define ERROR_DS_FILTER_UNKNOWN 8254L
#define ERROR_DS_PARAM_ERROR 8255L
#define ERROR_DS_NOT_SUPPORTED 8256L
#define ERROR_DS_NO_RESULTS_RETURNED 8257L
#define ERROR_DS_CONTROL_NOT_FOUND 8258L
#define ERROR_DS_CLIENT_LOOP 8259L
#define ERROR_DS_REFERRAL_LIMIT_EXCEEDED 8260L
#define ERROR_DS_SORT_CONTROL_MISSING 8261L
#define ERROR_DS_OFFSET_RANGE_ERROR 8262L
#define ERROR_DS_ROOT_MUST_BE_NC 8301L
#define ERROR_DS_ADD_REPLICA_INHIBITED 8302L
#define ERROR_DS_ATT_NOT_DEF_IN_SCHEMA 8303L
#define ERROR_DS_MAX_OBJ_SIZE_EXCEEDED 8304L
#define ERROR_DS_OBJ_STRING_NAME_EXISTS 8305L
#define ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA 8306L
#define ERROR_DS_RDN_DOESNT_MATCH_SCHEMA 8307L
#define ERROR_DS_NO_REQUESTED_ATTS_FOUND 8308L
#define ERROR_DS_USER_BUFFER_TO_SMALL 8309L
#define ERROR_DS_ATT_IS_NOT_ON_OBJ 8310L
#define ERROR_DS_ILLEGAL_MOD_OPERATION 8311L
#define ERROR_DS_OBJ_TOO_LARGE 8312L
#define ERROR_DS_BAD_INSTANCE_TYPE 8313L
#define ERROR_DS_MASTERDSA_REQUIRED 8314L
#define ERROR_DS_OBJECT_CLASS_REQUIRED 8315L
#define ERROR_DS_MISSING_REQUIRED_ATT 8316L
#define ERROR_DS_ATT_NOT_DEF_FOR_CLASS 8317L
#define ERROR_DS_ATT_ALREADY_EXISTS 8318L
#define ERROR_DS_CANT_ADD_ATT_VALUES 8320L
#define ERROR_DS_SINGLE_VALUE_CONSTRAINT 8321L
#define ERROR_DS_RANGE_CONSTRAINT 8322L
#define ERROR_DS_ATT_VAL_ALREADY_EXISTS 8323L
#define ERROR_DS_CANT_REM_MISSING_ATT 8324L
#define ERROR_DS_CANT_REM_MISSING_ATT_VAL 8325L
#define ERROR_DS_ROOT_CANT_BE_SUBREF 8326L
#define ERROR_DS_NO_CHAINING 8327L
#define ERROR_DS_NO_CHAINED_EVAL 8328L
#define ERROR_DS_NO_PARENT_OBJECT 8329L
#define ERROR_DS_PARENT_IS_AN_ALIAS 8330L
#define ERROR_DS_CANT_MIX_MASTER_AND_REPS 8331L
#define ERROR_DS_CHILDREN_EXIST 8332L
#define ERROR_DS_OBJ_NOT_FOUND 8333L
#define ERROR_DS_ALIASED_OBJ_MISSING 8334L
#define ERROR_DS_BAD_NAME_SYNTAX 8335L
#define ERROR_DS_ALIAS_POINTS_TO_ALIAS 8336L
#define ERROR_DS_CANT_DEREF_ALIAS 8337L
#define ERROR_DS_OUT_OF_SCOPE 8338L
#define ERROR_DS_OBJECT_BEING_REMOVED 8339L
#define ERROR_DS_CANT_DELETE_DSA_OBJ 8340L
#define ERROR_DS_GENERIC_ERROR 8341L
#define ERROR_DS_DSA_MUST_BE_INT_MASTER 8342L
#define ERROR_DS_CLASS_NOT_DSA 8343L
#define ERROR_DS_INSUFF_ACCESS_RIGHTS 8344L
#define ERROR_DS_ILLEGAL_SUPERIOR 8345L
#define ERROR_DS_ATTRIBUTE_OWNED_BY_SAM 8346L
#define ERROR_DS_NAME_TOO_MANY_PARTS 8347L
#define ERROR_DS_NAME_TOO_LONG 8348L
#define ERROR_DS_NAME_VALUE_TOO_LONG 8349L
#define ERROR_DS_NAME_UNPARSEABLE 8350L
#define ERROR_DS_NAME_TYPE_UNKNOWN 8351L
#define ERROR_DS_NOT_AN_OBJECT 8352L
#define ERROR_DS_SEC_DESC_TOO_SHORT 8353L
#define ERROR_DS_SEC_DESC_INVALID 8354L
#define ERROR_DS_NO_DELETED_NAME 8355L
#define ERROR_DS_SUBREF_MUST_HAVE_PARENT 8356L
#define ERROR_DS_NCNAME_MUST_BE_NC 8357L
#define ERROR_DS_CANT_ADD_SYSTEM_ONLY 8358L
#define ERROR_DS_CLASS_MUST_BE_CONCRETE 8359L
#define ERROR_DS_INVALID_DMD 8360L
#define ERROR_DS_OBJ_GUID_EXISTS 8361L
#define ERROR_DS_NOT_ON_BACKLINK 8362L
#define ERROR_DS_NO_CROSSREF_FOR_NC 8363L
#define ERROR_DS_SHUTTING_DOWN 8364L
#define ERROR_DS_UNKNOWN_OPERATION 8365L
#define ERROR_DS_INVALID_ROLE_OWNER 8366L
#define ERROR_DS_COULDNT_CONTACT_FSMO 8367L
#define ERROR_DS_CROSS_NC_DN_RENAME 8368L
#define ERROR_DS_CANT_MOD_SYSTEM_ONLY 8369L
#define ERROR_DS_REPLICATOR_ONLY 8370L
#define ERROR_DS_OBJ_CLASS_NOT_DEFINED 8371L
#define ERROR_DS_OBJ_CLASS_NOT_SUBCLASS 8372L
#define ERROR_DS_NAME_REFERENCE_INVALID 8373L
#define ERROR_DS_CROSS_REF_EXISTS 8374L
#define ERROR_DS_CANT_DEL_MASTER_CROSSREF 8375L
#define ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD 8376L
#define ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX 8377L
#define ERROR_DS_DUP_RDN 8378L
#define ERROR_DS_DUP_OID 8379L
#define ERROR_DS_DUP_MAPI_ID 8380L
#define ERROR_DS_DUP_SCHEMA_ID_GUID 8381L
#define ERROR_DS_DUP_LDAP_DISPLAY_NAME 8382L
#define ERROR_DS_SEMANTIC_ATT_TEST 8383L
#define ERROR_DS_SYNTAX_MISMATCH 8384L
#define ERROR_DS_EXISTS_IN_MUST_HAVE 8385L
#define ERROR_DS_EXISTS_IN_MAY_HAVE 8386L
#define ERROR_DS_NONEXISTENT_MAY_HAVE 8387L
#define ERROR_DS_NONEXISTENT_MUST_HAVE 8388L
#define ERROR_DS_AUX_CLS_TEST_FAIL 8389L
#define ERROR_DS_NONEXISTENT_POSS_SUP 8390L
#define ERROR_DS_SUB_CLS_TEST_FAIL 8391L
#define ERROR_DS_BAD_RDN_ATT_ID_SYNTAX 8392L
#define ERROR_DS_EXISTS_IN_AUX_CLS 8393L
#define ERROR_DS_EXISTS_IN_SUB_CLS 8394L
#define ERROR_DS_EXISTS_IN_POSS_SUP 8395L
#define ERROR_DS_RECALCSCHEMA_FAILED 8396L
#define ERROR_DS_TREE_DELETE_NOT_FINISHED 8397L
#define ERROR_DS_CANT_DELETE 8398L
#define ERROR_DS_ATT_SCHEMA_REQ_ID 8399L
#define ERROR_DS_BAD_ATT_SCHEMA_SYNTAX 8400L
#define ERROR_DS_CANT_CACHE_ATT 8401L
#define ERROR_DS_CANT_CACHE_CLASS 8402L
#define ERROR_DS_CANT_REMOVE_ATT_CACHE 8403L
#define ERROR_DS_CANT_REMOVE_CLASS_CACHE 8404L
#define ERROR_DS_CANT_RETRIEVE_DN 8405L
#define ERROR_DS_MISSING_SUPREF 8406L
#define ERROR_DS_CANT_RETRIEVE_INSTANCE 8407L
#define ERROR_DS_CODE_INCONSISTENCY 8408L
#define ERROR_DS_DATABASE_ERROR 8409L
#define ERROR_DS_GOVERNSID_MISSING 8410L
#define ERROR_DS_MISSING_EXPECTED_ATT 8411L
#define ERROR_DS_NCNAME_MISSING_CR_REF 8412L
#define ERROR_DS_SECURITY_CHECKING_ERROR 8413L
#define ERROR_DS_SCHEMA_NOT_LOADED 8414L
#define ERROR_DS_SCHEMA_ALLOC_FAILED 8415L
#define ERROR_DS_ATT_SCHEMA_REQ_SYNTAX 8416L
#define ERROR_DS_GCVERIFY_ERROR 8417L
#define ERROR_DS_DRA_SCHEMA_MISMATCH 8418L
#define ERROR_DS_CANT_FIND_DSA_OBJ 8419L
#define ERROR_DS_CANT_FIND_EXPECTED_NC 8420L
#define ERROR_DS_CANT_FIND_NC_IN_CACHE 8421L
#define ERROR_DS_CANT_RETRIEVE_CHILD 8422L
#define ERROR_DS_SECURITY_ILLEGAL_MODIFY 8423L
#define ERROR_DS_CANT_REPLACE_HIDDEN_REC 8424L
#define ERROR_DS_BAD_HIERARCHY_FILE 8425L
#define ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED 8426L
#define ERROR_DS_CONFIG_PARAM_MISSING 8427L
#define ERROR_DS_COUNTING_AB_INDICES_FAILED 8428L
#define ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED 8429L
#define ERROR_DS_INTERNAL_FAILURE 8430L
#define ERROR_DS_UNKNOWN_ERROR 8431L
#define ERROR_DS_ROOT_REQUIRES_CLASS_TOP 8432L
#define ERROR_DS_REFUSING_FSMO_ROLES 8433L
#define ERROR_DS_MISSING_FSMO_SETTINGS 8434L
#define ERROR_DS_UNABLE_TO_SURRENDER_ROLES 8435L
#define ERROR_DS_DRA_GENERIC 8436L
#define ERROR_DS_DRA_INVALID_PARAMETER 8437L
#define ERROR_DS_DRA_BUSY 8438L
#define ERROR_DS_DRA_BAD_DN 8439L
#define ERROR_DS_DRA_BAD_NC 8440L
#define ERROR_DS_DRA_DN_EXISTS 8441L
#define ERROR_DS_DRA_INTERNAL_ERROR 8442L
#define ERROR_DS_DRA_INCONSISTENT_DIT 8443L
#define ERROR_DS_DRA_CONNECTION_FAILED 8444L
#define ERROR_DS_DRA_BAD_INSTANCE_TYPE 8445L
#define ERROR_DS_DRA_OUT_OF_MEM 8446L
#define ERROR_DS_DRA_MAIL_PROBLEM 8447L
#define ERROR_DS_DRA_REF_ALREADY_EXISTS 8448L
#define ERROR_DS_DRA_REF_NOT_FOUND 8449L
#define ERROR_DS_DRA_OBJ_IS_REP_SOURCE 8450L
#define ERROR_DS_DRA_DB_ERROR 8451L
#define ERROR_DS_DRA_NO_REPLICA 8452L
#define ERROR_DS_DRA_ACCESS_DENIED 8453L
#define ERROR_DS_DRA_NOT_SUPPORTED 8454L
#define ERROR_DS_DRA_RPC_CANCELLED 8455L
#define ERROR_DS_DRA_SOURCE_DISABLED 8456L
#define ERROR_DS_DRA_SINK_DISABLED 8457L
#define ERROR_DS_DRA_NAME_COLLISION 8458L
#define ERROR_DS_DRA_SOURCE_REINSTALLED 8459L
#define ERROR_DS_DRA_MISSING_PARENT 8460L
#define ERROR_DS_DRA_PREEMPTED 8461L
#define ERROR_DS_DRA_ABANDON_SYNC 8462L
#define ERROR_DS_DRA_SHUTDOWN 8463L
#define ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET 8464L
#define ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA 8465L
#define ERROR_DS_DRA_EXTN_CONNECTION_FAILED 8466L
#define ERROR_DS_INSTALL_SCHEMA_MISMATCH 8467L
#define ERROR_DS_DUP_LINK_ID 8468L
#define ERROR_DS_NAME_ERROR_RESOLVING 8469L
#define ERROR_DS_NAME_ERROR_NOT_FOUND 8470L
#define ERROR_DS_NAME_ERROR_NOT_UNIQUE 8471L
#define ERROR_DS_NAME_ERROR_NO_MAPPING 8472L
#define ERROR_DS_NAME_ERROR_DOMAIN_ONLY 8473L
#define ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING 8474L
#define ERROR_DS_CONSTRUCTED_ATT_MOD 8475L
#define ERROR_DS_WRONG_OM_OBJ_CLASS 8476L
#define ERROR_DS_DRA_REPL_PENDING 8477L
#define ERROR_DS_DS_REQUIRED 8478L
#define ERROR_DS_INVALID_LDAP_DISPLAY_NAME 8479L
#define ERROR_DS_NON_BASE_SEARCH 8480L
#define ERROR_DS_CANT_RETRIEVE_ATTS 8481L
#define ERROR_DS_BACKLINK_WITHOUT_LINK 8482L
#define ERROR_DS_EPOCH_MISMATCH 8483L
#define ERROR_DS_SRC_NAME_MISMATCH 8484L
#define ERROR_DS_SRC_AND_DST_NC_IDENTICAL 8485L
#define ERROR_DS_DST_NC_MISMATCH 8486L
#define ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC 8487L
#define ERROR_DS_SRC_GUID_MISMATCH 8488L
#define ERROR_DS_CANT_MOVE_DELETED_OBJECT 8489L
#define ERROR_DS_PDC_OPERATION_IN_PROGRESS 8490L
#define ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD 8491L
#define ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION 8492L
#define ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS 8493L
#define ERROR_DS_NC_MUST_HAVE_NC_PARENT 8494L
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE 8495L
#define ERROR_DS_DST_DOMAIN_NOT_NATIVE 8496L
#define ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER 8497L
#define ERROR_DS_CANT_MOVE_ACCOUNT_GROUP 8498L
#define ERROR_DS_CANT_MOVE_RESOURCE_GROUP 8499L
#define ERROR_DS_INVALID_SEARCH_FLAG 8500L
#define ERROR_DS_NO_TREE_DELETE_ABOVE_NC 8501L
#define ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE 8502L
#define ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE 8503L
#define ERROR_DS_SAM_INIT_FAILURE 8504L
#define ERROR_DS_SENSITIVE_GROUP_VIOLATION 8505L
#define ERROR_DS_CANT_MOD_PRIMARYGROUPID 8506L
#define ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD 8507L
#define ERROR_DS_NONSAFE_SCHEMA_CHANGE 8508L
#define ERROR_DS_SCHEMA_UPDATE_DISALLOWED 8509L
#define ERROR_DS_CANT_CREATE_UNDER_SCHEMA 8510L
#define ERROR_DS_INSTALL_NO_SRC_SCH_VERSION 8511L
#define ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE 8512L
#define ERROR_DS_INVALID_GROUP_TYPE 8513L
#define ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN 8514L
#define ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN 8515L
#define ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER 8516L
#define ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER 8517L
#define ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER 8518L
#define ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER 8519L
#define ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER 8520L
#define ERROR_DS_HAVE_PRIMARY_MEMBERS 8521L
#define ERROR_DS_STRING_SD_CONVERSION_FAILED 8522L
#define ERROR_DS_NAMING_MASTER_GC 8523L
#define ERROR_DS_LOOKUP_FAILURE 8524L
#define ERROR_DS_COULDNT_UPDATE_SPNS 8525L
#define ERROR_DS_CANT_RETRIEVE_SD 8526L
#define ERROR_DS_KEY_NOT_UNIQUE 8527L
#define ERROR_DS_WRONG_LINKED_ATT_SYNTAX 8528L
#define ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD 8529L
#define ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY 8530L
#define ERROR_DS_CANT_START 8531L
#define ERROR_DS_INIT_FAILURE 8532L
#define ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION 8533L
#define ERROR_DS_SOURCE_DOMAIN_IN_FOREST 8534L
#define ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST 8535L
#define ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED 8536L
#define ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN 8537L
#define ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER 8538L
#define ERROR_DS_SRC_SID_EXISTS_IN_FOREST 8539L
#define ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH 8540L
#define ERROR_SAM_INIT_FAILURE 8541L
#define ERROR_DS_DRA_SCHEMA_INFO_SHIP 8542L
#define ERROR_DS_DRA_SCHEMA_CONFLICT 8543L
#define ERROR_DS_DRA_EARLIER_SCHEMA_CONLICT 8544L
#define ERROR_DS_DRA_OBJ_NC_MISMATCH 8545L
#define ERROR_DS_NC_STILL_HAS_DSAS 8546L
#define ERROR_DS_GC_REQUIRED 8547L
#define ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY 8548L
#define ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS 8549L
#define ERROR_DS_CANT_ADD_TO_GC 8550L
#define ERROR_DS_NO_CHECKPOINT_WITH_PDC 8551L
#define ERROR_DS_SOURCE_AUDITING_NOT_ENABLED 8552L
#define ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC 8553L
#define ERROR_DS_INVALID_NAME_FOR_SPN 8554L
#define ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS 8555L
#define ERROR_DS_UNICODEPWD_NOT_IN_QUOTES 8556L
#define ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED 8557L
#define ERROR_DS_MUST_BE_RUN_ON_DST_DC 8558L
#define ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER 8559L
#define ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ 8560L
#define ERROR_DS_INIT_FAILURE_CONSOLE 8561L
#define ERROR_DS_SAM_INIT_FAILURE_CONSOLE 8562L
#define ERROR_DS_FOREST_VERSION_TOO_HIGH 8563L
#define ERROR_DS_DOMAIN_VERSION_TOO_HIGH 8564L
#define ERROR_DS_FOREST_VERSION_TOO_LOW 8565L
#define ERROR_DS_DOMAIN_VERSION_TOO_LOW 8566L
#define ERROR_DS_INCOMPATIBLE_VERSION 8567L
#define ERROR_DS_LOW_DSA_VERSION 8568L
#define ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN 8569L
#define ERROR_DS_NOT_SUPPORTED_SORT_ORDER 8570L
#define ERROR_DS_NAME_NOT_UNIQUE 8571L
#define ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 8572L
#define ERROR_DS_OUT_OF_VERSION_STORE 8573L
#define ERROR_DS_INCOMPATIBLE_CONTROLS_USED 8574L
#define ERROR_DS_NO_REF_DOMAIN 8575L
#define ERROR_DS_RESERVED_LINK_ID 8576L
#define ERROR_DS_LINK_ID_NOT_AVAILABLE 8577L
#define ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER 8578L
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE 8579L
#define ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC 8580L
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG 8581L
#define ERROR_DS_MODIFYDN_WRONG_GRANDPARENT 8582L
#define ERROR_DS_NAME_ERROR_TRUST_REFERRAL 8583L
#define ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER 8584L
#define ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD 8585L
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 8586L
#define ERROR_DS_THREAD_LIMIT_EXCEEDED 8587L
#define ERROR_DS_NOT_CLOSEST 8588L
#define ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF 8589L
#define ERROR_DS_SINGLE_USER_MODE_FAILED 8590L
#define ERROR_DS_NTDSCRIPT_SYNTAX_ERROR 8591L
#define ERROR_DS_NTDSCRIPT_PROCESS_ERROR 8592L
#define ERROR_DS_DIFFERENT_REPL_EPOCHS 8593L
#define ERROR_DS_DRS_EXTENSIONS_CHANGED 8594L
#define ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR 8595L
#define ERROR_DS_NO_MSDS_INTID 8596L
#define ERROR_DS_DUP_MSDS_INTID 8597L
#define ERROR_DS_EXISTS_IN_RDNATTID 8598L
#define ERROR_DS_AUTHORIZATION_FAILED 8599L
#define ERROR_DS_INVALID_SCRIPT 8600L
#define ERROR_DS_REMOTE_CROSSREF_OP_FAILED 8601L
#define ERROR_DS_CROSS_REF_BUSY 8602L
#define ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN 8603L
#define ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC 8604L
#define ERROR_DS_DUPLICATE_ID_FOUND 8605L
#define ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT 8606L
#define ERROR_DS_GROUP_CONVERSION_ERROR 8607L
#define ERROR_DS_CANT_MOVE_APP_BASIC_GROUP 8608L
#define ERROR_DS_CANT_MOVE_APP_QUERY_GROUP 8609L
#define ERROR_DS_ROLE_NOT_VERIFIED 8610L
#define ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL 8611L
#define ERROR_DS_DOMAIN_RENAME_IN_PROGRESS 8612L
#define ERROR_DS_EXISTING_AD_CHILD_NC 8613L
#define DNS_ERROR_RCODE_FORMAT_ERROR 9001L
#define DNS_ERROR_RCODE_SERVER_FAILURE 9002L
#define DNS_ERROR_RCODE_NAME_ERROR 9003L
#define DNS_ERROR_RCODE_NOT_IMPLEMENTED 9004L
#define DNS_ERROR_RCODE_REFUSED 9005L
#define DNS_ERROR_RCODE_YXDOMAIN 9006L
#define DNS_ERROR_RCODE_YXRRSET 9007L
#define DNS_ERROR_RCODE_NXRRSET 9008L
#define DNS_ERROR_RCODE_NOTAUTH 9009L
#define DNS_ERROR_RCODE_NOTZONE 9010L
#define DNS_ERROR_RCODE_BADSIG 9016L
#define DNS_ERROR_RCODE_BADKEY 9017L
#define DNS_ERROR_RCODE_BADTIME 9018L
#define DNS_INFO_NO_RECORDS 9501L
#define DNS_ERROR_BAD_PACKET 9502L
#define DNS_ERROR_NO_PACKET 9503L
#define DNS_ERROR_RCODE 9504L
#define DNS_ERROR_UNSECURE_PACKET 9505L
#define DNS_ERROR_INVALID_TYPE 9551L
#define DNS_ERROR_INVALID_IP_ADDRESS 9552L
#define DNS_ERROR_INVALID_PROPERTY 9553L
#define DNS_ERROR_TRY_AGAIN_LATER 9554L
#define DNS_ERROR_NOT_UNIQUE 9555L
#define DNS_ERROR_NON_RFC_NAME 9556L
#define DNS_STATUS_FQDN 9557L
#define DNS_STATUS_DOTTED_NAME 9558L
#define DNS_STATUS_SINGLE_PART_NAME 9559L
#define DNS_ERROR_INVALID_NAME_CHAR 9560L
#define DNS_ERROR_NUMERIC_NAME 9561L
#define DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER 9562L
#define DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION 9563L
#define DNS_ERROR_CANNOT_FIND_ROOT_HINTS 9564L
#define DNS_ERROR_INCONSISTENT_ROOT_HINTS 9565L
#define DNS_ERROR_ZONE_DOES_NOT_EXIST 9601L
#define DNS_ERROR_NO_ZONE_INFO 9602L
#define DNS_ERROR_INVALID_ZONE_OPERATION 9603L
#define DNS_ERROR_ZONE_CONFIGURATION_ERROR 9604L
#define DNS_ERROR_ZONE_HAS_NO_SOA_RECORD 9605L
#define DNS_ERROR_ZONE_HAS_NO_NS_RECORDS 9606L
#define DNS_ERROR_ZONE_LOCKED 9607L
#define DNS_ERROR_ZONE_CREATION_FAILED 9608L
#define DNS_ERROR_ZONE_ALREADY_EXISTS 9609L
#define DNS_ERROR_AUTOZONE_ALREADY_EXISTS 9610L
#define DNS_ERROR_INVALID_ZONE_TYPE 9611L
#define DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP 9612L
#define DNS_ERROR_ZONE_NOT_SECONDARY 9613L
#define DNS_ERROR_NEED_SECONDARY_ADDRESSES 9614L
#define DNS_ERROR_WINS_INIT_FAILED 9615L
#define DNS_ERROR_NEED_WINS_SERVERS 9616L
#define DNS_ERROR_NBSTAT_INIT_FAILED 9617L
#define DNS_ERROR_SOA_DELETE_INVALID 9618L
#define DNS_ERROR_FORWARDER_ALREADY_EXISTS 9619L
#define DNS_ERROR_ZONE_REQUIRES_MASTER_IP 9620L
#define DNS_ERROR_ZONE_IS_SHUTDOWN 9621L
#define DNS_ERROR_PRIMARY_REQUIRES_DATAFILE 9651L
#define DNS_ERROR_INVALID_DATAFILE_NAME 9652L
#define DNS_ERROR_DATAFILE_OPEN_FAILURE 9653L
#define DNS_ERROR_FILE_WRITEBACK_FAILED 9654L
#define DNS_ERROR_DATAFILE_PARSING 9655L
#define DNS_ERROR_RECORD_DOES_NOT_EXIST 9701L
#define DNS_ERROR_RECORD_FORMAT 9702L
#define DNS_ERROR_NODE_CREATION_FAILED 9703L
#define DNS_ERROR_UNKNOWN_RECORD_TYPE 9704L
#define DNS_ERROR_RECORD_TIMED_OUT 9705L
#define DNS_ERROR_NAME_NOT_IN_ZONE 9706L
#define DNS_ERROR_CNAME_LOOP 9707L
#define DNS_ERROR_NODE_IS_CNAME 9708L
#define DNS_ERROR_CNAME_COLLISION 9709L
#define DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT 9710L
#define DNS_ERROR_RECORD_ALREADY_EXISTS 9711L
#define DNS_ERROR_SECONDARY_DATA 9712L
#define DNS_ERROR_NO_CREATE_CACHE_DATA 9713L
#define DNS_ERROR_NAME_DOES_NOT_EXIST 9714L
#define DNS_WARNING_PTR_CREATE_FAILED 9715L
#define DNS_WARNING_DOMAIN_UNDELETED 9716L
#define DNS_ERROR_DS_UNAVAILABLE 9717L
#define DNS_ERROR_DS_ZONE_ALREADY_EXISTS 9718L
#define DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE 9719L
#define DNS_INFO_AXFR_COMPLETE 9751L
#define DNS_ERROR_AXFR 9752L
#define DNS_INFO_ADDED_LOCAL_WINS 9753L
#define DNS_STATUS_CONTINUE_NEEDED 9801L
#define DNS_ERROR_NO_TCPIP 9851L
#define DNS_ERROR_NO_DNS_SERVERS 9852L
#define DNS_ERROR_DP_DOES_NOT_EXIST 9901L
#define DNS_ERROR_DP_ALREADY_EXISTS 9902L
#define DNS_ERROR_DP_NOT_ENLISTED 9903L
#define DNS_ERROR_DP_ALREADY_ENLISTED 9904L
#define DNS_ERROR_DP_NOT_AVAILABLE 9905L

#ifndef WSABASEERR
#define WSABASEERR 10000
#define WSAEINTR 10004L
#define WSAEBADF 10009L
#define WSAEACCES 10013L
#define WSAEFAULT 10014L
#define WSAEINVAL 10022L
#define WSAEMFILE 10024L
#define WSAEWOULDBLOCK 10035L
#define WSAEINPROGRESS 10036L
#define WSAEALREADY 10037L
#define WSAENOTSOCK 10038L
#define WSAEDESTADDRREQ 10039L
#define WSAEMSGSIZE 10040L
#define WSAEPROTOTYPE 10041L
#define WSAENOPROTOOPT 10042L
#define WSAEPROTONOSUPPORT 10043L
#define WSAESOCKTNOSUPPORT 10044L
#define WSAEOPNOTSUPP 10045L
#define WSAEPFNOSUPPORT 10046L
#define WSAEAFNOSUPPORT 10047L
#define WSAEADDRINUSE 10048L
#define WSAEADDRNOTAVAIL 10049L
#define WSAENETDOWN 10050L
#define WSAENETUNREACH 10051L
#define WSAENETRESET 10052L
#define WSAECONNABORTED 10053L
#define WSAECONNRESET 10054L
#define WSAENOBUFS 10055L
#define WSAEISCONN 10056L
#define WSAENOTCONN 10057L
#define WSAESHUTDOWN 10058L
#define WSAETOOMANYREFS 10059L
#define WSAETIMEDOUT 10060L
#define WSAECONNREFUSED 10061L
#define WSAELOOP 10062L
#define WSAENAMETOOLONG 10063L
#define WSAEHOSTDOWN 10064L
#define WSAEHOSTUNREACH 10065L
#define WSAENOTEMPTY 10066L
#define WSAEPROCLIM 10067L
#define WSAEUSERS 10068L
#define WSAEDQUOT 10069L
#define WSAESTALE 10070L
#define WSAEREMOTE 10071L
#define WSASYSNOTREADY 10091L
#define WSAVERNOTSUPPORTED 10092L
#define WSANOTINITIALISED 10093L
#define WSAEDISCON 10101L
#define WSAENOMORE 10102L
#define WSAECANCELLED 10103L
#define WSAEINVALIDPROCTABLE 10104L
#define WSAEINVALIDPROVIDER 10105L
#define WSAEPROVIDERFAILEDINIT 10106L
#define WSASYSCALLFAILURE 10107L
#define WSASERVICE_NOT_FOUND 10108L
#define WSATYPE_NOT_FOUND 10109L
#define WSA_E_NO_MORE 10110L
#define WSA_E_CANCELLED 10111L
#define WSAEREFUSED 10112L
#define WSAHOST_NOT_FOUND 11001L
#define WSATRY_AGAIN 11002L
#define WSANO_RECOVERY 11003L
#define WSANO_DATA 11004L
#define WSA_QOS_RECEIVERS 11005L
#define WSA_QOS_SENDERS 11006L
#define WSA_QOS_NO_SENDERS 11007L
#define WSA_QOS_NO_RECEIVERS 11008L
#define WSA_QOS_REQUEST_CONFIRMED 11009L
#define WSA_QOS_ADMISSION_FAILURE 11010L
#define WSA_QOS_POLICY_FAILURE 11011L
#define WSA_QOS_BAD_STYLE 11012L
#define WSA_QOS_BAD_OBJECT 11013L
#define WSA_QOS_TRAFFIC_CTRL_ERROR 11014L
#define WSA_QOS_GENERIC_ERROR 11015L
#define WSA_QOS_ESERVICETYPE 11016L
#define WSA_QOS_EFLOWSPEC 11017L
#define WSA_QOS_EPROVSPECBUF 11018L
#define WSA_QOS_EFILTERSTYLE 11019L
#define WSA_QOS_EFILTERTYPE 11020L
#define WSA_QOS_EFILTERCOUNT 11021L
#define WSA_QOS_EOBJLENGTH 11022L
#define WSA_QOS_EFLOWCOUNT 11023L
#define WSA_QOS_EUNKNOWNPSOBJ 11024L
#define WSA_QOS_EPOLICYOBJ 11025L
#define WSA_QOS_EFLOWDESC 11026L
#define WSA_QOS_EPSFLOWSPEC 11027L
#define WSA_QOS_EPSFILTERSPEC 11028L
#define WSA_QOS_ESDMODEOBJ 11029L
#define WSA_QOS_ESHAPERATEOBJ 11030L
#define WSA_QOS_RESERVED_PETYPE 11031L
#endif /* !WSABASEERR */

#define ERROR_IPSEC_QM_POLICY_EXISTS 13000L
#define ERROR_IPSEC_QM_POLICY_NOT_FOUND 13001L
#define ERROR_IPSEC_QM_POLICY_IN_USE 13002L
#define ERROR_IPSEC_MM_POLICY_EXISTS 13003L
#define ERROR_IPSEC_MM_POLICY_NOT_FOUND 13004L
#define ERROR_IPSEC_MM_POLICY_IN_USE 13005L
#define ERROR_IPSEC_MM_FILTER_EXISTS 13006L
#define ERROR_IPSEC_MM_FILTER_NOT_FOUND 13007L
#define ERROR_IPSEC_TRANSPORT_FILTER_EXISTS 13008L
#define ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND 13009L
#define ERROR_IPSEC_MM_AUTH_EXISTS 13010L
#define ERROR_IPSEC_MM_AUTH_NOT_FOUND 13011L
#define ERROR_IPSEC_MM_AUTH_IN_USE 13012L
#define ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND 13013L
#define ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND 13014L
#define ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND 13015L
#define ERROR_IPSEC_TUNNEL_FILTER_EXISTS 13016L
#define ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND 13017L
#define ERROR_IPSEC_MM_FILTER_PENDING_DELETION 13018L
#define ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION 13019L
#define ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION 13020L
#define ERROR_IPSEC_MM_POLICY_PENDING_DELETION 13021L
#define ERROR_IPSEC_MM_AUTH_PENDING_DELETION 13022L
#define ERROR_IPSEC_QM_POLICY_PENDING_DELETION 13023L
#define WARNING_IPSEC_MM_POLICY_PRUNED 13024L
#define WARNING_IPSEC_QM_POLICY_PRUNED 13025L
#define ERROR_IPSEC_IKE_AUTH_FAIL 13801L
#define ERROR_IPSEC_IKE_ATTRIB_FAIL 13802L
#define ERROR_IPSEC_IKE_NEGOTIATION_PENDING 13803L
#define ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR 13804L
#define ERROR_IPSEC_IKE_TIMED_OUT 13805L
#define ERROR_IPSEC_IKE_NO_CERT 13806L
#define ERROR_IPSEC_IKE_SA_DELETED 13807L
#define ERROR_IPSEC_IKE_SA_REAPED 13808L
#define ERROR_IPSEC_IKE_MM_ACQUIRE_DROP 13809L
#define ERROR_IPSEC_IKE_QM_ACQUIRE_DROP 13810L
#define ERROR_IPSEC_IKE_QUEUE_DROP_MM 13811L
#define ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM 13812L
#define ERROR_IPSEC_IKE_DROP_NO_RESPONSE 13813L
#define ERROR_IPSEC_IKE_MM_DELAY_DROP 13814L
#define ERROR_IPSEC_IKE_QM_DELAY_DROP 13815L
#define ERROR_IPSEC_IKE_ERROR 13816L
#define ERROR_IPSEC_IKE_CRL_FAILED 13817L
#define ERROR_IPSEC_IKE_INVALID_KEY_USAGE 13818L
#define ERROR_IPSEC_IKE_INVALID_CERT_TYPE 13819L
#define ERROR_IPSEC_IKE_NO_PRIVATE_KEY 13820L
#define ERROR_IPSEC_IKE_DH_FAIL 13822L
#define ERROR_IPSEC_IKE_INVALID_HEADER 13824L
#define ERROR_IPSEC_IKE_NO_POLICY 13825L
#define ERROR_IPSEC_IKE_INVALID_SIGNATURE 13826L
#define ERROR_IPSEC_IKE_KERBEROS_ERROR 13827L
#define ERROR_IPSEC_IKE_NO_PUBLIC_KEY 13828L
#define ERROR_IPSEC_IKE_PROCESS_ERR 13829L
#define ERROR_IPSEC_IKE_PROCESS_ERR_SA 13830L
#define ERROR_IPSEC_IKE_PROCESS_ERR_PROP 13831L
#define ERROR_IPSEC_IKE_PROCESS_ERR_TRANS 13832L
#define ERROR_IPSEC_IKE_PROCESS_ERR_KE 13833L
#define ERROR_IPSEC_IKE_PROCESS_ERR_ID 13834L
#define ERROR_IPSEC_IKE_PROCESS_ERR_CERT 13835L
#define ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ 13836L
#define ERROR_IPSEC_IKE_PROCESS_ERR_HASH 13837L
#define ERROR_IPSEC_IKE_PROCESS_ERR_SIG 13838L
#define ERROR_IPSEC_IKE_PROCESS_ERR_NONCE 13839L
#define ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY 13840L
#define ERROR_IPSEC_IKE_PROCESS_ERR_DELETE 13841L
#define ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR 13842L
#define ERROR_IPSEC_IKE_INVALID_PAYLOAD 13843L
#define ERROR_IPSEC_IKE_LOAD_SOFT_SA 13844L
#define ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN 13845L
#define ERROR_IPSEC_IKE_INVALID_COOKIE 13846L
#define ERROR_IPSEC_IKE_NO_PEER_CERT 13847L
#define ERROR_IPSEC_IKE_PEER_CRL_FAILED 13848L
#define ERROR_IPSEC_IKE_POLICY_CHANGE 13849L
#define ERROR_IPSEC_IKE_NO_MM_POLICY 13850L
#define ERROR_IPSEC_IKE_NOTCBPRIV 13851L
#define ERROR_IPSEC_IKE_SECLOADFAIL 13852L
#define ERROR_IPSEC_IKE_FAILSSPINIT 13853L
#define ERROR_IPSEC_IKE_FAILQUERYSSP 13854L
#define ERROR_IPSEC_IKE_SRVACQFAIL 13855L
#define ERROR_IPSEC_IKE_SRVQUERYCRED 13856L
#define ERROR_IPSEC_IKE_GETSPIFAIL 13857L
#define ERROR_IPSEC_IKE_INVALID_FILTER 13858L
#define ERROR_IPSEC_IKE_OUT_OF_MEMORY 13859L
#define ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED 13860L
#define ERROR_IPSEC_IKE_INVALID_POLICY 13861L
#define ERROR_IPSEC_IKE_UNKNOWN_DOI 13862L
#define ERROR_IPSEC_IKE_INVALID_SITUATION 13863L
#define ERROR_IPSEC_IKE_DH_FAILURE 13864L
#define ERROR_IPSEC_IKE_INVALID_GROUP 13865L
#define ERROR_IPSEC_IKE_ENCRYPT 13866L
#define ERROR_IPSEC_IKE_DECRYPT 13867L
#define ERROR_IPSEC_IKE_POLICY_MATCH 13868L
#define ERROR_IPSEC_IKE_UNSUPPORTED_ID 13869L
#define ERROR_IPSEC_IKE_INVALID_HASH 13870L
#define ERROR_IPSEC_IKE_INVALID_HASH_ALG 13871L
#define ERROR_IPSEC_IKE_INVALID_HASH_SIZE 13872L
#define ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG 13873L
#define ERROR_IPSEC_IKE_INVALID_AUTH_ALG 13874L
#define ERROR_IPSEC_IKE_INVALID_SIG 13875L
#define ERROR_IPSEC_IKE_LOAD_FAILED 13876L
#define ERROR_IPSEC_IKE_RPC_DELETE 13877L
#define ERROR_IPSEC_IKE_BENIGN_REINIT 13878L
#define ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY 13879L
#define ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN 13881L
#define ERROR_IPSEC_IKE_MM_LIMIT 13882L
#define ERROR_IPSEC_IKE_NEGOTIATION_DISABLED 13883L
#define ERROR_IPSEC_IKE_NEG_STATUS_END 13884L

#define ERROR_SXS_SECTION_NOT_FOUND 14000L
#define ERROR_SXS_CANT_GEN_ACTCTX 14001L
#define ERROR_SXS_INVALID_ACTCTXDATA_FORMAT 14002L
#define ERROR_SXS_ASSEMBLY_NOT_FOUND 14003L
#define ERROR_SXS_MANIFEST_FORMAT_ERROR 14004L
#define ERROR_SXS_MANIFEST_PARSE_ERROR 14005L
#define ERROR_SXS_ACTIVATION_CONTEXT_DISABLED 14006L
#define ERROR_SXS_KEY_NOT_FOUND 14007L
#define ERROR_SXS_VERSION_CONFLICT 14008L
#define ERROR_SXS_WRONG_SECTION_TYPE 14009L
#define ERROR_SXS_THREAD_QUERIES_DISABLED 14010L
#define ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET 14011L
#define ERROR_SXS_UNKNOWN_ENCODING_GROUP 14012L
#define ERROR_SXS_UNKNOWN_ENCODING 14013L
#define ERROR_SXS_INVALID_XML_NAMESPACE_URI 14014L
#define ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED 14015L
#define ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED 14016L
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE 14017L
#define ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE 14018L
#define ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE 14019L
#define ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT 14020L
#define ERROR_SXS_DUPLICATE_DLL_NAME 14021L
#define ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME 14022L
#define ERROR_SXS_DUPLICATE_CLSID 14023L
#define ERROR_SXS_DUPLICATE_IID 14024L
#define ERROR_SXS_DUPLICATE_TLBID 14025L
#define ERROR_SXS_DUPLICATE_PROGID 14026L
#define ERROR_SXS_DUPLICATE_ASSEMBLY_NAME 14027L
#define ERROR_SXS_FILE_HASH_MISMATCH 14028L
#define ERROR_SXS_POLICY_PARSE_ERROR 14029L
#define ERROR_SXS_XML_E_MISSINGQUOTE 14030L
#define ERROR_SXS_XML_E_COMMENTSYNTAX 14031L
#define ERROR_SXS_XML_E_BADSTARTNAMECHAR 14032L
#define ERROR_SXS_XML_E_BADNAMECHAR 14033L
#define ERROR_SXS_XML_E_BADCHARINSTRING 14034L
#define ERROR_SXS_XML_E_XMLDECLSYNTAX 14035L
#define ERROR_SXS_XML_E_BADCHARDATA 14036L
#define ERROR_SXS_XML_E_MISSINGWHITESPACE 14037L
#define ERROR_SXS_XML_E_EXPECTINGTAGEND 14038L
#define ERROR_SXS_XML_E_MISSINGSEMICOLON 14039L
#define ERROR_SXS_XML_E_UNBALANCEDPAREN 14040L
#define ERROR_SXS_XML_E_INTERNALERROR 14041L
#define ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE 14042L
#define ERROR_SXS_XML_E_INCOMPLETE_ENCODING 14043L
#define ERROR_SXS_XML_E_MISSING_PAREN 14044L
#define ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE 14045L
#define ERROR_SXS_XML_E_MULTIPLE_COLONS 14046L
#define ERROR_SXS_XML_E_INVALID_DECIMAL 14047L
#define ERROR_SXS_XML_E_INVALID_HEXIDECIMAL 14048L
#define ERROR_SXS_XML_E_INVALID_UNICODE 14049L
#define ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK 14050L
#define ERROR_SXS_XML_E_UNEXPECTEDENDTAG 14051L
#define ERROR_SXS_XML_E_UNCLOSEDTAG 14052L
#define ERROR_SXS_XML_E_DUPLICATEATTRIBUTE 14053L
#define ERROR_SXS_XML_E_MULTIPLEROOTS 14054L
#define ERROR_SXS_XML_E_INVALIDATROOTLEVEL 14055L
#define ERROR_SXS_XML_E_BADXMLDECL 14056L
#define ERROR_SXS_XML_E_MISSINGROOT 14057L
#define ERROR_SXS_XML_E_UNEXPECTEDEOF 14058L
#define ERROR_SXS_XML_E_BADPEREFINSUBSET 14059L
#define ERROR_SXS_XML_E_UNCLOSEDSTARTTAG 14060L
#define ERROR_SXS_XML_E_UNCLOSEDENDTAG 14061L
#define ERROR_SXS_XML_E_UNCLOSEDSTRING 14062L
#define ERROR_SXS_XML_E_UNCLOSEDCOMMENT 14063L
#define ERROR_SXS_XML_E_UNCLOSEDDECL 14064L
#define ERROR_SXS_XML_E_UNCLOSEDCDATA 14065L
#define ERROR_SXS_XML_E_RESERVEDNAMESPACE 14066L
#define ERROR_SXS_XML_E_INVALIDENCODING 14067L
#define ERROR_SXS_XML_E_INVALIDSWITCH 14068L
#define ERROR_SXS_XML_E_BADXMLCASE 14069L
#define ERROR_SXS_XML_E_INVALID_STANDALONE 14070L
#define ERROR_SXS_XML_E_UNEXPECTED_STANDALONE 14071L
#define ERROR_SXS_XML_E_INVALID_VERSION 14072L
#define ERROR_SXS_XML_E_MISSINGEQUALS 14073L
#define ERROR_SXS_PROTECTION_RECOVERY_FAILED 14074L
#define ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT 14075L
#define ERROR_SXS_PROTECTION_CATALOG_NOT_VALID 14076L
#define ERROR_SXS_UNTRANSLATABLE_HRESULT 14077L
#define ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING 14078L
#define ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE 14079L
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME 14080L

#define SEVERITY_SUCCESS 0
#define SEVERITY_ERROR 1
#define FACILITY_WINDOWS 8
#define FACILITY_STORAGE 3
#define FACILITY_RPC 1
#define FACILITY_WIN32 7
#define FACILITY_CONTROL 10
#define FACILITY_NULL 0
#define FACILITY_ITF 4
#define FACILITY_DISPATCH 2
#define SUCCEEDED(Status) ((HRESULT)(Status) >= 0)
#define FAILED(Status) ((HRESULT)(Status)<0)
#define IS_ERROR(Status) ((unsigned long)(Status) >> 31 == SEVERITY_ERROR)
#define HRESULT_CODE(r) ((r)&0xFFFF)
#define SCODE_CODE(c) ((c)&0xFFFF)
#define HRESULT_FACILITY(r) (((r)>>16)&0x1fff)
#define SCODE_FACILITY(c) (((c)>>16)&0x1fff)
#define HRESULT_SEVERITY(r) (((r)>>31)&0x1)
#define SCODE_SEVERITY(c) (((c)>>31)&0x1)
#define MAKE_HRESULT(s,f,c) ((HRESULT)(((unsigned long)(s)<<31)|((unsigned long)(f)<<16)|((unsigned long)(c))))
#define MAKE_SCODE(s,f,c) ((SCODE)(((unsigned long)(s)<<31)|((unsigned long)(f)<<16)|((unsigned long)(c))) )
#define FACILITY_NT_BIT 0x10000000
#define HRESULT_FROM_WIN32(x) (x?((HRESULT)(((x)&0x0000FFFF)|(FACILITY_WIN32<<16)|0x80000000)):0)
#define HRESULT_FROM_NT(x) ((HRESULT)((x)|FACILITY_NT_BIT))
#define GetScode(hr) ((SCODE) (hr))
#define ResultFromScode(sc) ((HRESULT) (sc))
#define PropagateResult(hrPrevious, scBase) ((HRESULT) scBase)

#define NOERROR S_OK
#define E_UNEXPECTED ((HRESULT)0x8000FFFFL)
#define E_NOTIMPL ((HRESULT)0x80004001L)
#define E_OUTOFMEMORY ((HRESULT)0x8007000EL)
#define E_INVALIDARG ((HRESULT)0x80070057L)
#define E_NOINTERFACE ((HRESULT)0x80004002L)
#define E_POINTER ((HRESULT)0x80004003L)
#define E_HANDLE ((HRESULT)0x80070006L)
#define E_ABORT ((HRESULT)0x80004004L)
#define E_FAIL ((HRESULT)0x80004005L)
#define E_ACCESSDENIED ((HRESULT)0x80070005L)
#define E_PENDING ((HRESULT)0x8000000AL)
#define CO_E_INIT_TLS ((HRESULT)0x80004006L)
#define CO_E_INIT_SHARED_ALLOCATOR ((HRESULT)0x80004007L)
#define CO_E_INIT_MEMORY_ALLOCATOR ((HRESULT)0x80004008L)
#define CO_E_INIT_CLASS_CACHE ((HRESULT)0x80004009L)
#define CO_E_INIT_RPC_CHANNEL ((HRESULT)0x8000400AL)
#define CO_E_INIT_TLS_SET_CHANNEL_CONTROL ((HRESULT)0x8000400BL)
#define CO_E_INIT_TLS_CHANNEL_CONTROL ((HRESULT)0x8000400CL)
#define CO_E_INIT_UNACCEPTED_USER_ALLOCATOR ((HRESULT)0x8000400DL)
#define CO_E_INIT_SCM_MUTEX_EXISTS ((HRESULT)0x8000400EL)
#define CO_E_INIT_SCM_FILE_MAPPING_EXISTS ((HRESULT)0x8000400FL)
#define CO_E_INIT_SCM_MAP_VIEW_OF_FILE ((HRESULT)0x80004010L)
#define CO_E_INIT_SCM_EXEC_FAILURE ((HRESULT)0x80004011L)
#define CO_E_INIT_ONLY_SINGLE_THREADED ((HRESULT)0x80004012L)
#define S_OK ((HRESULT)0x00000000L)
#define S_FALSE ((HRESULT)0x00000001L)
#define OLE_E_FIRST ((HRESULT)0x80040000L)
#define OLE_E_LAST ((HRESULT)0x800400FFL)
#define OLE_S_FIRST ((HRESULT)0x00040000L)
#define OLE_S_LAST ((HRESULT)0x000400FFL)
#define OLE_E_OLEVERB ((HRESULT)0x80040000L)
#define OLE_E_ADVF ((HRESULT)0x80040001L)
#define OLE_E_ENUM_NOMORE ((HRESULT)0x80040002L)
#define OLE_E_ADVISENOTSUPPORTED ((HRESULT)0x80040003L)
#define OLE_E_NOCONNECTION ((HRESULT)0x80040004L)
#define OLE_E_NOTRUNNING ((HRESULT)0x80040005L)
#define OLE_E_NOCACHE ((HRESULT)0x80040006L)
#define OLE_E_BLANK ((HRESULT)0x80040007L)
#define OLE_E_CLASSDIFF ((HRESULT)0x80040008L)
#define OLE_E_CANT_GETMONIKER ((HRESULT)0x80040009L)
#define OLE_E_CANT_BINDTOSOURCE ((HRESULT)0x8004000AL)
#define OLE_E_STATIC ((HRESULT)0x8004000BL)
#define OLE_E_PROMPTSAVECANCELLED ((HRESULT)0x8004000CL)
#define OLE_E_INVALIDRECT ((HRESULT)0x8004000DL)
#define OLE_E_WRONGCOMPOBJ ((HRESULT)0x8004000EL)
#define OLE_E_INVALIDHWND ((HRESULT)0x8004000FL)
#define OLE_E_NOT_INPLACEACTIVE ((HRESULT)0x80040010L)
#define OLE_E_CANTCONVERT ((HRESULT)0x80040011L)
#define OLE_E_NOSTORAGE ((HRESULT)0x80040012L)
#define DV_E_FORMATETC ((HRESULT)0x80040064L)
#define DV_E_DVTARGETDEVICE ((HRESULT)0x80040065L)
#define DV_E_STGMEDIUM ((HRESULT)0x80040066L)
#define DV_E_STATDATA ((HRESULT)0x80040067L)
#define DV_E_LINDEX ((HRESULT)0x80040068L)
#define DV_E_TYMED ((HRESULT)0x80040069L)
#define DV_E_CLIPFORMAT ((HRESULT)0x8004006AL)
#define DV_E_DVASPECT ((HRESULT)0x8004006BL)
#define DV_E_DVTARGETDEVICE_SIZE ((HRESULT)0x8004006CL)
#define DV_E_NOIVIEWOBJECT ((HRESULT)0x8004006DL)
#define DRAGDROP_E_FIRST ((HRESULT)0x80040100L)
#define DRAGDROP_E_LAST ((HRESULT)0x8004010FL)
#define DRAGDROP_S_FIRST ((HRESULT)0x00040100L)
#define DRAGDROP_S_LAST ((HRESULT)0x0004010FL)
#define DRAGDROP_E_NOTREGISTERED ((HRESULT)0x80040100L)
#define DRAGDROP_E_ALREADYREGISTERED ((HRESULT)0x80040101L)
#define DRAGDROP_E_INVALIDHWND ((HRESULT)0x80040102L)
#define CLASSFACTORY_E_FIRST ((HRESULT)0x80040110L)
#define CLASSFACTORY_E_LAST ((HRESULT)0x8004011FL)
#define CLASSFACTORY_S_FIRST ((HRESULT)0x00040110L)
#define CLASSFACTORY_S_LAST ((HRESULT)0x0004011FL)
#define CLASS_E_NOAGGREGATION ((HRESULT)0x80040110L)
#define CLASS_E_CLASSNOTAVAILABLE ((HRESULT)0x80040111L)
#define MARSHAL_E_FIRST ((HRESULT)0x80040120L)
#define MARSHAL_E_LAST ((HRESULT)0x8004012FL)
#define MARSHAL_S_FIRST ((HRESULT)0x00040120L)
#define MARSHAL_S_LAST ((HRESULT)0x0004012FL)
#define DATA_E_FIRST ((HRESULT)0x80040130L)
#define DATA_E_LAST ((HRESULT)0x8004013FL)
#define DATA_S_FIRST ((HRESULT)0x00040130L)
#define DATA_S_LAST ((HRESULT)0x0004013FL)
#define VIEW_E_FIRST ((HRESULT)0x80040140L)
#define VIEW_E_LAST ((HRESULT)0x8004014FL)
#define VIEW_S_FIRST ((HRESULT)0x00040140L)
#define VIEW_S_LAST ((HRESULT)0x0004014FL)
#define VIEW_E_DRAW ((HRESULT)0x80040140L)
#define REGDB_E_FIRST ((HRESULT)0x80040150L)
#define REGDB_E_LAST ((HRESULT)0x8004015FL)
#define REGDB_S_FIRST ((HRESULT)0x00040150L)
#define REGDB_S_LAST ((HRESULT)0x0004015FL)
#define REGDB_E_READREGDB ((HRESULT)0x80040150L)
#define REGDB_E_WRITEREGDB ((HRESULT)0x80040151L)
#define REGDB_E_KEYMISSING ((HRESULT)0x80040152L)
#define REGDB_E_INVALIDVALUE ((HRESULT)0x80040153L)
#define REGDB_E_CLASSNOTREG ((HRESULT)0x80040154L)
#define REGDB_E_IIDNOTREG ((HRESULT)0x80040155L)
#define CACHE_E_FIRST ((HRESULT)0x80040170L)
#define CACHE_E_LAST ((HRESULT)0x8004017FL)
#define CACHE_S_FIRST ((HRESULT)0x00040170L)
#define CACHE_S_LAST ((HRESULT)0x0004017FL)
#define CACHE_E_NOCACHE_UPDATED ((HRESULT)0x80040170L)
#define OLEOBJ_E_FIRST ((HRESULT)0x80040180L)
#define OLEOBJ_E_LAST ((HRESULT)0x8004018FL)
#define OLEOBJ_S_FIRST ((HRESULT)0x00040180L)
#define OLEOBJ_S_LAST ((HRESULT)0x0004018FL)
#define OLEOBJ_E_NOVERBS ((HRESULT)0x80040180L)
#define OLEOBJ_E_INVALIDVERB ((HRESULT)0x80040181L)
#define CLIENTSITE_E_FIRST ((HRESULT)0x80040190L)
#define CLIENTSITE_E_LAST ((HRESULT)0x8004019FL)
#define CLIENTSITE_S_FIRST ((HRESULT)0x00040190L)
#define CLIENTSITE_S_LAST ((HRESULT)0x0004019FL)
#define INPLACE_E_NOTUNDOABLE ((HRESULT)0x800401A0L)
#define INPLACE_E_NOTOOLSPACE ((HRESULT)0x800401A1L)
#define INPLACE_E_FIRST ((HRESULT)0x800401A0L)
#define INPLACE_E_LAST ((HRESULT)0x800401AFL)
#define INPLACE_S_FIRST ((HRESULT)0x000401A0L)
#define INPLACE_S_LAST ((HRESULT)0x000401AFL)
#define ENUM_E_FIRST ((HRESULT)0x800401B0L)
#define ENUM_E_LAST ((HRESULT)0x800401BFL)
#define ENUM_S_FIRST ((HRESULT)0x000401B0L)
#define ENUM_S_LAST ((HRESULT)0x000401BFL)
#define CONVERT10_E_FIRST ((HRESULT)0x800401C0L)
#define CONVERT10_E_LAST ((HRESULT)0x800401CFL)
#define CONVERT10_S_FIRST ((HRESULT)0x000401C0L)
#define CONVERT10_S_LAST ((HRESULT)0x000401CFL)
#define CONVERT10_E_OLESTREAM_GET ((HRESULT)0x800401C0L)
#define CONVERT10_E_OLESTREAM_PUT ((HRESULT)0x800401C1L)
#define CONVERT10_E_OLESTREAM_FMT ((HRESULT)0x800401C2L)
#define CONVERT10_E_OLESTREAM_BITMAP_TO_DIB ((HRESULT)0x800401C3L)
#define CONVERT10_E_STG_FMT ((HRESULT)0x800401C4L)
#define CONVERT10_E_STG_NO_STD_STREAM ((HRESULT)0x800401C5L)
#define CONVERT10_E_STG_DIB_TO_BITMAP ((HRESULT)0x800401C6L)
#define CLIPBRD_E_FIRST ((HRESULT)0x800401D0L)
#define CLIPBRD_E_LAST ((HRESULT)0x800401DFL)
#define CLIPBRD_S_FIRST ((HRESULT)0x000401D0L)
#define CLIPBRD_S_LAST ((HRESULT)0x000401DFL)
#define CLIPBRD_E_CANT_OPEN ((HRESULT)0x800401D0L)
#define CLIPBRD_E_CANT_EMPTY ((HRESULT)0x800401D1L)
#define CLIPBRD_E_CANT_SET ((HRESULT)0x800401D2L)
#define CLIPBRD_E_BAD_DATA ((HRESULT)0x800401D3L)
#define CLIPBRD_E_CANT_CLOSE ((HRESULT)0x800401D4L)
#define MK_E_FIRST ((HRESULT)0x800401E0L)
#define MK_E_LAST ((HRESULT)0x800401EFL)
#define MK_S_FIRST ((HRESULT)0x000401E0L)
#define MK_S_LAST ((HRESULT)0x000401EFL)
#define MK_E_CONNECTMANUALLY ((HRESULT)0x800401E0L)
#define MK_E_EXCEEDEDDEADLINE ((HRESULT)0x800401E1L)
#define MK_E_NEEDGENERIC ((HRESULT)0x800401E2L)
#define MK_E_UNAVAILABLE ((HRESULT)0x800401E3L)
#define MK_E_SYNTAX ((HRESULT)0x800401E4L)
#define MK_E_NOOBJECT ((HRESULT)0x800401E5L)
#define MK_E_INVALIDEXTENSION ((HRESULT)0x800401E6L)
#define MK_E_INTERMEDIATEINTERFACENOTSUPPORTED ((HRESULT)0x800401E7L)
#define MK_E_NOTBINDABLE ((HRESULT)0x800401E8L)
#define MK_E_NOTBOUND ((HRESULT)0x800401E9L)
#define MK_E_CANTOPENFILE ((HRESULT)0x800401EAL)
#define MK_E_MUSTBOTHERUSER ((HRESULT)0x800401EBL)
#define MK_E_NOINVERSE ((HRESULT)0x800401ECL)
#define MK_E_NOSTORAGE ((HRESULT)0x800401EDL)
#define MK_E_NOPREFIX ((HRESULT)0x800401EEL)
#define MK_E_ENUMERATION_FAILED ((HRESULT)0x800401EFL)
#define CO_E_FIRST ((HRESULT)0x800401F0L)
#define CO_E_LAST ((HRESULT)0x800401FFL)
#define CO_S_FIRST ((HRESULT)0x000401F0L)
#define CO_S_LAST ((HRESULT)0x000401FFL)
#define CO_E_NOTINITIALIZED ((HRESULT)0x800401F0L)
#define CO_E_ALREADYINITIALIZED ((HRESULT)0x800401F1L)
#define CO_E_CANTDETERMINECLASS ((HRESULT)0x800401F2L)
#define CO_E_CLASSSTRING ((HRESULT)0x800401F3L)
#define CO_E_IIDSTRING ((HRESULT)0x800401F4L)
#define CO_E_APPNOTFOUND ((HRESULT)0x800401F5L)
#define CO_E_APPSINGLEUSE ((HRESULT)0x800401F6L)
#define CO_E_ERRORINAPP ((HRESULT)0x800401F7L)
#define CO_E_DLLNOTFOUND ((HRESULT)0x800401F8L)
#define CO_E_ERRORINDLL ((HRESULT)0x800401F9L)
#define CO_E_WRONGOSFORAPP ((HRESULT)0x800401FAL)
#define CO_E_OBJNOTREG ((HRESULT)0x800401FBL)
#define CO_E_OBJISREG ((HRESULT)0x800401FCL)
#define CO_E_OBJNOTCONNECTED ((HRESULT)0x800401FDL)
#define CO_E_APPDIDNTREG ((HRESULT)0x800401FEL)
#define CO_E_RELEASED ((HRESULT)0x800401FFL)
#define OLE_S_USEREG ((HRESULT)0x00040000L)
#define OLE_S_STATIC ((HRESULT)0x00040001L)
#define OLE_S_MAC_CLIPFORMAT ((HRESULT)0x00040002L)
#define DRAGDROP_S_DROP ((HRESULT)0x00040100L)
#define DRAGDROP_S_CANCEL ((HRESULT)0x00040101L)
#define DRAGDROP_S_USEDEFAULTCURSORS ((HRESULT)0x00040102L)
#define DATA_S_SAMEFORMATETC ((HRESULT)0x00040130L)
#define VIEW_S_ALREADY_FROZEN ((HRESULT)0x00040140L)
#define CACHE_S_FORMATETC_NOTSUPPORTED ((HRESULT)0x00040170L)
#define CACHE_S_SAMECACHE ((HRESULT)0x00040171L)
#define CACHE_S_SOMECACHES_NOTUPDATED ((HRESULT)0x00040172L)
#define OLEOBJ_S_INVALIDVERB ((HRESULT)0x00040180L)
#define OLEOBJ_S_CANNOT_DOVERB_NOW ((HRESULT)0x00040181L)
#define OLEOBJ_S_INVALIDHWND ((HRESULT)0x00040182L)
#define INPLACE_S_TRUNCATED ((HRESULT)0x000401A0L)
#define CONVERT10_S_NO_PRESENTATION ((HRESULT)0x000401C0L)
#define MK_S_REDUCED_TO_SELF ((HRESULT)0x000401E2L)
#define MK_S_ME ((HRESULT)0x000401E4L)
#define MK_S_HIM ((HRESULT)0x000401E5L)
#define MK_S_US ((HRESULT)0x000401E6L)
#define MK_S_MONIKERALREADYREGISTERED ((HRESULT)0x000401E7L)
#define CO_E_CLASS_CREATE_FAILED ((HRESULT)0x80080001L)
#define CO_E_SCM_ERROR ((HRESULT)0x80080002L)
#define CO_E_SCM_RPC_FAILURE ((HRESULT)0x80080003L)
#define CO_E_BAD_PATH ((HRESULT)0x80080004L)
#define CO_E_SERVER_EXEC_FAILURE ((HRESULT)0x80080005L)
#define CO_E_OBJSRV_RPC_FAILURE ((HRESULT)0x80080006L)
#define MK_E_NO_NORMALIZED ((HRESULT)0x80080007L)
#define CO_E_SERVER_STOPPING ((HRESULT)0x80080008L)
#define MEM_E_INVALID_ROOT ((HRESULT)0x80080009L)
#define MEM_E_INVALID_LINK ((HRESULT)0x80080010L)
#define MEM_E_INVALID_SIZE ((HRESULT)0x80080011L)
#define CO_S_NOTALLINTERFACES ((HRESULT)0x00080012L)
#define DISP_E_UNKNOWNINTERFACE ((HRESULT)0x80020001L)
#define DISP_E_MEMBERNOTFOUND ((HRESULT)0x80020003L)
#define DISP_E_PARAMNOTFOUND ((HRESULT)0x80020004L)
#define DISP_E_TYPEMISMATCH ((HRESULT)0x80020005L)
#define DISP_E_UNKNOWNNAME ((HRESULT)0x80020006L)
#define DISP_E_NONAMEDARGS ((HRESULT)0x80020007L)
#define DISP_E_BADVARTYPE ((HRESULT)0x80020008L)
#define DISP_E_EXCEPTION ((HRESULT)0x80020009L)
#define DISP_E_OVERFLOW ((HRESULT)0x8002000AL)
#define DISP_E_BADINDEX ((HRESULT)0x8002000BL)
#define DISP_E_UNKNOWNLCID ((HRESULT)0x8002000CL)
#define DISP_E_ARRAYISLOCKED ((HRESULT)0x8002000DL)
#define DISP_E_BADPARAMCOUNT ((HRESULT)0x8002000EL)
#define DISP_E_PARAMNOTOPTIONAL ((HRESULT)0x8002000FL)
#define DISP_E_BADCALLEE ((HRESULT)0x80020010L)
#define DISP_E_NOTACOLLECTION ((HRESULT)0x80020011L)
#define DISP_E_DIVBYZERO ((HRESULT)0x80020012L)
#define TYPE_E_BUFFERTOOSMALL ((HRESULT)0x80028016L)
#define TYPE_E_INVDATAREAD ((HRESULT)0x80028018L)
#define TYPE_E_UNSUPFORMAT ((HRESULT)0x80028019L)
#define TYPE_E_REGISTRYACCESS ((HRESULT)0x8002801CL)
#define TYPE_E_LIBNOTREGISTERED ((HRESULT)0x8002801DL)
#define TYPE_E_UNDEFINEDTYPE ((HRESULT)0x80028027L)
#define TYPE_E_QUALIFIEDNAMEDISALLOWED ((HRESULT)0x80028028L)
#define TYPE_E_INVALIDSTATE ((HRESULT)0x80028029L)
#define TYPE_E_WRONGTYPEKIND ((HRESULT)0x8002802AL)
#define TYPE_E_ELEMENTNOTFOUND ((HRESULT)0x8002802BL)
#define TYPE_E_AMBIGUOUSNAME ((HRESULT)0x8002802CL)
#define TYPE_E_NAMECONFLICT ((HRESULT)0x8002802DL)
#define TYPE_E_UNKNOWNLCID ((HRESULT)0x8002802EL)
#define TYPE_E_DLLFUNCTIONNOTFOUND ((HRESULT)0x8002802FL)
#define TYPE_E_BADMODULEKIND ((HRESULT)0x800288BDL)
#define TYPE_E_SIZETOOBIG ((HRESULT)0x800288C5L)
#define TYPE_E_DUPLICATEID ((HRESULT)0x800288C6L)
#define TYPE_E_INVALIDID ((HRESULT)0x800288CFL)
#define TYPE_E_TYPEMISMATCH ((HRESULT)0x80028CA0L)
#define TYPE_E_OUTOFBOUNDS ((HRESULT)0x80028CA1L)
#define TYPE_E_IOERROR ((HRESULT)0x80028CA2L)
#define TYPE_E_CANTCREATETMPFILE ((HRESULT)0x80028CA3L)
#define TYPE_E_CANTLOADLIBRARY ((HRESULT)0x80029C4AL)
#define TYPE_E_INCONSISTENTPROPFUNCS ((HRESULT)0x80029C83L)
#define TYPE_E_CIRCULARTYPE ((HRESULT)0x80029C84L)
#define STG_E_INVALIDFUNCTION ((HRESULT)0x80030001L)
#define STG_E_FILENOTFOUND ((HRESULT)0x80030002L)
#define STG_E_PATHNOTFOUND ((HRESULT)0x80030003L)
#define STG_E_TOOMANYOPENFILES ((HRESULT)0x80030004L)
#define STG_E_ACCESSDENIED ((HRESULT)0x80030005L)
#define STG_E_INVALIDHANDLE ((HRESULT)0x80030006L)
#define STG_E_INSUFFICIENTMEMORY ((HRESULT)0x80030008L)
#define STG_E_INVALIDPOINTER ((HRESULT)0x80030009L)
#define STG_E_NOMOREFILES ((HRESULT)0x80030012L)
#define STG_E_DISKISWRITEPROTECTED ((HRESULT)0x80030013L)
#define STG_E_SEEKERROR ((HRESULT)0x80030019L)
#define STG_E_WRITEFAULT ((HRESULT)0x8003001DL)
#define STG_E_READFAULT ((HRESULT)0x8003001EL)
#define STG_E_SHAREVIOLATION ((HRESULT)0x80030020L)
#define STG_E_LOCKVIOLATION ((HRESULT)0x80030021L)
#define STG_E_FILEALREADYEXISTS ((HRESULT)0x80030050L)
#define STG_E_INVALIDPARAMETER ((HRESULT)0x80030057L)
#define STG_E_MEDIUMFULL ((HRESULT)0x80030070L)
#define STG_E_ABNORMALAPIEXIT ((HRESULT)0x800300FAL)
#define STG_E_INVALIDHEADER ((HRESULT)0x800300FBL)
#define STG_E_INVALIDNAME ((HRESULT)0x800300FCL)
#define STG_E_UNKNOWN ((HRESULT)0x800300FDL)
#define STG_E_UNIMPLEMENTEDFUNCTION ((HRESULT)0x800300FEL)
#define STG_E_INVALIDFLAG ((HRESULT)0x800300FFL)
#define STG_E_INUSE ((HRESULT)0x80030100L)
#define STG_E_NOTCURRENT ((HRESULT)0x80030101L)
#define STG_E_REVERTED ((HRESULT)0x80030102L)
#define STG_E_CANTSAVE ((HRESULT)0x80030103L)
#define STG_E_OLDFORMAT ((HRESULT)0x80030104L)
#define STG_E_OLDDLL ((HRESULT)0x80030105L)
#define STG_E_SHAREREQUIRED ((HRESULT)0x80030106L)
#define STG_E_NOTFILEBASEDSTORAGE ((HRESULT)0x80030107L)
#define STG_E_EXTANTMARSHALLINGS ((HRESULT)0x80030108L)
#define STG_S_CONVERTED ((HRESULT)0x00030200L)
#define RPC_E_CALL_REJECTED ((HRESULT)0x80010001L)
#define RPC_E_CALL_CANCELED ((HRESULT)0x80010002L)
#define RPC_E_CANTPOST_INSENDCALL ((HRESULT)0x80010003L)
#define RPC_E_CANTCALLOUT_INASYNCCALL ((HRESULT)0x80010004L)
#define RPC_E_CANTCALLOUT_INEXTERNALCALL ((HRESULT)0x80010005L)
#define RPC_E_CONNECTION_TERMINATED ((HRESULT)0x80010006L)
#define RPC_E_SERVER_DIED ((HRESULT)0x80010007L)
#define RPC_E_CLIENT_DIED ((HRESULT)0x80010008L)
#define RPC_E_INVALID_DATAPACKET ((HRESULT)0x80010009L)
#define RPC_E_CANTTRANSMIT_CALL ((HRESULT)0x8001000AL)
#define RPC_E_CLIENT_CANTMARSHAL_DATA ((HRESULT)0x8001000BL)
#define RPC_E_CLIENT_CANTUNMARSHAL_DATA ((HRESULT)0x8001000CL)
#define RPC_E_SERVER_CANTMARSHAL_DATA ((HRESULT)0x8001000DL)
#define RPC_E_SERVER_CANTUNMARSHAL_DATA ((HRESULT)0x8001000EL)
#define RPC_E_INVALID_DATA ((HRESULT)0x8001000FL)
#define RPC_E_INVALID_PARAMETER ((HRESULT)0x80010010L)
#define RPC_E_CANTCALLOUT_AGAIN ((HRESULT)0x80010011L)
#define RPC_E_SERVER_DIED_DNE ((HRESULT)0x80010012L)
#define RPC_E_SYS_CALL_FAILED ((HRESULT)0x80010100L)
#define RPC_E_OUT_OF_RESOURCES ((HRESULT)0x80010101L)
#define RPC_E_ATTEMPTED_MULTITHREAD ((HRESULT)0x80010102L)
#define RPC_E_NOT_REGISTERED ((HRESULT)0x80010103L)
#define RPC_E_FAULT ((HRESULT)0x80010104L)
#define RPC_E_SERVERFAULT ((HRESULT)0x80010105L)
#define RPC_E_CHANGED_MODE ((HRESULT)0x80010106L)
#define RPC_E_INVALIDMETHOD ((HRESULT)0x80010107L)
#define RPC_E_DISCONNECTED ((HRESULT)0x80010108L)
#define RPC_E_RETRY ((HRESULT)0x80010109L)
#define RPC_E_SERVERCALL_RETRYLATER ((HRESULT)0x8001010AL)
#define RPC_E_SERVERCALL_REJECTED ((HRESULT)0x8001010BL)
#define RPC_E_INVALID_CALLDATA ((HRESULT)0x8001010CL)
#define RPC_E_CANTCALLOUT_ININPUTSYNCCALL ((HRESULT)0x8001010DL)
#define RPC_E_WRONG_THREAD ((HRESULT)0x8001010EL)
#define RPC_E_THREAD_NOT_INIT ((HRESULT)0x8001010FL)
#define RPC_E_UNEXPECTED ((HRESULT)0x8001FFFFL)

#define NTE_BAD_UID ((HRESULT)0x80090001L)
#define NTE_BAD_HASH ((HRESULT)0x80090002L)
#define NTE_BAD_KEY ((HRESULT)0x80090003L)
#define NTE_BAD_LEN ((HRESULT)0x80090004L)
#define NTE_BAD_DATA ((HRESULT)0x80090005L)
#define NTE_BAD_SIGNATURE ((HRESULT)0x80090006L)
#define NTE_BAD_VER ((HRESULT)0x80090007L)
#define NTE_BAD_ALGID ((HRESULT)0x80090008L)
#define NTE_BAD_FLAGS ((HRESULT)0x80090009L)
#define NTE_BAD_TYPE ((HRESULT)0x8009000AL)
#define NTE_BAD_KEY_STATE ((HRESULT)0x8009000BL)
#define NTE_BAD_HASH_STATE ((HRESULT)0x8009000CL)
#define NTE_NO_KEY ((HRESULT)0x8009000DL)
#define NTE_NO_MEMORY ((HRESULT)0x8009000EL)
#define NTE_EXISTS ((HRESULT)0x8009000FL)
#define NTE_PERM ((HRESULT)0x80090010L)
#define NTE_NOT_FOUND ((HRESULT)0x80090011L)
#define NTE_DOUBLE_ENCRYPT ((HRESULT)0x80090012L)
#define NTE_BAD_PROVIDER ((HRESULT)0x80090013L)
#define NTE_BAD_PROV_TYPE ((HRESULT)0x80090014L)
#define NTE_BAD_PUBLIC_KEY ((HRESULT)0x80090015L)
#define NTE_BAD_KEYSET ((HRESULT)0x80090016L)
#define NTE_PROV_TYPE_NOT_DEF ((HRESULT)0x80090017L)
#define NTE_PROV_TYPE_ENTRY_BAD ((HRESULT)0x80090018L)
#define NTE_KEYSET_NOT_DEF ((HRESULT)0x80090019L)
#define NTE_KEYSET_ENTRY_BAD ((HRESULT)0x8009001AL)
#define NTE_PROV_TYPE_NO_MATCH ((HRESULT)0x8009001BL)
#define NTE_SIGNATURE_FILE_BAD ((HRESULT)0x8009001CL)
#define NTE_PROVIDER_DLL_FAIL ((HRESULT)0x8009001DL)
#define NTE_PROV_DLL_NOT_FOUND ((HRESULT)0x8009001EL)
#define NTE_BAD_KEYSET_PARAM ((HRESULT)0x8009001FL)
#define NTE_FAIL ((HRESULT)0x80090020L)
#define NTE_SYS_ERR ((HRESULT)0x80090021L)
/* #define NTE_TOKEN_KEYSET_STORAGE ??? */

#endif
