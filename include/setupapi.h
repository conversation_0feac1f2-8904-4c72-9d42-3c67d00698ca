#ifndef _SETUPAPI_H_
#define _SETUPAPI_H_
#if __GNUC__ >=3
#pragma GCC system_header
#endif

#include <commctrl.h>
#include <pshpack1.h>

#ifdef __cplusplus
extern "C" {
#endif

#define WINSETUPAPI DECLSPEC_IMPORT

#define LINE_LEN	256
#define MAX_INF_STRING_LENGTH	4096
#define MAX_TITLE_LEN	60
#define MAX_INSTRUCTION_LEN	256
#define MAX_LABEL_LEN	30
#define MAX_SERVICE_NAME_LEN	256
#define MAX_SUBTITLE_LEN	256
#define SP_MAX_MACHINENAME_LENGTH	(MAX_PATH + 3)

#define COPYFLG_WARN_IF_SKIP	0x00000001
#define COPYFLG_NOSKIP	0x00000002
#define COPYFLG_NOVERSIONCHECK	0x00000004
#define COPYFLG_FORCE_FILE_IN_USE	0x00000008
#define COPYFLG_NO_OVERWRITE	0x00000010
#define COPYFLG_NO_VERSION_DIALOG	0x00000020
#define COPYFLG_OVERWRITE_OLDER_ONLY	0x00000040
#define COPYFLG_REPLACEONLY	0x00000400
#define COPYFLG_NODECOMP	0x00000800
#define COPYFLG_REPLACE_BOOT_FILE	0x00001000
#define COPYFLG_NOPRUNE	0x00002000
#define DELFLG_IN_USE	0x00000001
#define DELFLG_IN_USE1	0x00010000
#define DI_REMOVEDEVICE_GLOBAL	0x00000001
#define DI_REMOVEDEVICE_CONFIGSPECIFIC	0x00000002
#define DI_UNREMOVEDEVICE_CONFIGSPECIFIC	0x00000002
#define DI_SHOWOEM	0x00000001
#define DI_SHOWCOMPAT	0x00000002
#define DI_SHOWCLASS	0x00000004
#define DI_SHOWALL	0x00000007
#define DI_NOVCP	0x00000008
#define DI_DIDCOMPAT	0x00000010
#define DI_DIDCLASS	0x00000020
#define DI_AUTOASSIGNRES	0x00000040
#define DI_NEEDRESTART	0x00000080
#define DI_NEEDREBOOT	0x00000100
#define DI_NOBROWSE	0x00000200
#define DI_MULTMFGS	0x00000400
#define DI_DISABLED	0x00000800
#define DI_GENERALPAGE_ADDED	0x00001000
#define DI_RESOURCEPAGE_ADDED	0x00002000
#define DI_PROPERTIES_CHANGE	0x00004000
#define DI_INF_IS_SORTED	0x00008000
#define DI_ENUMSINGLEINF	0x00010000
#define DI_DONOTCALLCONFIGMG	0x00020000
#define DI_INSTALLDISABLED	0x00040000
#define DI_COMPAT_FROM_CLASS	0x00080000
#define DI_CLASSINSTALLPARAMS	0x00100000
#define DI_NODI_DEFAULTACTION	0x00200000
#define DI_QUIETINSTALL	0x00800000
#define DI_NOFILECOPY	0x01000000
#define DI_FORCECOPY	0x02000000
#define DI_DRIVERPAGE_ADDED	0x04000000
#define DI_USECI_SELECTSTRINGS	0x08000000
#define DI_OVERRIDE_INFFLAGS	0x10000000
#define DI_PROPS_NOCHANGEUSAGE	0x20000000
#define DI_NOSELECTICONS	0x40000000
#define DI_NOWRITE_IDS	0x80000000
#define DI_FLAGSEX_USEOLDINFSEARCH	0x00000001
#define DI_FLAGSEX_AUTOSELECTRANK0	0x00000002
#define DI_FLAGSEX_CI_FAILED	0x00000004
#define DI_FLAGSEX_DIDINFOLIST	0x00000010
#define DI_FLAGSEX_DIDCOMPATINFO	0x00000020
#define DI_FLAGSEX_FILTERCLASSES	0x00000040
#define DI_FLAGSEX_SETFAILEDINSTALL	0x00000080
#define DI_FLAGSEX_DEVICECHANGE	0x00000100
#define DI_FLAGSEX_ALWAYSWRITEIDS	0x00000200
#define DI_FLAGSEX_PROPCHANGE_PENDING	0x00000400
#define DI_FLAGSEX_ALLOWEXCLUDEDDRVS	0x00000800
#define DI_FLAGSEX_NOUIONQUERYREMOVE	0x00001000
#define DI_FLAGSEX_USECLASSFORCOMPAT	0x00002000
#define DI_FLAGSEX_OLDINF_IN_CLASSLIST	0x00004000
#define DI_FLAGSEX_NO_DRVREG_MODIFY	0x00008000
#define DI_FLAGSEX_IN_SYSTEM_SETUP	0x00010000
#define DI_FLAGSEX_INET_DRIVER	0x00020000
#define DI_FLAGSEX_APPENDDRIVERLIST	0x00040000
#define DI_FLAGSEX_PREINSTALLBACKUP	0x00080000
#define DI_FLAGSEX_BACKUPONREPLACE	0x00100000
#define DI_FLAGSEX_DRIVERLIST_FROM_URL	0x00200000
#define DI_FLAGSEX_RESERVED1	0x00400000
#define DI_FLAGSEX_EXCLUDE_OLD_INET_DRIVERS	0x00800000
#define DI_FLAGSEX_POWERPAGE_ADDED	0x01000000
#define DIBCI_NOINSTALLCLASS	0x00000001
#define DIBCI_NODISPLAYCLASS	0x00000002
#define DICD_GENERATE_ID	0x00000001
#define DICD_INHERIT_CLASSDRVS	0x00000002
#define DICS_ENABLE	1
#define DICS_FLAG_GLOBAL	1
#define DICS_DISABLE	2
#define DICS_FLAG_CONFIGSPECIFIC	2
#define DICS_PROPCHANGE	3
#define DICS_START	4
#define DICS_FLAG_CONFIGGENERAL	4
#define DICS_STOP	5
#define DIF_SELECTDEVICE	1
#define DIF_INSTALLDEVICE	2
#define DIF_ASSIGNRESOURCES	3
#define DIF_PROPERTIES	4
#define DIF_REMOVE	5
#define DIF_FIRSTTIMESETUP	6
#define DIF_FOUNDDEVICE	7
#define DIF_SELECTCLASSDRIVERS	8
#define DIF_VALIDATECLASSDRIVERS	9
#define DIF_INSTALLCLASSDRIVERS	10
#define DIF_CALCDISKSPACE	11
#define DIF_DESTROYPRIVATEDATA	12
#define DIF_VALIDATEDRIVER	13
#define DIF_MOVEDEVICE	14
#define DIF_DETECT	15
#define DIF_INSTALLWIZARD	16
#define DIF_DESTROYWIZARDDATA	17
#define DIF_PROPERTYCHANGE	18
#define DIF_ENABLECLASS	19
#define DIF_DETECTVERIFY	20
#define DIF_INSTALLDEVICEFILES	21
#define DIF_UNREMOVE	22
#define DIF_SELECTBESTCOMPATDRV	23
#define DIF_ALLOW_INSTALL	24
#define DIF_REGISTERDEVICE	25
#define DIF_NEWDEVICEWIZARD_PRESELECT	26
#define DIF_NEWDEVICEWIZARD_SELECT	27
#define DIF_NEWDEVICEWIZARD_PREANALYZE	28
#define DIF_NEWDEVICEWIZARD_POSTANALYZE	29
#define DIF_NEWDEVICEWIZARD_FINISHINSTALL	30
#define DIF_UNUSED1	31
#define DIF_INSTALLINTERFACES	32
#define DIF_DETECTCANCEL	33
#define DIF_REGISTER_COINSTALLERS	34
#define DIF_ADDPROPERTYPAGE_ADVANCED	35
#define DIF_ADDPROPERTYPAGE_BASIC	36
#define DIF_RESERVED1	37
#define DIF_TROUBLESHOOTER	38
#define DIF_POWERMESSAGEWAKE	39
#define DIGCF_DEFAULT	0x00000001
#define DIGCDP_FLAG_BASIC	0x00000001
#define DIGCF_PRESENT	0x00000002
#define DIGCDP_FLAG_ADVANCED	0x00000002
#define DIGCF_ALLCLASSES	0x00000004
#define DIGCF_PROFILE	0x00000008
#define DIGCF_DEVICEINTERFACE	0x00000010
#define DIGCF_INTERFACEDEVICE	0x00000010
#define DIOCR_INSTALLER	0x00000001
#define DIOCR_INTERFACE	0x00000002
#define DIODI_NO_ADD	0x00000001
#define DIOD_INHERIT_CLASSDRVS	0x00000002
#define DIOD_CANCEL_REMOVE	0x00000004
#define DIREG_DEV	0x00000001
#define DIREG_DRV	0x00000002
#define DIREG_BOTH	0x00000004
#define DIRID_ABSOLUTE	-1
#define DIRID_NULL	0
#define DIRID_SRCPATH	1
#define DIRID_WINDOWS	10
#define DIRID_SYSTEM	11
#define DIRID_DRIVERS	12
#define DIRID_INF	17
#define DIRID_HELP	18
#define DIRID_FONTS	20
#define DIRID_VIEWERS	21
#define DIRID_COLOR	23
#define DIRID_APPS	24
#define DIRID_SHARED	25
#define DIRID_BOOT	30
#define DIRID_SYSTEM16	50
#define DIRID_SPOOL	51
#define DIRID_SPOOLDRIVERS	52
#define DIRID_USERPROFILE	53
#define DIRID_LOADER	54
#define DIRID_PRINTPROCESSOR	55
#define DIRID_COMMON_STARTMENU	16406
#define DIRID_COMMON_PROGRAMS	16407
#define DIRID_COMMON_STARTUP	16408
#define DIRID_COMMON_DESKTOPDIRECTORY	16409
#define DIRID_COMMON_FAVORITES	16415
#define DIRID_COMMON_APPDATA	16419
#define DIRID_PROGRAM_FILES	16422
#define DIRID_SYSTEM_X86	16425
#define DIRID_PROGRAM_FILES_X86	16426
#define DIRID_PROGRAM_FILES_COMMON	16427
#define DIRID_PROGRAM_FILES_COMMONX86	16428
#define DIRID_COMMON_TEMPLATES	16429
#define DIRID_COMMON_DOCUMENTS	16430
#define DIRID_USER	0x8000
#define DIRID_ABSOLUTE_16BIT	0xffff
#define DIRID_IOSUBSYS	DIRID_DRIVERS
#define DIRID_DEFAULT	DIRID_SYSTEM
#define DMI_MASK	0x00000001
#define DMI_BKCOLOR	0x00000002
#define DMI_USERECT	0x00000004
#define DNF_DUPDESC	0x00000001
#define DNF_OLDDRIVER	0x00000002
#define DNF_EXCLUDEFROMLIST	0x00000004
#define DNF_NODRIVER	0x00000008
#define DNF_LEGACYINF	0x00000010
#define DNF_CLASS_DRIVER	0x00000020
#define DNF_COMPATIBLE_DRIVER	0x00000040
#define DNF_INET_DRIVER	0x00000080
#define DNF_UNUSED1	0x00000100
#define DNF_INDEXED_DRIVER	0x00000200
#define DNF_OLD_INET_DRIVER	0x00000400
#define DNF_BAD_DRIVER	0x00000800
#define DNF_DUPPROVIDER	0x00001000
#define DPROMPT_SUCCESS	0
#define DPROMPT_CANCEL	1
#define DPROMPT_SKIPFILE	2
#define DPROMPT_BUFFERTOOSMALL	3
#define DPROMPT_OUTOFMEMORY	4
#define DRIVER_HARDWAREID_RANK	0x00000FFF
#define DYNAWIZ_FLAG_PAGESADDED	0x00000001
#define DYNAWIZ_FLAG_INSTALLDET_NEXT	0x00000002
#define DYNAWIZ_FLAG_INSTALLDET_PREV	0x00000004
#define DYNAWIZ_FLAG_ANALYZE_HANDLECONFLICT	0x00000008
#define ENABLECLASS_QUERY	0
#define ENABLECLASS_SUCCESS	1
#define ENABLECLASS_FAILURE	2
#define ERROR_EXPECTED_SECTION_NAME	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0)
#define ERROR_BAD_SECTION_NAME_LINE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|1)
#define ERROR_SECTION_NAME_TOO_LONG	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|2)
#define ERROR_GENERAL_SYNTAX	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|3)
#define ERROR_WRONG_INF_STYLE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x100)
#define ERROR_NOT_INSTALLED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x1000)
#define ERROR_SECTION_NOT_FOUND	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x101)
#define ERROR_LINE_NOT_FOUND	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x102)
#define ERROR_NO_BACKUP	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x103)
#define ERROR_NO_ASSOCIATED_CLASS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x200)
#define ERROR_CLASS_MISMATCH	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x201)
#define ERROR_DUPLICATE_FOUND	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x202)
#define ERROR_NO_DRIVER_SELECTED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x203)
#define ERROR_KEY_DOES_NOT_EXIST	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x204)
#define ERROR_INVALID_DEVINST_NAME	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x205)
#define ERROR_INVALID_CLASS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x206)
#define ERROR_DEVINST_ALREADY_EXISTS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x207)
#define ERROR_DEVINFO_NOT_REGISTERED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x208)
#define ERROR_INVALID_REG_PROPERTY	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x209)
#define ERROR_NO_INF	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20A)
#define ERROR_NO_SUCH_DEVINST	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20B)
#define ERROR_CANT_LOAD_CLASS_ICON	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20C)
#define ERROR_INVALID_CLASS_INSTALLER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20D)
#define ERROR_DI_DO_DEFAULT	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20E)
#define ERROR_DI_NOFILECOPY	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x20F)
#define ERROR_INVALID_HWPROFILE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x210)
#define ERROR_NO_DEVICE_SELECTED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x211)
#define ERROR_DEVINFO_LIST_LOCKED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x212)
#define ERROR_DEVINFO_DATA_LOCKED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x213)
#define ERROR_DI_BAD_PATH	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x214)
#define ERROR_NO_CLASSINSTALL_PARAMS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x215)
#define ERROR_FILEQUEUE_LOCKED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x216)
#define ERROR_BAD_SERVICE_INSTALLSECT	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x217)
#define ERROR_NO_CLASS_DRIVER_LIST	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x218)
#define ERROR_NO_ASSOCIATED_SERVICE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x219)
#define ERROR_NO_DEFAULT_DEVICE_INTERFACE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21A)
#define ERROR_DEVICE_INTERFACE_ACTIVE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21B)
#define ERROR_DEVICE_INTERFACE_REMOVED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21C)
#define ERROR_BAD_INTERFACE_INSTALLSECT	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21D)
#define ERROR_NO_SUCH_INTERFACE_CLASS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21E)
#define ERROR_INVALID_REFERENCE_STRING	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x21F)
#define ERROR_INVALID_MACHINENAME	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x220)
#define ERROR_REMOTE_COMM_FAILURE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x221)
#define ERROR_MACHINE_UNAVAILABLE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x222)
#define ERROR_NO_CONFIGMGR_SERVICES	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x223)
#define ERROR_INVALID_PROPPAGE_PROVIDER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x224)
#define ERROR_NO_SUCH_DEVICE_INTERFACE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x225)
#define ERROR_DI_POSTPROCESSING_REQUIRED	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x226)
#define ERROR_INVALID_COINSTALLER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x227)
#define ERROR_NO_COMPAT_DRIVERS	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x228)
#define ERROR_NO_DEVICE_ICON	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x229)
#define ERROR_INVALID_INF_LOGCONFIG	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22A)
#define ERROR_DI_DONT_INSTALL	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22B)
#define ERROR_INVALID_FILTER_DRIVER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22C)
#define ERROR_NON_WINDOWS_NT_DRIVER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22D)
#define ERROR_NON_WINDOWS_DRIVER	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22E)
#define ERROR_NO_CATALOG_FOR_OEM_INF	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x22F)
#define ERROR_DEVINSTALL_QUEUE_NONNATIVE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x230)
#define ERROR_NOT_DISABLEABLE	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x231)
#define ERROR_CANT_REMOVE_DEVINST	(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR|0x232)
#define ERROR_INTERFACE_DEVICE_ACTIVE	ERROR_DEVICE_INTERFACE_ACTIVE
#define ERROR_INTERFACE_DEVICE_REMOVED	ERROR_DEVICE_INTERFACE_REMOVED
#define ERROR_NO_DEFAULT_INTERFACE_DEVICE	ERROR_NO_DEFAULT_DEVICE_INTERFACE
#define ERROR_NO_SUCH_INTERFACE_DEVICE	ERROR_NO_SUCH_DEVICE_INTERFACE

#define FILEOP_COPY	0
#define FILEOP_ABORT	0
#define FILE_COMPRESSION_NONE	0
#define FILEOP_RENAME	1
#define FILEOP_DOIT	1
#define FILE_COMPRESSION_WINLZA	1
#define FILEOP_DELETE	2
#define FILEOP_SKIP	2
#define FILE_COMPRESSION_MSZIP	2
#define FILEOP_BACKUP	3
#define FILE_COMPRESSION_NTCAB	3
#define FILEOP_NEWPATH	4
#define FILEOP_RETRY	FILEOP_DOIT
#define FLG_ADDREG_TYPE_SZ	0x00000000
#define FLG_ADDREG_BINVALUETYPE	0x00000001
#define FLG_ADDREG_NOCLOBBER	0x00000002
#define FLG_ADDREG_DELVAL	0x00000004
#define FLG_ADDREG_APPEND	0x00000008
#define FLG_ADDREG_KEYONLY	0x00000010
#define FLG_ADDREG_OVERWRITEONLY	0x00000020
#if (_SETUPAPI_VER >= 0x0501)
#define FLG_ADDREG_64BITKEY	0x00001000
#define FLG_ADDREG_KEYONLY_COMMON	0x00002000
#define FLG_ADDREG_32BITKEY	0x00004000
#define FLG_ADDREG_DELREG_BIT	0x00008000
#endif
#define FLG_ADDREG_TYPE_MULTI_SZ	0x00010000
#define FLG_ADDREG_TYPE_EXPAND_SZ	0x00020000
#define FLG_ADDREG_TYPE_BINARY	(0x00000000|FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_DWORD	(0x00010000|FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_NONE	(0x00020000|FLG_ADDREG_BINVALUETYPE)
#define FLG_ADDREG_TYPE_MASK	(0xFFFF0000|FLG_ADDREG_BINVALUETYPE)
#define FLG_DELREG_VALUE	0x00000000
#if (_SETUPAPI_VER >= 0x0501)
#define FLG_DELREG_TYPE_MASK	FLG_ADDREG_TYPE_MASK
#define FLG_DELREG_TYPE_SZ	FLG_ADDREG_TYPE_SZ
#define FLG_DELREG_TYPE_MULTI_SZ	FLG_ADDREG_TYPE_MULTI_SZ
#define FLG_DELREG_TYPE_EXPAND_SZ	FLG_ADDREG_TYPE_EXPAND_SZ
#define FLG_DELREG_TYPE_BINARY	FLG_ADDREG_TYPE_BINARY
#define FLG_DELREG_TYPE_DWORD	FLG_ADDREG_TYPE_DWORD
#define FLG_DELREG_TYPE_NONE	FLG_ADDREG_TYPE_NONE
#define FLG_DELREG_64BITKEY	FLG_ADDREG_64BITKEY
#define FLG_DELREG_KEYONLY_COMMON	FLG_ADDREG_KEYONLY_COMMON
#define FLG_DELREG_32BITKEY	FLG_ADDREG_32BITKEY
#define FLG_DELREG_OPERATION_MASK	0x000000FE
#define FLG_DELREG_MULTI_SZ_DELSTRING	(0x00000002|FLG_DELREG_TYPE_MULTI_SZ|FLG_ADDREG_DELREG_BIT)
#endif
#define FLG_BITREG_CLEARBITS	0x00000000
#define FLG_BITREG_SETBITS	0x00000001
#define FLG_PROFITEM_CURRENTUSER	0x00000001
#define FLG_PROFITEM_DELETE	0x00000002
#define FLG_PROFITEM_GROUP	0x00000004
#define FLG_PROFITEM_CSIDL	0x00000008
#define FLG_REGSVR_DLLREGISTER	0x00000001
#define FLG_REGSVR_DLLINSTALL	0x00000002
#define MIN_IDD_DYNAWIZ_RESOURCE_ID	10000
#define MAX_IDD_DYNAWIZ_RESOURCE_ID	11000
#define IDD_DYNAWIZ_FIRSTPAGE	10000
#define IDD_DYNAWIZ_SELECT_PREVPAGE	10001
#define IDD_DYNAWIZ_SELECT_NEXTPAGE	10002
#define IDD_DYNAWIZ_ANALYZE_PREVPAGE	10003
#define IDD_DYNAWIZ_ANALYZE_NEXTPAGE	10004
#define IDD_DYNAWIZ_INSTALLDETECTED_PREVPAGE	10006
#define IDD_DYNAWIZ_INSTALLDETECTED_NEXTPAGE	10007
#define IDD_DYNAWIZ_INSTALLDETECTED_NODEVS	10008
#define IDD_DYNAWIZ_SELECTDEV_PAGE	10009
#define IDD_DYNAWIZ_ANALYZEDEV_PAGE	10010
#define IDD_DYNAWIZ_INSTALLDETECTEDDEVS_PAGE	10011
#define IDD_DYNAWIZ_SELECTCLASS_PAGE	10012
#define IDF_NOBROWSE	0x00000001
#define IDF_NOSKIP	0x00000002
#define IDF_NODETAILS	0x00000004
#define IDF_NOCOMPRESSED	0x00000008
#define IDF_CHECKFIRST	0x00000100
#define IDF_NOBEEP	0x00000200
#define IDF_NOFOREGROUND	0x00000400
#define IDF_WARNIFSKIP	0x00000800
#define IDF_OEMDISK	0x80000000
#define IDI_RESOURCEFIRST	159
#define IDI_RESOURCE	159
#define IDI_RESOURCELAST	161
#define IDI_RESOURCEOVERLAYFIRST	161
#define IDI_RESOURCEOVERLAYLAST	161
#define IDI_CONFLICT	161
#define IDI_PROBLEM_OVL	500
#define IDI_DISABLED_OVL	501
#define IDI_FORCED_OVL	502
#define IDI_CLASSICON_OVERLAYFIRST	500
#define IDI_CLASSICON_OVERLAYLAST	502
#define INF_STYLE_NONE	0x00000000
#define INF_STYLE_OLDNT	0x00000001
#define INF_STYLE_WIN4	0x00000002
#define INF_STYLE_CACHE_ENABLE	0x00000010
#define INF_STYLE_CACHE_DISABLE	0x00000020
#define INFINFO_INF_SPEC_IS_HINF	1
#define INFINFO_INF_NAME_IS_ABSOLUTE	2
#define INFINFO_DEFAULT_SEARCH	3
#define INFINFO_REVERSE_DEFAULT_SEARCH	4
#define INFINFO_INF_PATH_LIST_SEARCH	5
#define LogSevInformation	0
#define LogSevWarning	1
#define LogSevError	2
#define LogSevFatalError	3
#define LogSevMaximum	4
#define LogSeverity	DWORD
#define MAX_INSTALLWIZARD_DYNAPAGES	20
#define NDW_INSTALLFLAG_DIDFACTDEFS	0x00000001
#define NDW_INSTALLFLAG_HARDWAREALLREADYIN	0x00000002
#define NDW_INSTALLFLAG_NEEDSHUTDOWN	0x00000200
#define NDW_INSTALLFLAG_EXPRESSINTRO	0x00000400
#define NDW_INSTALLFLAG_SKIPISDEVINSTALLED	0x00000800
#define NDW_INSTALLFLAG_NODETECTEDDEVS	0x00001000
#define NDW_INSTALLFLAG_INSTALLSPECIFIC	0x00002000
#define NDW_INSTALLFLAG_SKIPCLASSLIST	0x00004000
#define NDW_INSTALLFLAG_CI_PICKED_OEM	0x00008000
#define NDW_INSTALLFLAG_PCMCIAMODE	0x00010000
#define NDW_INSTALLFLAG_PCMCIADEVICE	0x00020000
#define NDW_INSTALLFLAG_USERCANCEL	0x00040000
#define NDW_INSTALLFLAG_KNOWNCLASS	0x00080000
#define NDW_INSTALLFLAG_NEEDRESTART	0x00000080
#define NDW_INSTALLFLAG_NEEDREBOOT	0x00000100
#define SETDIRID_NOT_FULL_PATH	0x00000001
#define SP_COPY_DELETESOURCE	0x0000001
#define SP_COPY_REPLACEONLY	0x0000002
#define SP_COPY_NEWER	0x0000004
#define SP_COPY_NEWER_OR_SAME	0x0000004
#define SP_COPY_NOOVERWRITE	0x0000008
#define SP_COPY_NODECOMP	0x0000010
#define SP_COPY_LANGUAGEAWARE	0x0000020
#define SP_COPY_SOURCE_ABSOLUTE	0x0000040
#define SP_COPY_SOURCEPATH_ABSOLUTE	0x0000080
#define SP_COPY_IN_USE_NEEDS_REBOOT	0x0000100
#define SP_COPY_FORCE_IN_USE	0x0000200
#define SP_COPY_NOSKIP	0x0000400
#define SP_COPY_FORCE_NOOVERWRITE	0x0001000
#define SP_COPY_FORCE_NEWER	0x0002000
#define SP_COPY_WARNIFSKIP	0x0004000
#define SP_COPY_NOBROWSE	0x0008000
#define SP_COPY_NEWER_ONLY	0x0010000
#define SP_COPY_SOURCE_SIS_MASTER	0x0020000
#define SP_COPY_OEMINF_CATALOG_ONLY	0x0040000
#define SP_COPY_REPLACE_BOOT_FILE	0x0080000
#define SP_COPY_NOPRUNE	0x0100000
#define SP_FLAG_CABINETCONTINUATION	0x0000800
#define SPCRP_SECURITY	23
#define SPCRP_SECURITY_SDS	24
#define SPCRP_DEVTYPE	25
#define SPCRP_EXCLUSIVE	26
#define SPCRP_CHARACTERISTICS	27
#define SPCRP_MAXIMUM_PROPERTY	28
#define SPDIT_NODRIVER	0
#define SPDIT_CLASSDRIVER	1
#define SPDIT_COMPATDRIVER	2
#define SPDRP_DEVICEDESC	0
#define SPDRP_HARDWAREID	1
#define SPDRP_COMPATIBLEIDS	2
#define SPDRP_UNUSED0	3
#define SPDRP_SERVICE	4
#define SPDRP_UNUSED1	5
#define SPDRP_UNUSED2	6
#define SPDRP_CLASS	7
#define SPDRP_CLASSGUID	8
#define SPDRP_DRIVER	9
#define SPDRP_CONFIGFLAGS	10
#define SPDRP_MFG	11
#define SPDRP_FRIENDLYNAME	12
#define SPDRP_LOCATION_INFORMATION	13
#define SPDRP_PHYSICAL_DEVICE_OBJECT_NAME	14
#define SPDRP_CAPABILITIES	15
#define SPDRP_UI_NUMBER	16
#define SPDRP_UPPERFILTERS	17
#define SPDRP_LOWERFILTERS	18
#define SPDRP_BUSTYPEGUID	19
#define SPDRP_LEGACYBUSTYPE	20
#define SPDRP_BUSNUMBER	21
#define SPDRP_ENUMERATOR_NAME	22
#define SPDRP_SECURITY	23
#define SPDRP_SECURITY_SDS	24
#define SPDRP_DEVTYPE	25
#define SPDRP_EXCLUSIVE	26
#define SPDRP_CHARACTERISTICS	27
#define SPDRP_ADDRESS	28
#define SPDRP_UI_NUMBER_DESC_FORMAT	30
#define SPDRP_MAXIMUM_PROPERTY	31
#define SPDSL_IGNORE_DISK	1
#define SPDSL_DISALLOW_NEGATIVE_ADJUST	2

#define SPFILENOTIFY_STARTQUEUE	1
#define SPFILENOTIFY_ENDQUEUE	2
#define SPFILENOTIFY_STARTSUBQUEUE	3
#define SPFILENOTIFY_ENDSUBQUEUE	4
#define SPFILENOTIFY_STARTDELETE	5
#define SPFILENOTIFY_ENDDELETE	6
#define SPFILENOTIFY_DELETEERROR	7
#define SPFILENOTIFY_STARTRENAME	8
#define SPFILENOTIFY_ENDRENAME	9
#define SPFILENOTIFY_RENAMEERROR	10
#define SPFILENOTIFY_STARTCOPY	11
#define SPFILENOTIFY_ENDCOPY	12
#define SPFILENOTIFY_COPYERROR	13
#define SPFILENOTIFY_NEEDMEDIA	14
#define SPFILENOTIFY_QUEUESCAN	15
#define SPFILENOTIFY_CABINETINFO	16
#define SPFILENOTIFY_FILEINCABINET	17
#define SPFILENOTIFY_NEEDNEWCABINET	18
#define SPFILENOTIFY_FILEEXTRACTED	19
#define SPFILENOTIFY_FILEOPDELAYED	20
#define SPFILENOTIFY_STARTBACKUP	21
#define SPFILENOTIFY_BACKUPERROR	22
#define SPFILENOTIFY_ENDBACKUP	23
#define SPFILENOTIFY_QUEUESCAN_EX	24
#define SPFILENOTIFY_LANGMISMATCH	0x00010000
#define SPFILENOTIFY_TARGETEXISTS	0x00020000
#define SPFILENOTIFY_TARGETNEWER	0x00040000
#define SPFILELOG_SYSTEMLOG	0x00000001
#define SPFILELOG_OEMFILE	0x00000001
#define SPFILELOG_FORCENEW	0x00000002
#define SPFILELOG_QUERYONLY	0x00000004
#define SPFILEQ_FILE_IN_USE	0x00000001
#define SPFILEQ_REBOOT_RECOMMENDED	0x00000002
#define SPFILEQ_REBOOT_IN_PROGRESS	0x00000004
#define SPINT_ACTIVE	0x00000001
#define SPINT_DEFAULT	0x00000002
#define SPINT_REMOVED	0x00000004
#define SPID_ACTIVE	0x00000001
#define SPID_DEFAULT	0x00000002
#define SPID_REMOVED	0x00000004
#define SPINST_LOGCONFIG	0x00000001
#define SPINST_INIFILES	0x00000002
#define SPINST_REGISTRY	0x00000004
#define SPINST_INI2REG	0x00000008
#define SPINST_FILES	0x00000010
#define SPINST_BITREG	0x00000020
#define SPINST_REGSVR	0x00000040
#define SPINST_UNREGSVR	0x00000080
#define SPINST_PROFILEITEMS	0x00000100
#if (_SETUPAPI_VER >= 0x0501)
#define SPINST_COPYINF	0x00000200
#define SPINST_ALL	0x000003ff
#else
#define SPINST_ALL	0x000001ff
#endif
#define SPINST_SINGLESECTION	0x00010000
#define SPINST_LOGCONFIG_IS_FORCED	0x00020000
#define SPINST_LOGCONFIGS_ARE_OVERRIDES	0x00040000
#define SPOST_NONE	0
#define SPOST_PATH	1
#define SPOST_URL	2
#define SPOST_MAX	3
#define SPPSR_SELECT_DEVICE_RESOURCES	1
#define SPPSR_ENUM_BASIC_DEVICE_PROPERTIES	2
#define SPPSR_ENUM_ADV_DEVICE_PROPERTIES	3
#define SPQ_SCAN_FILE_PRESENCE	0x00000001
#define SPQ_DELAYED_COPY	0x00000001
#define SPQ_SCAN_FILE_VALIDITY	0x00000002
#define SPQ_SCAN_USE_CALLBACK	0x00000004
#define SPQ_SCAN_USE_CALLBACKEX	0x00000008
#define SPQ_SCAN_INFORM_USER	0x00000010
#define SPQ_SCAN_PRUNE_COPY_QUEUE	0x00000020
#define SPRDI_FIND_DUPS	0x00000001
#define SPSVCINST_TAGTOFRONT	0x00000001
#define SPSVCINST_ASSOCSERVICE	0x00000002
#define SPSVCINST_DELETEEVENTLOGENTRY	0x00000004
#define SPSVCINST_NOCLOBBER_DISPLAYNAME	0x00000008
#define SPSVCINST_NOCLOBBER_STARTTYPE	0x00000010
#define SPSVCINST_NOCLOBBER_ERRORCONTROL	0x00000020
#define SPSVCINST_NOCLOBBER_LOADORDERGROUP	0x00000040
#define SPSVCINST_NOCLOBBER_DEPENDENCIES	0x00000080
#define SPSVCINST_NOCLOBBER_DESCRIPTION	0x00000100
#define SPSVCINST_STOPSERVICE	0x00000200
#define SPWPT_SELECTDEVICE	0x00000001
#define SPWP_USE_DEVINFO_DATA	0x00000001
#define SRCINFO_PATH	1
#define SRCINFO_TAGFILE	2
#define SRCINFO_DESCRIPTION	3
#define SRCINFO_FLAGS	4
#define SRCLIST_TEMPORARY	0x00000001
#define SRCLIST_NOBROWSE	0x00000002
#define SRCLIST_SYSTEM	0x00000010
#define SRCLIST_USER	0x00000020
#define SRCLIST_SYSIFADMIN	0x00000040
#define SRCLIST_SUBDIRS	0x00000100
#define SRCLIST_APPEND	0x00000200
#define SRCLIST_NOSTRIPPLATFORM	0x00000400

#ifndef RC_INVOKED
typedef PVOID HINF;
typedef PVOID HDSKSPC;
typedef PVOID HDEVINFO;
typedef PVOID HSPFILEQ;
typedef PVOID HSPFILELOG;
typedef UINT DI_FUNCTION;

typedef enum {
    SetupFileLogSourceFilename,
    SetupFileLogChecksum,
    SetupFileLogDiskTagfile,
    SetupFileLogDiskDescription,
    SetupFileLogOtherInfo,
    SetupFileLogMax
} SetupFileLogInfo;
typedef struct _INFCONTEXT {
    PVOID Inf;
    PVOID CurrentInf;
    UINT Section;
    UINT Line;
} INFCONTEXT, *PINFCONTEXT;
typedef struct _SP_INF_INFORMATION {
    DWORD InfStyle;
    DWORD InfCount;
    BYTE VersionData[ANYSIZE_ARRAY];
} SP_INF_INFORMATION, *PSP_INF_INFORMATION;
typedef struct _SP_ALTPLATFORM_INFO {
    DWORD cbSize;
    DWORD Platform;
    DWORD MajorVersion;
    DWORD MinorVersion;
    WORD  ProcessorArchitecture;
    WORD  Reserved;
} SP_ALTPLATFORM_INFO, *PSP_ALTPLATFORM_INFO;
typedef struct _SP_ORIGINAL_FILE_INFO_A {
    DWORD  cbSize;
    CHAR   OriginalInfName[MAX_PATH];
    CHAR   OriginalCatalogName[MAX_PATH];
} SP_ORIGINAL_FILE_INFO_A, *PSP_ORIGINAL_FILE_INFO_A;
typedef struct _SP_ORIGINAL_FILE_INFO_W {
    DWORD  cbSize;
    WCHAR  OriginalInfName[MAX_PATH];
    WCHAR  OriginalCatalogName[MAX_PATH];
} SP_ORIGINAL_FILE_INFO_W, *PSP_ORIGINAL_FILE_INFO_W;
typedef struct _FILEPATHS_A {
    PCSTR  Target;
    PCSTR  Source;
    UINT   Win32Error;
    DWORD  Flags;
} FILEPATHS_A, *PFILEPATHS_A;
typedef struct _FILEPATHS_W {
    PCWSTR Target;
    PCWSTR Source;
    UINT   Win32Error;
    DWORD  Flags;
} FILEPATHS_W, *PFILEPATHS_W;
typedef struct _SOURCE_MEDIA_A {
    PCSTR Reserved;
    PCSTR Tagfile;
    PCSTR Description;
    PCSTR SourcePath;
    PCSTR SourceFile;
    DWORD Flags;
} SOURCE_MEDIA_A, *PSOURCE_MEDIA_A;
typedef struct _SOURCE_MEDIA_W {
    PCWSTR Reserved;
    PCWSTR Tagfile;
    PCWSTR Description;
    PCWSTR SourcePath;
    PCWSTR SourceFile;
    DWORD  Flags;
} SOURCE_MEDIA_W, *PSOURCE_MEDIA_W;
typedef struct _CABINET_INFO_A {
    PCSTR CabinetPath;
    PCSTR CabinetFile;
    PCSTR DiskName;
    USHORT SetId;
    USHORT CabinetNumber;
} CABINET_INFO_A, *PCABINET_INFO_A;
typedef struct _CABINET_INFO_W {
    PCWSTR CabinetPath;
    PCWSTR CabinetFile;
    PCWSTR DiskName;
    USHORT SetId;
    USHORT CabinetNumber;
} CABINET_INFO_W, *PCABINET_INFO_W;
typedef struct _FILE_IN_CABINET_INFO_A {
    PCSTR NameInCabinet;
    DWORD FileSize;
    DWORD Win32Error;
    WORD  DosDate;
    WORD  DosTime;
    WORD  DosAttribs;
    CHAR  FullTargetName[MAX_PATH];
} FILE_IN_CABINET_INFO_A, *PFILE_IN_CABINET_INFO_A;
typedef struct _FILE_IN_CABINET_INFO_W {
    PCWSTR NameInCabinet;
    DWORD  FileSize;
    DWORD  Win32Error;
    WORD   DosDate;
    WORD   DosTime;
    WORD   DosAttribs;
    WCHAR  FullTargetName[MAX_PATH];
} FILE_IN_CABINET_INFO_W, *PFILE_IN_CABINET_INFO_W;
typedef struct _SP_FILE_COPY_PARAMS_A {
    DWORD    cbSize;
    HSPFILEQ QueueHandle;
    PCSTR    SourceRootPath;
    PCSTR    SourcePath;
    PCSTR    SourceFilename;
    PCSTR    SourceDescription;
    PCSTR    SourceTagfile;
    PCSTR    TargetDirectory;
    PCSTR    TargetFilename;
    DWORD    CopyStyle;
    HINF     LayoutInf;
    PCSTR    SecurityDescriptor;
} SP_FILE_COPY_PARAMS_A, *PSP_FILE_COPY_PARAMS_A;
typedef struct _SP_FILE_COPY_PARAMS_W {
    DWORD    cbSize;
    HSPFILEQ QueueHandle;
    PCWSTR   SourceRootPath;
    PCWSTR   SourcePath;
    PCWSTR   SourceFilename;
    PCWSTR   SourceDescription;
    PCWSTR   SourceTagfile;
    PCWSTR   TargetDirectory;
    PCWSTR   TargetFilename;
    DWORD    CopyStyle;
    HINF     LayoutInf;
    PCWSTR   SecurityDescriptor;
} SP_FILE_COPY_PARAMS_W, *PSP_FILE_COPY_PARAMS_W;
typedef struct _SP_DEVINFO_DATA {
    DWORD cbSize;
    GUID  ClassGuid;
    DWORD DevInst;
    ULONG_PTR Reserved;
} SP_DEVINFO_DATA, *PSP_DEVINFO_DATA;
typedef struct _SP_DEVICE_INTERFACE_DATA {
    DWORD cbSize;
    GUID  InterfaceClassGuid;
    DWORD Flags;
    ULONG_PTR Reserved;
} SP_DEVICE_INTERFACE_DATA, *PSP_DEVICE_INTERFACE_DATA;
/* For backward compatability */
typedef SP_DEVICE_INTERFACE_DATA  SP_INTERFACE_DEVICE_DATA, *PSP_INTERFACE_DEVICE_DATA;

typedef struct _SP_DEVICE_INTERFACE_DETAIL_DATA_A {
    DWORD  cbSize;
    CHAR   DevicePath[ANYSIZE_ARRAY];
} SP_DEVICE_INTERFACE_DETAIL_DATA_A, *PSP_DEVICE_INTERFACE_DETAIL_DATA_A;
typedef struct _SP_DEVICE_INTERFACE_DETAIL_DATA_W {
    DWORD  cbSize;
    WCHAR  DevicePath[ANYSIZE_ARRAY];
} SP_DEVICE_INTERFACE_DETAIL_DATA_W, *PSP_DEVICE_INTERFACE_DETAIL_DATA_W;
/* For backward compatability */
typedef SP_DEVICE_INTERFACE_DETAIL_DATA_A SP_INTERFACE_DEVICE_DETAIL_DATA_A,
    *PSP_INTERFACE_DEVICE_DETAIL_DATA_A;
typedef SP_DEVICE_INTERFACE_DETAIL_DATA_W SP_INTERFACE_DEVICE_DETAIL_DATA_W,
    *PSP_INTERFACE_DEVICE_DETAIL_DATA_W;

typedef struct _SP_DEVINFO_LIST_DETAIL_DATA_A {
    DWORD  cbSize;
    GUID   ClassGuid;
    HANDLE RemoteMachineHandle;
    CHAR   RemoteMachineName[SP_MAX_MACHINENAME_LENGTH];
} SP_DEVINFO_LIST_DETAIL_DATA_A, *PSP_DEVINFO_LIST_DETAIL_DATA_A;
typedef struct _SP_DEVINFO_LIST_DETAIL_DATA_W {
    DWORD  cbSize;
    GUID   ClassGuid;
    HANDLE RemoteMachineHandle;
    WCHAR  RemoteMachineName[SP_MAX_MACHINENAME_LENGTH];
} SP_DEVINFO_LIST_DETAIL_DATA_W, *PSP_DEVINFO_LIST_DETAIL_DATA_W;

typedef UINT (CALLBACK* PSP_FILE_CALLBACK_A)(PVOID,UINT,UINT_PTR,UINT_PTR);
typedef UINT (CALLBACK* PSP_FILE_CALLBACK_W)(PVOID,UINT,UINT_PTR,UINT_PTR);
typedef struct _SP_DEVINSTALL_PARAMS_A {
    DWORD             cbSize;
    DWORD             Flags;
    DWORD             FlagsEx;
    HWND              hwndParent;
    PSP_FILE_CALLBACK_A InstallMsgHandler;
    PVOID             InstallMsgHandlerContext;
    HSPFILEQ          FileQueue;
    ULONG_PTR         ClassInstallReserved;
    DWORD             Reserved;
    CHAR              DriverPath[MAX_PATH];
} SP_DEVINSTALL_PARAMS_A, *PSP_DEVINSTALL_PARAMS_A;
typedef struct _SP_DEVINSTALL_PARAMS_W {
    DWORD             cbSize;
    DWORD             Flags;
    DWORD             FlagsEx;
    HWND              hwndParent;
    PSP_FILE_CALLBACK_W InstallMsgHandler;
    PVOID             InstallMsgHandlerContext;
    HSPFILEQ          FileQueue;
    ULONG_PTR         ClassInstallReserved;
    DWORD             Reserved;
    WCHAR             DriverPath[MAX_PATH];
} SP_DEVINSTALL_PARAMS_W, *PSP_DEVINSTALL_PARAMS_W;
typedef struct _SP_CLASSINSTALL_HEADER {
    DWORD       cbSize;
    DI_FUNCTION InstallFunction;
} SP_CLASSINSTALL_HEADER, *PSP_CLASSINSTALL_HEADER;
typedef struct _SP_ENABLECLASS_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    GUID                   ClassGuid;
    DWORD                  EnableMessage;
} SP_ENABLECLASS_PARAMS, *PSP_ENABLECLASS_PARAMS;
typedef struct _SP_MOVEDEV_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    SP_DEVINFO_DATA        SourceDeviceInfoData;
} SP_MOVEDEV_PARAMS, *PSP_MOVEDEV_PARAMS;
typedef struct _SP_PROPCHANGE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD                  StateChange;
    DWORD                  Scope;
    DWORD                  HwProfile;
} SP_PROPCHANGE_PARAMS, *PSP_PROPCHANGE_PARAMS;
typedef struct _SP_REMOVEDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Scope;
    DWORD HwProfile;
} SP_REMOVEDEVICE_PARAMS, *PSP_REMOVEDEVICE_PARAMS;
typedef struct _SP_UNREMOVEDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD Scope;
    DWORD HwProfile;
} SP_UNREMOVEDEVICE_PARAMS, *PSP_UNREMOVEDEVICE_PARAMS;
typedef struct _SP_SELECTDEVICE_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR                   Title[MAX_TITLE_LEN];
    CHAR                   Instructions[MAX_INSTRUCTION_LEN];
    CHAR                   ListLabel[MAX_LABEL_LEN];
    CHAR                   SubTitle[MAX_SUBTITLE_LEN];
    BYTE                   Reserved[2];
} SP_SELECTDEVICE_PARAMS_A, *PSP_SELECTDEVICE_PARAMS_A;
typedef struct _SP_SELECTDEVICE_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR                  Title[MAX_TITLE_LEN];
    WCHAR                  Instructions[MAX_INSTRUCTION_LEN];
    WCHAR                  ListLabel[MAX_LABEL_LEN];
    WCHAR                  SubTitle[MAX_SUBTITLE_LEN];
} SP_SELECTDEVICE_PARAMS_W, *PSP_SELECTDEVICE_PARAMS_W;

typedef BOOL (CALLBACK* PDETECT_PROGRESS_NOTIFY)(PVOID,DWORD);
typedef struct _SP_DETECTDEVICE_PARAMS {
    SP_CLASSINSTALL_HEADER  ClassInstallHeader;
    PDETECT_PROGRESS_NOTIFY DetectProgressNotify;
    PVOID                   ProgressNotifyParam;
} SP_DETECTDEVICE_PARAMS, *PSP_DETECTDEVICE_PARAMS;
typedef struct _SP_INSTALLWIZARD_DATA {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD                  Flags;
    HPROPSHEETPAGE         DynamicPages[MAX_INSTALLWIZARD_DYNAPAGES];
    DWORD                  NumDynamicPages;
    DWORD                  DynamicPageFlags;
    DWORD                  PrivateFlags;
    LPARAM                 PrivateData;
    HWND                   hwndWizardDlg;
} SP_INSTALLWIZARD_DATA, *PSP_INSTALLWIZARD_DATA;
typedef struct _SP_NEWDEVICEWIZARD_DATA {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    DWORD                  Flags;
    HPROPSHEETPAGE         DynamicPages[MAX_INSTALLWIZARD_DYNAPAGES];
    DWORD                  NumDynamicPages;
    HWND                   hwndWizardDlg;
} SP_NEWDEVICEWIZARD_DATA, *PSP_NEWDEVICEWIZARD_DATA;
typedef SP_NEWDEVICEWIZARD_DATA SP_ADDPROPERTYPAGE_DATA,
    *PSP_ADDPROPERTYPAGE_DATA;
typedef struct _SP_TROUBLESHOOTER_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR                   ChmFile[MAX_PATH];
    CHAR                   HtmlTroubleShooter[MAX_PATH];
} SP_TROUBLESHOOTER_PARAMS_A, *PSP_TROUBLESHOOTER_PARAMS_A;
typedef struct _SP_TROUBLESHOOTER_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR                  ChmFile[MAX_PATH];
    WCHAR                  HtmlTroubleShooter[MAX_PATH];
} SP_TROUBLESHOOTER_PARAMS_W, *PSP_TROUBLESHOOTER_PARAMS_W;
typedef struct _SP_POWERMESSAGEWAKE_PARAMS_A {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    CHAR                   PowerMessageWake[LINE_LEN*2];
} SP_POWERMESSAGEWAKE_PARAMS_A, *PSP_POWERMESSAGEWAKE_PARAMS_A;
typedef struct _SP_POWERMESSAGEWAKE_PARAMS_W {
    SP_CLASSINSTALL_HEADER ClassInstallHeader;
    WCHAR                  PowerMessageWake[LINE_LEN*2];
} SP_POWERMESSAGEWAKE_PARAMS_W, *PSP_POWERMESSAGEWAKE_PARAMS_W;
typedef struct _SP_DRVINFO_DATA_V2_A {
    DWORD     cbSize;
    DWORD     DriverType;
    ULONG_PTR Reserved;
    CHAR      Description[LINE_LEN];
    CHAR      MfgName[LINE_LEN];
    CHAR      ProviderName[LINE_LEN];
    FILETIME  DriverDate;
    DWORDLONG DriverVersion;
} SP_DRVINFO_DATA_V2_A, *PSP_DRVINFO_DATA_V2_A;
typedef struct _SP_DRVINFO_DATA_V2_W {
    DWORD     cbSize;
    DWORD     DriverType;
    ULONG_PTR Reserved;
    WCHAR     Description[LINE_LEN];
    WCHAR     MfgName[LINE_LEN];
    WCHAR     ProviderName[LINE_LEN];
    FILETIME  DriverDate;
    DWORDLONG DriverVersion;
} SP_DRVINFO_DATA_V2_W, *PSP_DRVINFO_DATA_V2_W;
typedef struct _SP_DRVINFO_DATA_V1_A {
    DWORD     cbSize;
    DWORD     DriverType;
    ULONG_PTR Reserved;
    CHAR      Description[LINE_LEN];
    CHAR      MfgName[LINE_LEN];
    CHAR      ProviderName[LINE_LEN];
} SP_DRVINFO_DATA_V1_A, *PSP_DRVINFO_DATA_V1_A;
typedef struct _SP_DRVINFO_DATA_V1_W {
    DWORD     cbSize;
    DWORD     DriverType;
    ULONG_PTR Reserved;
    WCHAR     Description[LINE_LEN];
    WCHAR     MfgName[LINE_LEN];
    WCHAR     ProviderName[LINE_LEN];
} SP_DRVINFO_DATA_V1_W, *PSP_DRVINFO_DATA_V1_W;

#ifdef UNICODE
typedef SP_DRVINFO_DATA_V1_W SP_DRVINFO_DATA_V1, *PSP_DRVINFO_DATA_V1;
typedef SP_DRVINFO_DATA_V2_W SP_DRVINFO_DATA_V2, *PSP_DRVINFO_DATA_V2;
#else
typedef SP_DRVINFO_DATA_V1_A SP_DRVINFO_DATA_V1, *PSP_DRVINFO_DATA_V1;
typedef SP_DRVINFO_DATA_V2_A SP_DRVINFO_DATA_V2, *PSP_DRVINFO_DATA_V2;
#endif

#if USE_SP_DRVINFO_DATA_V1
typedef SP_DRVINFO_DATA_V1_A SP_DRVINFO_DATA_A, *PSP_DRVINFO_DATA_A;
typedef SP_DRVINFO_DATA_V1_W SP_DRVINFO_DATA_W, *PSP_DRVINFO_DATA_W;
typedef SP_DRVINFO_DATA_V1 SP_DRVINFO_DATA, *PSP_DRVINFO_DATA;
#else
typedef SP_DRVINFO_DATA_V2_A SP_DRVINFO_DATA_A, *PSP_DRVINFO_DATA_A;
typedef SP_DRVINFO_DATA_V2_W SP_DRVINFO_DATA_W, *PSP_DRVINFO_DATA_W;
typedef SP_DRVINFO_DATA_V2 SP_DRVINFO_DATA, *PSP_DRVINFO_DATA;
#endif

typedef DWORD (CALLBACK* PSP_DETSIG_CMPPROC)(HDEVINFO,PSP_DEVINFO_DATA,PSP_DEVINFO_DATA,PVOID);

typedef struct _SP_DRVINFO_DETAIL_DATA_A {
    DWORD    cbSize;
    FILETIME InfDate;
    DWORD    CompatIDsOffset;
    DWORD    CompatIDsLength;
    ULONG_PTR Reserved;
    CHAR     SectionName[LINE_LEN];
    CHAR     InfFileName[MAX_PATH];
    CHAR     DrvDescription[LINE_LEN];
    CHAR     HardwareID[ANYSIZE_ARRAY];
} SP_DRVINFO_DETAIL_DATA_A, *PSP_DRVINFO_DETAIL_DATA_A;
typedef struct _SP_DRVINFO_DETAIL_DATA_W {
    DWORD    cbSize;
    FILETIME InfDate;
    DWORD    CompatIDsOffset;
    DWORD    CompatIDsLength;
    ULONG_PTR Reserved;
    WCHAR    SectionName[LINE_LEN];
    WCHAR    InfFileName[MAX_PATH];
    WCHAR    DrvDescription[LINE_LEN];
    WCHAR    HardwareID[ANYSIZE_ARRAY];
} SP_DRVINFO_DETAIL_DATA_W, *PSP_DRVINFO_DETAIL_DATA_W;
typedef struct _SP_DRVINSTALL_PARAMS {
    DWORD cbSize;
    DWORD Rank;
    DWORD Flags;
    DWORD_PTR PrivateData;
    DWORD Reserved;
} SP_DRVINSTALL_PARAMS, *PSP_DRVINSTALL_PARAMS;


typedef struct _COINSTALLER_CONTEXT_DATA {
    BOOL  PostProcessing;
    DWORD InstallResult;
    PVOID PrivateData;
} COINSTALLER_CONTEXT_DATA, *PCOINSTALLER_CONTEXT_DATA;
typedef struct _SP_CLASSIMAGELIST_DATA {
    DWORD      cbSize;
    HIMAGELIST ImageList;
    ULONG_PTR  Reserved;
} SP_CLASSIMAGELIST_DATA, *PSP_CLASSIMAGELIST_DATA;
typedef struct _SP_PROPSHEETPAGE_REQUEST {
    DWORD            cbSize;
    DWORD            PageRequested;
    HDEVINFO         DeviceInfoSet;
    PSP_DEVINFO_DATA DeviceInfoData;
} SP_PROPSHEETPAGE_REQUEST, *PSP_PROPSHEETPAGE_REQUEST;
typedef struct _SP_BACKUP_QUEUE_PARAMS_A {
    DWORD    cbSize;
    CHAR     FullInfPath[MAX_PATH];
    INT      FilenameOffset;
} SP_BACKUP_QUEUE_PARAMS_A, *PSP_BACKUP_QUEUE_PARAMS_A;
typedef struct _SP_BACKUP_QUEUE_PARAMS_W {
    DWORD    cbSize;
    WCHAR    FullInfPath[MAX_PATH];
    INT      FilenameOffset;
} SP_BACKUP_QUEUE_PARAMS_W, *PSP_BACKUP_QUEUE_PARAMS_W;


#ifdef UNICODE
typedef SP_ORIGINAL_FILE_INFO_W SP_ORIGINAL_FILE_INFO, *PSP_ORIGINAL_FILE_INFO;
typedef FILEPATHS_W FILEPATHS, *PFILEPATHS;
typedef SOURCE_MEDIA_W SOURCE_MEDIA, *PSOURCE_MEDIA;
typedef CABINET_INFO_W CABINET_INFO, *PCABINET_INFO;
typedef FILE_IN_CABINET_INFO_W FILE_IN_CABINET_INFO, *PFILE_IN_CABINET_INFO;
typedef SP_FILE_COPY_PARAMS_W SP_FILE_COPY_PARAMS, PSP_FILE_COPY_PARAMS;
typedef SP_DEVICE_INTERFACE_DETAIL_DATA_W SP_DEVICE_INTERFACE_DETAIL_DATA,
    *PSP_DEVICE_INTERFACE_DETAIL_DATA;
typedef SP_INTERFACE_DEVICE_DETAIL_DATA_W SP_INTERFACE_DEVICE_DETAIL_DATA,
    *PSP_INTERFACE_DEVICE_DETAIL_DATA; /* deprecated */
typedef SP_DEVINFO_LIST_DETAIL_DATA_W SP_DEVINFO_LIST_DETAIL_DATA,
    *PSP_DEVINFO_LIST_DETAIL_DATA;
typedef SP_DEVINSTALL_PARAMS_W SP_DEVINSTALL_PARAMS, *PSP_DEVINSTALL_PARAMS;
typedef SP_SELECTDEVICE_PARAMS_W SP_SELECTDEVICE_PARAMS, *PSP_SELECTDEVICE_PARAMS;
typedef SP_TROUBLESHOOTER_PARAMS_W SP_TROUBLESHOOTER_PARAMS,
    *PSP_TROUBLESHOOTER_PARAMS;
typedef SP_POWERMESSAGEWAKE_PARAMS_W SP_POWERMESSAGEWAKE_PARAMS,
    *PSP_POWERMESSAGEWAKE_PARAMS;
typedef SP_DRVINFO_DETAIL_DATA_W SP_DRVINFO_DETAIL_DATA,
     *PSP_DRVINFO_DETAIL_DATA;
typedef SP_BACKUP_QUEUE_PARAMS_W SP_BACKUP_QUEUE_PARAMS,
    *PSP_BACKUP_QUEUE_PARAMS;
#else
typedef SP_ORIGINAL_FILE_INFO_A SP_ORIGINAL_FILE_INFO, *PSP_ORIGINAL_FILE_INFO;
typedef FILEPATHS_A FILEPATHS, *PFILEPATHS;
typedef SOURCE_MEDIA_A SOURCE_MEDIA, *PSOURCE_MEDIA;
typedef CABINET_INFO_A CABINET_INFO, *PCABINET_INFO;
typedef FILE_IN_CABINET_INFO_A FILE_IN_CABINET_INFO, *PFILE_IN_CABINET_INFO;
typedef SP_FILE_COPY_PARAMS_A SP_FILE_COPY_PARAMS, *PSP_FILE_COPY_PARAMS;
typedef SP_DEVICE_INTERFACE_DETAIL_DATA_A SP_DEVICE_INTERFACE_DETAIL_DATA,
    *PSP_DEVICE_INTERFACE_DETAIL_DATA;
typedef SP_INTERFACE_DEVICE_DETAIL_DATA_A SP_INTERFACE_DEVICE_DETAIL_DATA,
    *PSP_INTERFACE_DEVICE_DETAIL_DATA; /* deprecated */
typedef SP_DEVINFO_LIST_DETAIL_DATA_A SP_DEVINFO_LIST_DETAIL_DATA,
    *PSP_DEVINFO_LIST_DETAIL_DATA;
typedef SP_DEVINSTALL_PARAMS_A SP_DEVINSTALL_PARAMS, *PSP_DEVINSTALL_PARAMS;
typedef SP_SELECTDEVICE_PARAMS_A SP_SELECTDEVICE_PARAMS,
    *PSP_SELECTDEVICE_PARAMS;
typedef SP_TROUBLESHOOTER_PARAMS_A SP_TROUBLESHOOTER_PARAMS,
    *PSP_TROUBLESHOOTER_PARAMS;
typedef SP_POWERMESSAGEWAKE_PARAMS_A SP_POWERMESSAGEWAKE_PARAMS,
    *PSP_POWERMESSAGEWAKE_PARAMS;
typedef SP_DRVINFO_DETAIL_DATA_A SP_DRVINFO_DETAIL_DATA,
    *PSP_DRVINFO_DETAIL_DATA;
typedef SP_BACKUP_QUEUE_PARAMS_A SP_BACKUP_QUEUE_PARAMS,
    *PSP_BACKUP_QUEUE_PARAMS;
#endif /* UNICODE */

WINSETUPAPI BOOL WINAPI SetupAddInstallSectionToDiskSpaceListA(HDSKSPC,HINF,HINF,PCSTR,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddInstallSectionToDiskSpaceListW(HDSKSPC,HINF,HINF,PCWSTR,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddSectionToDiskSpaceListA(HDSKSPC,HINF,HINF,PCSTR,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddSectionToDiskSpaceListW(HDSKSPC,HINF,HINF,PCWSTR,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddToDiskSpaceListA(HDSKSPC,PCSTR,LONGLONG,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddToDiskSpaceListW(HDSKSPC,PCWSTR,LONGLONG,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAddToSourceListA(DWORD,PCSTR);
WINSETUPAPI BOOL WINAPI SetupAddToSourceListW(DWORD,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupAdjustDiskSpaceListA(HDSKSPC,LPCSTR,LONGLONG,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupAdjustDiskSpaceListW(HDSKSPC,LPCWSTR,LONGLONG,PVOID,UINT);
WINSETUPAPI UINT WINAPI SetupBackupErrorA(HWND,PCSTR,PCSTR,PCSTR,UINT,DWORD);
WINSETUPAPI UINT WINAPI SetupBackupErrorW(HWND,PCWSTR,PCWSTR,PCWSTR,UINT,DWORD);
WINSETUPAPI BOOL WINAPI SetupCancelTemporary(VOID);
WINSETUPAPI BOOL WINAPI SetupCloseFileQueue(HSPFILEQ);
WINSETUPAPI VOID WINAPI SetupCloseInfFile(HINF);
WINSETUPAPI VOID WINAPI SetupCloseLog(VOID);
WINSETUPAPI BOOL WINAPI SetupCommitFileQueueA(HWND,HSPFILEQ,PSP_FILE_CALLBACK_A,PVOID);
WINSETUPAPI BOOL WINAPI SetupCommitFileQueueW(HWND,HSPFILEQ,PSP_FILE_CALLBACK_W,PVOID);
WINSETUPAPI UINT WINAPI SetupCopyErrorA(HWND,PCSTR,PCSTR,PCSTR,PCSTR,PCSTR,UINT,DWORD,PSTR,DWORD,PDWORD);
WINSETUPAPI UINT WINAPI SetupCopyErrorW(HWND,PCWSTR,PCWSTR,PCWSTR,PCWSTR,PCWSTR,UINT,DWORD,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupCopyOEMInfA(PCSTR,PCSTR,DWORD,DWORD,PSTR,DWORD,PDWORD,PSTR*);
WINSETUPAPI BOOL WINAPI SetupCopyOEMInfW(PCWSTR,PCWSTR,DWORD,DWORD,PWSTR,DWORD,PDWORD,PWSTR*);
WINSETUPAPI HDSKSPC WINAPI SetupCreateDiskSpaceListA(PVOID,DWORD,UINT);
WINSETUPAPI HDSKSPC WINAPI SetupCreateDiskSpaceListW(PVOID,DWORD,UINT);
WINSETUPAPI DWORD WINAPI SetupDecompressOrCopyFileA(PCSTR,PCSTR,PUINT);
WINSETUPAPI DWORD WINAPI SetupDecompressOrCopyFileW(PCWSTR,PCWSTR,PUINT);
WINSETUPAPI UINT WINAPI SetupDefaultQueueCallbackA(PVOID,UINT,UINT_PTR,UINT_PTR);
WINSETUPAPI UINT WINAPI SetupDefaultQueueCallbackW(PVOID,UINT,UINT_PTR,UINT_PTR);
WINSETUPAPI UINT WINAPI SetupDeleteErrorA(HWND,PCSTR,PCSTR,UINT,DWORD);
WINSETUPAPI UINT WINAPI SetupDeleteErrorW(HWND,PCWSTR,PCWSTR,UINT,DWORD);
WINSETUPAPI BOOL WINAPI SetupDestroyDiskSpaceList(HDSKSPC);
WINSETUPAPI BOOL WINAPI SetupDiAskForOEMDisk(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiBuildClassInfoList(DWORD,LPGUID,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiBuildClassInfoListExA(DWORD,LPGUID,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiBuildClassInfoListExW(DWORD,LPGUID,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiBuildDriverInfoList(HDEVINFO,PSP_DEVINFO_DATA,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiCallClassInstaller(DI_FUNCTION,HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiCancelDriverInfoSearch(HDEVINFO);
WINSETUPAPI BOOL WINAPI SetupDiChangeState(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiClassGuidsFromNameA(PCSTR,LPGUID,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiClassGuidsFromNameW(PCWSTR,LPGUID,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiClassGuidsFromNameExA(PCSTR,LPGUID,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiClassGuidsFromNameExW(PCWSTR,LPGUID,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiClassNameFromGuidA(CONST GUID*,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiClassNameFromGuidW(CONST GUID*,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiClassNameFromGuidExA(CONST GUID*,PSTR,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiClassNameFromGuidExW(CONST GUID*,PWSTR,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiCreateDeviceInfoA(HDEVINFO,PCSTR,CONST GUID*,PCSTR,HWND,DWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiCreateDeviceInfoW(HDEVINFO,PCWSTR,CONST GUID*,PCWSTR,HWND,DWORD,PSP_DEVINFO_DATA);
WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoList(CONST GUID*,HWND);
WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoListExA(CONST GUID*,HWND,PCSTR,PVOID);
WINSETUPAPI HDEVINFO WINAPI SetupDiCreateDeviceInfoListExW(CONST GUID*,HWND,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiCreateDeviceInterfaceA(HDEVINFO,PSP_DEVINFO_DATA,CONST GUID*,PCSTR,DWORD,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiCreateDeviceInterfaceW(HDEVINFO,PSP_DEVINFO_DATA,CONST GUID*,PCWSTR,DWORD,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI HKEY WINAPI SetupDiCreateDeviceInterfaceRegKeyA(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,DWORD,REGSAM,HINF,PCSTR);
WINSETUPAPI HKEY WINAPI SetupDiCreateDeviceInterfaceRegKeyW(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,DWORD,REGSAM,HINF,PCWSTR);
WINSETUPAPI HKEY WINAPI SetupDiCreateDevRegKeyA(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,DWORD,HINF,PCSTR);
WINSETUPAPI HKEY WINAPI SetupDiCreateDevRegKeyW(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,DWORD,HINF,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupDiDeleteDeviceInfo(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiDeleteDeviceInterfaceData(HDEVINFO,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiDeleteDeviceInterfaceRegKey(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiDeleteDevRegKey(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiDestroyClassImageList(PSP_CLASSIMAGELIST_DATA);
WINSETUPAPI BOOL WINAPI SetupDiDestroyDeviceInfoList(HDEVINFO);
WINSETUPAPI BOOL WINAPI SetupDiDestroyDriverInfoList(HDEVINFO,PSP_DEVINFO_DATA,DWORD);
WINSETUPAPI INT WINAPI SetupDiDrawMiniIcon(HDC,RECT,INT,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiEnumDeviceInfo(HDEVINFO,DWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiEnumDeviceInterfaces(HDEVINFO,PSP_DEVINFO_DATA,CONST GUID*,DWORD,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiEnumDriverInfoA(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,PSP_DRVINFO_DATA_A);
WINSETUPAPI BOOL WINAPI SetupDiEnumDriverInfoW(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,PSP_DRVINFO_DATA_W);
WINSETUPAPI BOOL WINAPI SetupDiGetActualSectionToInstallA(HINF,PCSTR,PSTR,DWORD,PDWORD,PSTR*);
WINSETUPAPI BOOL WINAPI SetupDiGetActualSectionToInstallW(HINF,PCWSTR,PWSTR,DWORD,PDWORD,PWSTR*);
WINSETUPAPI BOOL WINAPI SetupDiGetClassBitmapIndex(CONST GUID*,PINT);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDescriptionA(CONST GUID*,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDescriptionW(CONST GUID*,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDescriptionExA(CONST GUID*,PSTR,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDescriptionExW(CONST GUID*,PWSTR,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDevPropertySheetsA(HDEVINFO,PSP_DEVINFO_DATA,LPPROPSHEETHEADERA,DWORD,PDWORD,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetClassDevPropertySheetsW(HDEVINFO,PSP_DEVINFO_DATA,LPPROPSHEETHEADERW,DWORD,PDWORD,DWORD);
WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsA(CONST GUID*,PCSTR,HWND,DWORD);
WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsW(CONST GUID*,PCWSTR,HWND,DWORD);
WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsExA(CONST GUID*,PCSTR,HWND,DWORD,HDEVINFO,PCSTR,PVOID);
WINSETUPAPI HDEVINFO WINAPI SetupDiGetClassDevsExW(CONST GUID*,PCWSTR,HWND,DWORD,HDEVINFO,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassImageIndex(PSP_CLASSIMAGELIST_DATA,CONST GUID*,PINT);
WINSETUPAPI BOOL WINAPI SetupDiGetClassImageList(PSP_CLASSIMAGELIST_DATA);
WINSETUPAPI BOOL WINAPI SetupDiGetClassImageListExA(PSP_CLASSIMAGELIST_DATA,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassImageListExW(PSP_CLASSIMAGELIST_DATA,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_CLASSINSTALL_HEADER,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetClassInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_CLASSINSTALL_HEADER,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetClassRegistryPropertyA(LPGUID,DWORD,PDWORD,PBYTE,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetClassRegistryPropertyW(LPGUID,DWORD,PDWORD,PBYTE,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInfoListClass(HDEVINFO,LPGUID);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInfoListDetailA(HDEVINFO,PSP_DEVINFO_LIST_DETAIL_DATA_A);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInfoListDetailW(HDEVINFO,PSP_DEVINFO_LIST_DETAIL_DATA_W);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DEVINSTALL_PARAMS_A);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DEVINSTALL_PARAMS_W);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInstanceIdA(HDEVINFO,PSP_DEVINFO_DATA,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInstanceIdW(HDEVINFO,PSP_DEVINFO_DATA,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInterfaceAlias(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,CONST GUID*,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInterfaceDetailA(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,PSP_DEVICE_INTERFACE_DETAIL_DATA_A,DWORD,PDWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceInterfaceDetailW(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,PSP_DEVICE_INTERFACE_DETAIL_DATA_W,DWORD,PDWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceRegistryPropertyA(HDEVINFO,PSP_DEVINFO_DATA,DWORD,PDWORD,PBYTE,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDeviceRegistryPropertyW(HDEVINFO,PSP_DEVINFO_DATA,DWORD,PDWORD,PBYTE,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDriverInfoDetailA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_A,PSP_DRVINFO_DETAIL_DATA_A,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDriverInfoDetailW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_W,PSP_DRVINFO_DETAIL_DATA_W,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetDriverInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_A,PSP_DRVINSTALL_PARAMS);
WINSETUPAPI BOOL WINAPI SetupDiGetDriverInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_W,PSP_DRVINSTALL_PARAMS);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileFriendlyNameA(DWORD,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileFriendlyNameExA(DWORD,PSTR,DWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileFriendlyNameExW(DWORD,PWSTR,DWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileFriendlyNameW(DWORD,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileList(PDWORD,DWORD,PDWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileListExA(PDWORD,DWORD,PDWORD,PDWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetHwProfileListExW(PDWORD,DWORD,PDWORD,PDWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiGetINFClassA(PCSTR,LPGUID,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetINFClassW(PCWSTR,LPGUID,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupDiGetSelectedDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiGetSelectedDriverA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_A);
WINSETUPAPI BOOL WINAPI SetupDiGetSelectedDriverW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_W);
WINSETUPAPI HPROPSHEETPAGE WINAPI SetupDiGetWizardage(HDEVINFO,PSP_DEVINFO_DATA,PSP_INSTALLWIZARD_DATA,DWORD,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiInstallClassA(HWND,PCSTR,DWORD,HSPFILEQ);
WINSETUPAPI BOOL WINAPI SetupDiInstallClassW(HWND,PCWSTR,DWORD,HSPFILEQ);
WINSETUPAPI BOOL WINAPI SetupDiInstallClassExA(HWND,PCSTR,DWORD,HSPFILEQ,CONST GUID*,PVOID,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiInstallClassExW(HWND,PCWSTR,DWORD,HSPFILEQ,CONST GUID*,PVOID,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiInstallDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiInstallDeviceInterfaces(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiInstallDriverFiles(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiLoadClassIcon(CONST GUID*,HICON*,PINT);
WINSETUPAPI BOOL WINAPI SetupDiMoveDuplicateDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKey(CONST GUID*,REGSAM);
WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKeyExA(CONST GUID*,REGSAM,DWORD,PCSTR,PVOID);
WINSETUPAPI HKEY WINAPI SetupDiOpenClassRegKeyExW(CONST GUID*,REGSAM,DWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiOpenDeviceInfoA(HDEVINFO,PCSTR,HWND,DWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiOpenDeviceInfoW(HDEVINFO,PCWSTR,HWND,DWORD,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiOpenDeviceInterfaceA(HDEVINFO,PCSTR,DWORD,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiOpenDeviceInterfaceW(HDEVINFO,PCWSTR,DWORD,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI HKEY WINAPI SetupDiOpenDeviceInterfaceRegKey(HDEVINFO,PSP_DEVICE_INTERFACE_DATA,DWORD,REGSAM);
WINSETUPAPI HKEY WINAPI SetupDiOpenDevRegKey(HDEVINFO,PSP_DEVINFO_DATA,DWORD,DWORD,DWORD,REGSAM);
WINSETUPAPI BOOL WINAPI SetupDiRegisterCoDeviceInstallers(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiRegisterDeviceInfo(HDEVINFO,PSP_DEVINFO_DATA,DWORD,PSP_DETSIG_CMPPROC,PVOID,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiRemoveDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiRemoveDeviceInterface(HDEVINFO,PSP_DEVICE_INTERFACE_DATA);
WINSETUPAPI BOOL WINAPI SetupDiSelectBestCompatDrv(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiSelectDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiSelectOEMDrv(HWND,HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiSetClassInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_CLASSINSTALL_HEADER,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiSetClassInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_CLASSINSTALL_HEADER,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiSetClassRegistryPropertyA(LPGUID,DWORD,CONST BYTE*,DWORD,PCSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiSetClassRegistryPropertyW(LPGUID,DWORD,CONST BYTE*,DWORD,PCWSTR,PVOID);
WINSETUPAPI BOOL WINAPI SetupDiSetDeviceInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DEVINSTALL_PARAMS_A);
WINSETUPAPI BOOL WINAPI SetupDiSetDeviceInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DEVINSTALL_PARAMS_W);
WINSETUPAPI BOOL WINAPI SetupDiSetDeviceRegistryPropertyA(HDEVINFO,PSP_DEVINFO_DATA,DWORD,CONST BYTE*,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiSetDeviceRegistryPropertyW(HDEVINFO,PSP_DEVINFO_DATA,DWORD,CONST BYTE*,DWORD);
WINSETUPAPI BOOL WINAPI SetupDiSetDriverInstallParamsA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_A,PSP_DRVINSTALL_PARAMS);
WINSETUPAPI BOOL WINAPI SetupDiSetDriverInstallParamsW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_W,PSP_DRVINSTALL_PARAMS);
WINSETUPAPI BOOL WINAPI SetupDiSetSelectedDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupDiSetSelectedDriverA(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_A);
WINSETUPAPI BOOL WINAPI SetupDiSetSelectedDriverW(HDEVINFO,PSP_DEVINFO_DATA,PSP_DRVINFO_DATA_W);
WINSETUPAPI BOOL WINAPI SetupDiUnremoveDevice(HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI HDSKSPC WINAPI SetupDuplicateDiskSpaceListA(HDSKSPC,PVOID,DWORD,UINT);
WINSETUPAPI HDSKSPC WINAPI SetupDuplicateDiskSpaceListW(HDSKSPC,PVOID,DWORD,UINT);
WINSETUPAPI BOOL WINAPI SetupFindFirstLineA(HINF,PCSTR,PCSTR,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupFindFirstLineW(HINF,PCWSTR,PCWSTR,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupFindNextLine(PINFCONTEXT,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupFindNextMatchLineA(PINFCONTEXT,PCSTR,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupFindNextMatchLineW(PINFCONTEXT,PCWSTR,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupFreeA(PCSTR**,UINT);
WINSETUPAPI BOOL WINAPI SetupFreeW(PCWSTR**,UINT);
WINSETUPAPI BOOL WINAPI SetupGetBackupInformationA(HSPFILEQ,PSP_BACKUP_QUEUE_PARAMS_A);
WINSETUPAPI BOOL WINAPI SetupGetBackupInformationW(HSPFILEQ,PSP_BACKUP_QUEUE_PARAMS_W);
WINSETUPAPI BOOL WINAPI SetupGetBinaryField(PINFCONTEXT,DWORD,PBYTE,DWORD,LPDWORD);
WINSETUPAPI DWORD WINAPI SetupGetFieldCount(PINFCONTEXT);
WINSETUPAPI DWORD WINAPI SetupGetFileCompressionInfoA(PCSTR,PSTR*,PDWORD,PDWORD,PUINT);
WINSETUPAPI DWORD WINAPI SetupGetFileCompressionInfoW(PCWSTR,PWSTR*,PDWORD,PDWORD,PUINT);
WINSETUPAPI BOOL WINAPI SetupGetInfFileListA(PCSTR,DWORD,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetInfFileListW(PCWSTR,DWORD,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetInfInformationA(LPCVOID,DWORD,PSP_INF_INFORMATION,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetInfInformationW(LPCVOID,DWORD,PSP_INF_INFORMATION,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetIntField(PINFCONTEXT,DWORD,PINT);
WINSETUPAPI BOOL WINAPI SetupGetLineByIndexA(HINF,PCSTR,DWORD,PINFCONTEXT);
WINSETUPAPI BOOL WINAPI SetupGetLineByIndexW(HINF,PCWSTR,DWORD,PINFCONTEXT);
WINSETUPAPI LONG WINAPI SetupGetLineCountA(HINF,PCSTR);
WINSETUPAPI LONG WINAPI SetupGetLineCountW(HINF,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupGetLineTextA(PINFCONTEXT,HINF,PCSTR,PCSTR,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetLineTextW(PINFCONTEXT,HINF,PCWSTR,PCWSTR,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetMultiSzFieldA(PINFCONTEXT,DWORD,PSTR,DWORD,LPDWORD);
WINSETUPAPI BOOL WINAPI SetupGetMultiSzFieldW(PINFCONTEXT,DWORD,PWSTR,DWORD,LPDWORD);
WINSETUPAPI BOOL WINAPI SetupGetSourceFileLocationA(HINF,PINFCONTEXT,PCSTR,PUINT,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetSourceFileLocationW(HINF,PINFCONTEXT,PCWSTR,PUINT,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetSourceFileSizeA(HINF,PINFCONTEXT,PCSTR,PCSTR,PDWORD,UINT);
WINSETUPAPI BOOL WINAPI SetupGetSourceFileSizeW(HINF,PINFCONTEXT,PCWSTR,PCWSTR,PDWORD,UINT);
WINSETUPAPI BOOL WINAPI SetupGetSourceInfoA(HINF,UINT,UINT,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetSourceInfoW(HINF,UINT,UINT,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetStringFieldA(PINFCONTEXT,DWORD,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetStringFieldW(PINFCONTEXT,DWORD,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetTargetPathA(HINF,PINFCONTEXT,PCSTR,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupGetTargetPathW(HINF,PINFCONTEXT,PCWSTR,PWSTR,DWORD,PDWORD);
WINSETUPAPI PVOID WINAPI SetupInitDefaultQueueCallback(HWND);
WINSETUPAPI PVOID WINAPI SetupInitDefaultQueueCallbackEx(HWND,HWND,UINT,DWORD,PVOID);
WINSETUPAPI HSPFILELOG WINAPI SetupInitializeFileLogA(PCSTR,DWORD);
WINSETUPAPI HSPFILELOG WINAPI SetupInitializeFileLogW(PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupInstallFileA(HINF,PINFCONTEXT,PCSTR,PCSTR,PCSTR,DWORD,PSP_FILE_CALLBACK_A,PVOID);
WINSETUPAPI BOOL WINAPI SetupInstallFileW(HINF,PINFCONTEXT,PCWSTR,PCWSTR,PCWSTR,DWORD,PSP_FILE_CALLBACK_W,PVOID);
WINSETUPAPI BOOL WINAPI SetupInstallFileExA(HINF,PINFCONTEXT,PCSTR,PCSTR,PCSTR,DWORD,PSP_FILE_CALLBACK_A,PVOID,PBOOL);
WINSETUPAPI BOOL WINAPI SetupInstallFileExW(HINF,PINFCONTEXT,PCWSTR,PCWSTR,PCWSTR,DWORD,PSP_FILE_CALLBACK_W,PVOID,PBOOL);
WINSETUPAPI BOOL WINAPI SetupInstallFilesFromInfSectionA(HINF,HINF,HSPFILEQ,PCSTR,PCSTR,UINT);
WINSETUPAPI BOOL WINAPI SetupInstallFilesFromInfSectionW(HINF,HINF,HSPFILEQ,PCWSTR,PCWSTR,UINT);
WINSETUPAPI BOOL WINAPI SetupInstallFromInfSectionA(HWND,HINF,PCSTR,UINT,HKEY,PCSTR,UINT,PSP_FILE_CALLBACK_A,PVOID,HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupInstallFromInfSectionW(HWND,HINF,PCWSTR,UINT,HKEY,PCWSTR,UINT,PSP_FILE_CALLBACK_W,PVOID,HDEVINFO,PSP_DEVINFO_DATA);
WINSETUPAPI BOOL WINAPI SetupInstallServicesFromInfSectionA(HINF,PCSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupInstallServicesFromInfSectionW(HINF,PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupInstallServicesFromInfSectionExA(HINF,PCSTR,DWORD,HDEVINFO,PSP_DEVINFO_DATA,PVOID,PVOID);
WINSETUPAPI BOOL WINAPI SetupInstallServicesFromInfSectionExW(HINF,PCWSTR,DWORD,HDEVINFO,PSP_DEVINFO_DATA,PVOID,PVOID);
WINSETUPAPI BOOL WINAPI SetupIterateCabinetA(PCSTR,DWORD,PSP_FILE_CALLBACK_A,PVOID);
WINSETUPAPI BOOL WINAPI SetupIterateCabinetW(PCWSTR,DWORD,PSP_FILE_CALLBACK_W,PVOID);
WINSETUPAPI BOOL WINAPI SetupLogErrorA(LPCSTR,LogSeverity);
WINSETUPAPI BOOL WINAPI SetupLogErrorW(LPCWSTR,LogSeverity);
WINSETUPAPI BOOL WINAPI SetupLogFileA(HSPFILELOG,PCSTR,PCSTR,PCSTR,DWORD,PCSTR,PCSTR,PCSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupLogFileW(HSPFILELOG,PCWSTR,PCWSTR,PCWSTR,DWORD,PCWSTR,PCWSTR,PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupOpenAppendInfFileA(PCSTR,HINF,PUINT);
WINSETUPAPI BOOL WINAPI SetupOpenAppendInfFileW(PCWSTR,HINF,PUINT);
WINSETUPAPI HSPFILEQ WINAPI SetupOpenFileQueue(VOID);
WINSETUPAPI HINF WINAPI SetupOpenInfFileA(PCSTR,PCSTR,DWORD,PUINT);
WINSETUPAPI HINF WINAPI SetupOpenInfFileW(PCWSTR,PCWSTR,DWORD,PUINT);
WINSETUPAPI BOOL WINAPI SetupOpenLog(BOOL);
WINSETUPAPI HINF WINAPI SetupOpenMasterInf(VOID);
WINSETUPAPI UINT WINAPI SetupPromptForDiskA(HWND,PCSTR,PCSTR,PCSTR,PCSTR,PCSTR,DWORD,PSTR,DWORD,PDWORD);
WINSETUPAPI UINT WINAPI SetupPromptForDiskW(HWND,PCWSTR,PCWSTR,PCWSTR,PCWSTR,PCWSTR,DWORD,PWSTR,DWORD,PDWORD);
WINSETUPAPI INT WINAPI SetupPromptReboot(HSPFILEQ,HWND,BOOL);
WINSETUPAPI BOOL WINAPI SetupQueryA(DWORD,PCSTR**,PUINT);
WINSETUPAPI BOOL WINAPI SetupQueryW(DWORD,PCWSTR**,PUINT);
WINSETUPAPI BOOL WINAPI SetupQueryDrivesInDiskSpaceListA(HDSKSPC,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryDrivesInDiskSpaceListW(HDSKSPC,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryFileLogA(HSPFILELOG,PCSTR,PCSTR,SetupFileLogInfo,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryFileLogW(HSPFILELOG,PCWSTR,PCWSTR,SetupFileLogInfo,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryInfFileInformationA(PSP_INF_INFORMATION,UINT,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryInfFileInformationW(PSP_INF_INFORMATION,UINT,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryInfOriginalFileInformationA(PSP_INF_INFORMATION,UINT,PSP_ALTPLATFORM_INFO,PSP_ORIGINAL_FILE_INFO_A);
WINSETUPAPI BOOL WINAPI SetupQueryInfOriginalFileInformationW(PSP_INF_INFORMATION,UINT,PSP_ALTPLATFORM_INFO,PSP_ORIGINAL_FILE_INFO_W);
WINSETUPAPI BOOL WINAPI SetupQueryInfVersionInformationA(PSP_INF_INFORMATION,UINT,PSTR,PSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQueryInfVersionInformationW(PSP_INF_INFORMATION,UINT,PCWSTR,PWSTR,DWORD,PDWORD);
WINSETUPAPI BOOL WINAPI SetupQuerySpaceRequiredOnDriveA(HDSKSPC,PCSTR,LONGLONG*,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupQuerySpaceRequiredOnDriveW(HDSKSPC,PCWSTR,LONGLONG*,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupQueueCopyA(HSPFILEQ,PCSTR,PCSTR,PCSTR,PCSTR,PCSTR,PCSTR,PCSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueCopyW(HSPFILEQ,PCWSTR,PCWSTR,PCWSTR,PCWSTR,PCWSTR,PCWSTR,PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueCopyIndirectA(PSP_FILE_COPY_PARAMS_A);
WINSETUPAPI BOOL WINAPI SetupQueueCopyIndirectW(PSP_FILE_COPY_PARAMS_W);
WINSETUPAPI BOOL WINAPI SetupQueueCopySectionA(HSPFILEQ,PCSTR,HINF,HINF,PCSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueCopySectionW(HSPFILEQ,PCWSTR,HINF,HINF,PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueDefaultCopyA(HSPFILEQ,HINF,PCSTR,PCSTR,PCSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueDefaultCopyW(HSPFILEQ,HINF,PCWSTR,PCWSTR,PCWSTR,DWORD);
WINSETUPAPI BOOL WINAPI SetupQueueDeleteA(HSPFILEQ,PCSTR,PCSTR);
WINSETUPAPI BOOL WINAPI SetupQueueDeleteW(HSPFILEQ,PCWSTR,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupQueueDeleteSectionA(HSPFILEQ,HINF,HINF,PCSTR);
WINSETUPAPI BOOL WINAPI SetupQueueDeleteSectionW(HSPFILEQ,HINF,HINF,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupQueueRenameA(HSPFILEQ,PCSTR,PCSTR,PCSTR,PCSTR);
WINSETUPAPI BOOL WINAPI SetupQueueRenameW(HSPFILEQ,PCWSTR,PCWSTR,PCWSTR,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupQueueRenameSectionA(HSPFILEQ,HINF,HINF,PCSTR);
WINSETUPAPI BOOL WINAPI SetupQueueRenameSectionW(HSPFILEQ,HINF,HINF,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupRemoveFileLogEntryA(HSPFILELOG,PCSTR,PCSTR);
WINSETUPAPI BOOL WINAPI SetupRemoveFileLogEntryW(HSPFILELOG,PCWSTR,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupRemoveFromDiskSpaceListA(HDSKSPC,PCSTR,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupRemoveFromDiskSpaceListW(HDSKSPC,PCWSTR,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupRemoveFromSourceListA(DWORD,PCSTR);
WINSETUPAPI BOOL WINAPI SetupRemoveFromSourceListW(DWORD,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupRemoveInstallSectionFromDiskSpaceListA(HDSKSPC,HINF,HINF,PCSTR,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupRemoveInstallSectionFromDiskSpaceListW(HDSKSPC,HINF,HINF,PCWSTR,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupRemoveSectionFromDiskSpaceListA(HDSKSPC,HINF,HINF,PCSTR,UINT,PVOID,UINT);
WINSETUPAPI BOOL WINAPI SetupRemoveSectionFromDiskSpaceListW(HDSKSPC,HINF,HINF,PCWSTR,UINT,PVOID,UINT);
WINSETUPAPI UINT WINAPI SetupRenameErrorA(HWND,PCSTR,PCSTR,PCSTR,UINT,DWORD);
WINSETUPAPI UINT WINAPI SetupRenameErrorW(HWND,PCWSTR,PCWSTR,PCWSTR,UINT,DWORD);
WINSETUPAPI BOOL WINAPI SetupScanFileQueueA(HSPFILEQ,DWORD,HWND,PSP_FILE_CALLBACK_A,PVOID,PDWORD);
WINSETUPAPI BOOL WINAPI SetupScanFileQueueW(HSPFILEQ,DWORD,HWND,PSP_FILE_CALLBACK_W,PVOID,PDWORD);
WINSETUPAPI BOOL WINAPI SetupSetDirectoryIdA(HINF,DWORD,PCSTR);
WINSETUPAPI BOOL WINAPI SetupSetDirectoryIdW(HINF,DWORD,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupSetDirectoryIdExA(HINF,DWORD,PCSTR,DWORD,DWORD,PVOID);
WINSETUPAPI BOOL WINAPI SetupSetDirectoryIdExW(HINF,DWORD,PCWSTR,DWORD,DWORD,PVOID);
WINSETUPAPI BOOL WINAPI SetupSetFileQueueAlternatePlatformA(HSPFILEQ,PSP_ALTPLATFORM_INFO,PCSTR);
WINSETUPAPI BOOL WINAPI SetupSetFileQueueAlternatePlatformW(HSPFILEQ,PSP_ALTPLATFORM_INFO,PCWSTR);
WINSETUPAPI BOOL WINAPI SetupSetPlatformPathOverrideA(PCSTR);
WINSETUPAPI BOOL WINAPI SetupSetPlatformPathOverrideW(PCWSTR);
WINSETUPAPI BOOL WINAPI SetupSetSourceListA(DWORD,PCSTR*,UINT);
WINSETUPAPI BOOL WINAPI SetupSetSourceListW(DWORD,PCWSTR*,UINT);
WINSETUPAPI VOID WINAPI SetupTermDefaultQueueCallback(PVOID);
WINSETUPAPI BOOL WINAPI SetupTerminateFileLog(HSPFILELOG);

/* for backward compatability */
#define SetupDiCreateInterfaceDeviceW	SetupDiCreateDeviceInterfaceW
#define SetupDiCreateInterfaceDeviceRegKeyW	SetupDiCreateDeviceInterfaceRegKeyW
#define SetupDiOpenInterfaceDeviceW	SetupDiOpenDeviceInterfaceW
#define SetupDiGetInterfaceDeviceDetailW	SetupDiGetDeviceInterfaceDetailW
#define SetupDiCreateInterfaceDeviceA	SetupDiCreateDeviceInterfaceA
#define SetupDiCreateInterfaceDeviceRegKeyA	SetupDiCreateDeviceInterfaceRegKeyA
#define SetupDiOpenInterfaceDeviceA	SetupDiOpenDeviceInterfaceA
#define SetupDiGetInterfaceDeviceDetailA	SetupDiGetDeviceInterfaceDetailA

#ifdef UNICODE		
#define PSP_FILE_CALLBACK PSP_FILE_CALLBACK_W
#define SetupAddInstallSectionToDiskSpaceList	SetupAddInstallSectionToDiskSpaceListW
#define SetupAddSectionToDiskSpaceList	SetupAddSectionToDiskSpaceListW
#define SetupAddToDiskSpaceList	SetupAddToDiskSpaceListW
#define SetupAddToSourceList	SetupAddToSourceListW
#define SetupAdjustDiskSpaceList	SetupAdjustDiskSpaceListW
#define SetupBackupError	SetupBackupErrorW
#define SetupCommitFileQueue	SetupCommitFileQueueW
#define SetupCopyError	SetupCopyErrorW
#define SetupCopyOEMInf	SetupCopyOEMInfW
#define SetupCreateDiskSpaceList	SetupCreateDiskSpaceListW
#define SetupDecompressOrCopyFile	SetupDecompressOrCopyFileW
#define SetupDefaultQueueCallback	SetupDefaultQueueCallbackW
#define SetupDeleteError	SetupDeleteErrorW
#define SetupDiBuildClassInfoListEx	SetupDiBuildClassInfoListExW
#define SetupDiClassGuidsFromNameEx	SetupDiClassGuidsFromNameExW
#define SetupDiClassGuidsFromName	SetupDiClassGuidsFromNameW
#define SetupDiClassNameFromGuidEx	SetupDiClassNameFromGuidExW
#define SetupDiClassNameFromGuid	SetupDiClassNameFromGuidW
#define SetupDiCreateDeviceInfoListEx	SetupDiCreateDeviceInfoListExW
#define SetupDiCreateDeviceInfo	SetupDiCreateDeviceInfoW
#define SetupDiCreateDeviceInterfaceRegKey	SetupDiCreateDeviceInterfaceRegKeyW
#define SetupDiCreateInterfaceDeviceRegKey	SetupDiCreateDeviceInterfaceRegKeyW
#define SetupDiCreateDeviceInterface	SetupDiCreateDeviceInterfaceW
#define SetupDiCreateInterfaceDevice	SetupDiCreateDeviceInterfaceW
#define SetupDiCreateDevRegKey	SetupDiCreateDevRegKeyW
#define SetupDiEnumDriverInfo	SetupDiEnumDriverInfoW
#define SetupDiGetActualSectionToInstall	SetupDiGetActualSectionToInstallW
#define SetupDiGetClassDescriptionEx	SetupDiGetClassDescriptionExW
#define SetupDiGetClassDescription	SetupDiGetClassDescriptionW
#define SetupDiGetClassDevPropertySheets	SetupDiGetClassDevPropertySheetsW
#define SetupDiGetClassDevsEx	SetupDiGetClassDevsExW
#define SetupDiGetClassDevs	SetupDiGetClassDevsW
#define SetupDiGetClassImageListEx	SetupDiGetClassImageListExW
#define SetupDiGetClassInstallParams	SetupDiGetClassInstallParamsW
#define SetupDiGetClassRegistryProperty	SetupDiGetClassRegistryPropertyW
#define SetupDiGetDeviceInfoListDetail	SetupDiGetDeviceInfoListDetailW
#define SetupDiGetDeviceInstallParams	SetupDiGetDeviceInstallParamsW
#define SetupDiGetDeviceInstanceId	SetupDiGetDeviceInstanceIdW
#define SetupDiGetDeviceInterfaceDetail	SetupDiGetDeviceInterfaceDetailW
#define SetupDiGetInterfaceDeviceDetail	SetupDiGetDeviceInterfaceDetailW
#define SetupDiGetDeviceRegistryProperty	SetupDiGetDeviceRegistryPropertyW
#define SetupDiGetDriverInfoDetail	SetupDiGetDriverInfoDetailW
#define SetupDiGetDriverInstallParams	SetupDiGetDriverInstallParamsW
#define SetupDiGetHwProfileFriendlyNameEx	SetupDiGetHwProfileFriendlyNameExW
#define SetupDiGetHwProfileFriendlyName	SetupDiGetHwProfileFriendlyNameW
#define SetupDiGetHwProfileListEx	SetupDiGetHwProfileListExW
#define SetupDiGetINFClass	SetupDiGetINFClassW
#define SetupDiGetSelectedDriver	SetupDiGetSelectedDriverW
#define SetupDiInstallClassEx	SetupDiInstallClassExW
#define SetupDiInstallClass	SetupDiInstallClassW
#define SetupDiOpenClassRegKeyEx	SetupDiOpenClassRegKeyExW
#define SetupDiOpenDeviceInfo	SetupDiOpenDeviceInfoW
#define SetupDiOpenDeviceInterface	SetupDiOpenDeviceInterfaceW
#define SetupDiOpenInterfaceDevice	SetupDiOpenDeviceInterfaceW
#define SetupDiSetClassInstallParams	SetupDiSetClassInstallParamsW
#define SetupDiSetClassRegistryProperty	SetupDiSetClassRegistryPropertyW
#define SetupDiSetDeviceInstallParams	SetupDiSetDeviceInstallParamsW
#define SetupDiSetDeviceRegistryProperty	SetupDiSetDeviceRegistryPropertyW
#define SetupDiSetDriverInstallParams	SetupDiSetDriverInstallParamsW
#define SetupDiSetSelectedDriver	SetupDiSetSelectedDriverW
#define SetupDuplicateDiskSpaceList	SetupDuplicateDiskSpaceListW
#define SetupFindFirstLine	SetupFindFirstLineW
#define SetupFindNextMatchLine	SetupFindNextMatchLineW
#define SetupFreeSourceList	SetupFreeSourceListW
#define SetupGetBackupInformation	SetupGetBackupInformationW
#define SetupGetFileCompressionInfo	SetupGetFileCompressionInfoW
#define SetupGetInfFileList	SetupGetInfFileListW
#define SetupGetInfInformation	SetupGetInfInformationW
#define SetupGetLineByIndex	SetupGetLineByIndexW
#define SetupGetLineCount	SetupGetLineCountW
#define SetupGetLineText	SetupGetLineTextW
#define SetupGetMultiSzField	SetupGetMultiSzFieldW
#define SetupGetSourceFileLocation	SetupGetSourceFileLocationW
#define SetupGetSourceFileSize	SetupGetSourceFileSizeW
#define SetupGetSourceInfo	SetupGetSourceInfoW
#define SetupGetStringField	SetupGetStringFieldW
#define SetupGetTargetPath	SetupGetTargetPathW
#define SetupInitializeFileLog	SetupInitializeFileLogW
#define SetupInstallFileEx	SetupInstallFileExW
#define SetupInstallFilesFromInfSection	SetupInstallFilesFromInfSectionW
#define SetupInstallFile	SetupInstallFileW
#define SetupInstallFromInfSection	SetupInstallFromInfSectionW
#define SetupInstallServicesFromInfSectionEx	SetupInstallServicesFromInfSectionExW
#define SetupInstallServicesFromInfSection	SetupInstallServicesFromInfSectionW
#define SetupIterateCabinet	SetupIterateCabinetW
#define SetupLogError	SetupLogErrorW
#define SetupLogFile	SetupLogFileW
#define SetupOpenAppendInfFile	SetupOpenAppendInfFileW
#define SetupOpenInfFile	SetupOpenInfFileW
#define SetupPromptForDisk	SetupPromptForDiskW
#define SetupQueryDrivesInDiskSpaceList	SetupQueryDrivesInDiskSpaceListW
#define SetupQueryFileLog	SetupQueryFileLogW
#define SetupQueryInfFileInformation	SetupQueryInfFileInformationW
#define SetupQueryInfOriginalFileInformation	SetupQueryInfOriginalFileInformationW
#define SetupQueryInfVersionInformation	SetupQueryInfVersionInformationW
#define SetupQuerySourceList	SetupQuerySourceListW
#define SetupQuerySpaceRequiredOnDrive	SetupQuerySpaceRequiredOnDriveW
#define SetupQueueCopyIndirect	SetupQueueCopyIndirectW
#define SetupQueueCopySection	SetupQueueCopySectionW
#define SetupQueueCopy	SetupQueueCopyW
#define SetupQueueDefaultCopy	SetupQueueDefaultCopyW
#define SetupQueueDeleteSection	SetupQueueDeleteSectionW
#define SetupQueueDelete	SetupQueueDeleteW
#define SetupQueueRenameSection	SetupQueueRenameSectionW
#define SetupQueueRename	SetupQueueRenameW
#define SetupRemoveFileLogEntry	SetupRemoveFileLogEntryW
#define SetupRemoveFromDiskSpaceList	SetupRemoveFromDiskSpaceListW
#define SetupRemoveFromSourceList	SetupRemoveFromSourceListW
#define SetupRemoveInstallSectionFromDiskSpaceList	SetupRemoveInstallSectionFromDiskSpaceListW
#define SetupRemoveSectionFromDiskSpaceList	SetupRemoveSectionFromDiskSpaceListW
#define SetupRenameError	SetupRenameErrorW
#define SetupScanFileQueue	SetupScanFileQueueW
#define SetupSetDirectoryIdEx	SetupSetDirectoryIdExW
#define SetupSetDirectoryId	SetupSetDirectoryIdW
#define SetupSetFileQueueAlternatePlatform	SetupSetFileQueueAlternatePlatformW
#define SetupSetPlatformPathOverride	SetupSetPlatformPathOverrideW
#define SetupSetSourceList	SetupSetSourceListW
#else		
#define PSP_FILE_CALLBACK PSP_FILE_CALLBACK_A
#define SetupAddInstallSectionToDiskSpaceList	SetupAddInstallSectionToDiskSpaceListA
#define SetupAddSectionToDiskSpaceList	SetupAddSectionToDiskSpaceListA
#define SetupAddToDiskSpaceList	SetupAddToDiskSpaceListA
#define SetupAddToSourceList	SetupAddToSourceListA
#define SetupAdjustDiskSpaceList	SetupAdjustDiskSpaceListA
#define SetupBackupError	SetupBackupErrorA
#define SetupCommitFileQueue	SetupCommitFileQueueA
#define SetupCopyError	SetupCopyErrorA
#define SetupCopyOEMInf	SetupCopyOEMInfA
#define SetupCreateDiskSpaceList	SetupCreateDiskSpaceListA
#define SetupDecompressOrCopyFile	SetupDecompressOrCopyFileA
#define SetupDefaultQueueCallback	SetupDefaultQueueCallbackA
#define SetupDeleteError	SetupDeleteErrorA
#define SetupDiBuildClassInfoListEx	SetupDiBuildClassInfoListExA
#define SetupDiClassGuidsFromName	SetupDiClassGuidsFromNameA
#define SetupDiClassGuidsFromNameEx	SetupDiClassGuidsFromNameExA
#define SetupDiClassNameFromGuid	SetupDiClassNameFromGuidA
#define SetupDiClassNameFromGuidEx	SetupDiClassNameFromGuidExA
#define SetupDiCreateDeviceInfo	SetupDiCreateDeviceInfoA
#define SetupDiCreateDeviceInfoListEx	SetupDiCreateDeviceInfoListExA
#define SetupDiCreateDeviceInterface	SetupDiCreateDeviceInterfaceA
#define SetupDiCreateInterfaceDevice	SetupDiCreateDeviceInterfaceA
#define SetupDiCreateDeviceInterfaceRegKey	SetupDiCreateDeviceInterfaceRegKeyA
#define SetupDiCreateInterfaceDeviceRegKey	SetupDiCreateDeviceInterfaceRegKeyA
#define SetupDiCreateDevRegKey	SetupDiCreateDevRegKeyA
#define SetupDiDeleteInterfaceDeviceData	SetupDiDeleteDeviceInterfaceData
#define SetupDiEnumDriverInfo	SetupDiEnumDriverInfoA
#define SetupDiGetActualSectionToInstall	SetupDiGetActualSectionToInstallA
#define SetupDiGetClassDescription	SetupDiGetClassDescriptionA
#define SetupDiGetClassDescriptionEx	SetupDiGetClassDescriptionExA
#define SetupDiGetClassDevPropertySheets	SetupDiGetClassDevPropertySheetsA
#define SetupDiGetClassDevs	SetupDiGetClassDevsA
#define SetupDiGetClassDevsEx	SetupDiGetClassDevsExA
#define SetupDiGetClassImageListEx	SetupDiGetClassImageListExA
#define SetupDiGetClassInstallParams	SetupDiGetClassInstallParamsA
#define SetupDiGetClassRegistryProperty	SetupDiGetClassRegistryPropertyA
#define SetupDiGetDeviceInfoListDetail	SetupDiGetDeviceInfoListDetailA
#define SetupDiGetDeviceInstallParams	SetupDiGetDeviceInstallParamsA
#define SetupDiGetDeviceInstanceId	SetupDiGetDeviceInstanceIdA
#define SetupDiGetDeviceInterfaceDetail	SetupDiGetDeviceInterfaceDetailA
#define SetupDiGetInterfaceDeviceDetail	SetupDiGetDeviceInterfaceDetailA
#define SetupDiGetDeviceRegistryProperty	SetupDiGetDeviceRegistryPropertyA
#define SetupDiGetDriverInfoDetail	SetupDiGetDriverInfoDetailA
#define SetupDiGetDriverInstallParams	SetupDiGetDriverInstallParamsA
#define SetupDiGetHwProfileFriendlyName	SetupDiGetHwProfileFriendlyNameA
#define SetupDiGetHwProfileFriendlyNameEx	SetupDiGetHwProfileFriendlyNameExA
#define SetupDiGetHwProfileListEx	SetupDiGetHwProfileListExA
#define SetupDiGetINFClass	SetupDiGetINFClassA
#define SetupDiGetSelectedDriver	SetupDiGetSelectedDriverA
#define SetupDiInstallClass	SetupDiInstallClassA
#define SetupDiInstallClassEx	SetupDiInstallClassExA
#define SetupDiOpenClassRegKeyEx	SetupDiOpenClassRegKeyExA
#define SetupDiOpenDeviceInfo	SetupDiOpenDeviceInfoA
#define SetupDiOpenDeviceInterface	SetupDiOpenDeviceInterfaceA
#define SetupDiOpenInterfaceDevice	SetupDiOpenDeviceInterfaceA
#define SetupDiSetClassInstallParams	SetupDiSetClassInstallParamsA
#define SetupDiSetClassRegistryProperty	SetupDiSetClassRegistryPropertyA
#define SetupDiSetDeviceInstallParams	SetupDiSetDeviceInstallParamsA
#define SetupDiSetDeviceRegistryProperty	SetupDiSetDeviceRegistryPropertyA
#define SetupDiSetDriverInstallParams	SetupDiSetDriverInstallParamsA
#define SetupDiSetSelectedDriver	SetupDiSetSelectedDriverA
#define SetupDuplicateDiskSpaceList	SetupDuplicateDiskSpaceListA
#define SetupFindFirstLine	SetupFindFirstLineA
#define SetupFindNextMatchLine	SetupFindNextMatchLineA
#define SetupFreeSourceList	SetupFreeSourceListA
#define SetupGetBackupInformation	SetupGetBackupInformationA
#define SetupGetFileCompressionInfo	SetupGetFileCompressionInfoA
#define SetupGetInfFileList	SetupGetInfFileListA
#define SetupGetInfInformation	SetupGetInfInformationA
#define SetupGetLineByIndex	SetupGetLineByIndexA
#define SetupGetLineCount	SetupGetLineCountA
#define SetupGetLineText	SetupGetLineTextA
#define SetupGetMultiSzField	SetupGetMultiSzFieldA
#define SetupGetSourceFileLocation	SetupGetSourceFileLocationA
#define SetupGetSourceFileSize	SetupGetSourceFileSizeA
#define SetupGetSourceInfo	SetupGetSourceInfoA
#define SetupGetStringField	SetupGetStringFieldA
#define SetupGetTargetPath	SetupGetTargetPathA
#define SetupInitializeFileLog	SetupInitializeFileLogA
#define SetupInstallFile	SetupInstallFileA
#define SetupInstallFileEx	SetupInstallFileExA
#define SetupInstallFilesFromInfSection	SetupInstallFilesFromInfSectionA
#define SetupInstallFromInfSection	SetupInstallFromInfSectionA
#define SetupInstallServicesFromInfSection	SetupInstallServicesFromInfSectionA
#define SetupInstallServicesFromInfSectionEx	SetupInstallServicesFromInfSectionExA
#define SetupIterateCabinet	SetupIterateCabinetA
#define SetupLogError	SetupLogErrorA
#define SetupLogFile	SetupLogFileA
#define SetupOpenAppendInfFile	SetupOpenAppendInfFileA
#define SetupOpenInfFile	SetupOpenInfFileA
#define SetupPromptForDisk	SetupPromptForDiskA
#define SetupQueryDrivesInDiskSpaceList	SetupQueryDrivesInDiskSpaceListA
#define SetupQueryFileLog	SetupQueryFileLogA
#define SetupQueryInfFileInformation	SetupQueryInfFileInformationA
#define SetupQueryInfOriginalFileInformation	SetupQueryInfOriginalFileInformationA
#define SetupQueryInfVersionInformation	SetupQueryInfVersionInformationA
#define SetupQuerySourceList	SetupQuerySourceListA
#define SetupQuerySpaceRequiredOnDrive	SetupQuerySpaceRequiredOnDriveA
#define SetupQueueCopy	SetupQueueCopyA
#define SetupQueueCopyIndirect	SetupQueueCopyIndirectA
#define SetupQueueCopySection	SetupQueueCopySectionA
#define SetupQueueDefaultCopy	SetupQueueDefaultCopyA
#define SetupQueueDelete	SetupQueueDeleteA
#define SetupQueueDeleteSection	SetupQueueDeleteSectionA
#define SetupQueueRename	SetupQueueRenameA
#define SetupQueueRenameSection	SetupQueueRenameSectionA
#define SetupRemoveFileLogEntry	SetupRemoveFileLogEntryA
#define SetupRemoveFromDiskSpaceList	SetupRemoveFromDiskSpaceListA
#define SetupRemoveFromSourceList	SetupRemoveFromSourceListA
#define SetupRemoveInstallSectionFromDiskSpaceList	SetupRemoveInstallSectionFromDiskSpaceListA
#define SetupRemoveSectionFromDiskSpaceList	SetupRemoveSectionFromDiskSpaceListA
#define SetupRenameError	SetupRenameErrorA
#define SetupScanFileQueue	SetupScanFileQueueA
#define SetupSetDirectoryId	SetupSetDirectoryIdA
#define SetupSetDirectoryIdEx	SetupSetDirectoryIdExA
#define SetupSetFileQueueAlternatePlatform	SetupSetFileQueueAlternatePlatformA
#define SetupSetPlatformPathOverride	SetupSetPlatformPathOverrideA
#define SetupSetSourceList	SetupSetSourceListA
#endif	/* UNICODE */	

#endif /* RC_INVOKED */

#ifdef __cplusplus
}
#endif

#include <poppack.h>
#endif /* _SETUPAPI_H_ */

