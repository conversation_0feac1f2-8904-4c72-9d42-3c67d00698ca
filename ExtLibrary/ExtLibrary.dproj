﻿	<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
		<PropertyGroup>
			<ProjectGuid>{9A124F0D-203C-468B-B0BE-F0CA28B23813}</ProjectGuid>
			<MainSource>ExtLibrary.dpk</MainSource>
			<Config Condition="'$(Config)'==''">Debug</Config>
			<DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
			<ProjectVersion>12.3</ProjectVersion>
			<Base>True</Base>
			<Platform>Win32</Platform>
			<AppType>Package</AppType>
			<FrameworkType>None</FrameworkType>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
			<Cfg_1>true</Cfg_1>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
			<Cfg_2>true</Cfg_2>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base)'!=''">
			<DCC_ImageBase>00400000</DCC_ImageBase>
			<DCC_UsePackage>vcl;vclx;VclSmp;rtl;dbrtl;adortl;vcldb;inet;dsnap;dsnapcon;indy</DCC_UsePackage>
			<GenPackage>true</GenPackage>
			<DCC_DependencyCheckOutputName>C:\Users\<USER>\Documents\RAD Studio\7.0\Bpl\ExtLibrary.bpl</DCC_DependencyCheckOutputName>
			<DCC_UnitAlias>WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE;WinTypes=Windows;WinProcs=Windows;$(DCC_UnitAlias)</DCC_UnitAlias>
			<DCC_Platform>x86</DCC_Platform>
			<DCC_UnitSearchPath>E:\XCL;$(DCC_UnitSearchPath)</DCC_UnitSearchPath>
			<DCC_S>false</DCC_S>
			<DCC_OutputNeverBuildDcps>true</DCC_OutputNeverBuildDcps>
			<GenDll>true</GenDll>
			<DCC_N>true</DCC_N>
			<DCC_E>false</DCC_E>
			<DCC_F>false</DCC_F>
			<DCC_K>false</DCC_K>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_1)'!=''">
			<DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
			<DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
			<DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
			<DCC_DebugInformation>false</DCC_DebugInformation>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_2)'!=''">
			<DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
		</PropertyGroup>
		<ItemGroup>
			<DelphiCompile Include="ExtLibrary.dpk">
				<MainSource>MainSource</MainSource>
			</DelphiCompile>
			<DCCReference Include="rtl.dcp"/>
			<DCCReference Include="XCL.dcp"/>
			<DCCReference Include="UCommPanels.pas"/>
			<DCCReference Include="UBoard.pas"/>
			<DCCReference Include="UBlogView.pas"/>
			<DCCReference Include="UExtFuncs.pas"/>
			<DCCReference Include="UCommonClasses.pas"/>
			<BuildConfiguration Include="Debug">
				<Key>Cfg_2</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
			<BuildConfiguration Include="Base">
				<Key>Base</Key>
			</BuildConfiguration>
			<BuildConfiguration Include="Release">
				<Key>Cfg_1</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
		</ItemGroup>
		<Import Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')" Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
		<Import Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')" Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj"/>
		<ProjectExtensions>
			<Borland.Personality>Delphi.Personality.12</Borland.Personality>
			<Borland.ProjectType>Package</Borland.ProjectType>
			<BorlandProject>
				<Delphi.Personality>
					<Source>
						<Source Name="MainSource">ExtLibrary.dpk</Source>
					</Source>
					<Parameters/>
					<VersionInfo>
						<VersionInfo Name="IncludeVerInfo">True</VersionInfo>
						<VersionInfo Name="AutoIncBuild">False</VersionInfo>
						<VersionInfo Name="MajorVer">1</VersionInfo>
						<VersionInfo Name="MinorVer">0</VersionInfo>
						<VersionInfo Name="Release">0</VersionInfo>
						<VersionInfo Name="Build">0</VersionInfo>
						<VersionInfo Name="Debug">False</VersionInfo>
						<VersionInfo Name="PreRelease">False</VersionInfo>
						<VersionInfo Name="Special">False</VersionInfo>
						<VersionInfo Name="Private">False</VersionInfo>
						<VersionInfo Name="DLL">False</VersionInfo>
						<VersionInfo Name="Locale">2052</VersionInfo>
						<VersionInfo Name="CodePage">936</VersionInfo>
					</VersionInfo>
					<VersionInfoKeys>
						<VersionInfoKeys Name="CompanyName"/>
						<VersionInfoKeys Name="FileDescription"/>
						<VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="InternalName"/>
						<VersionInfoKeys Name="LegalCopyright"/>
						<VersionInfoKeys Name="LegalTrademarks"/>
						<VersionInfoKeys Name="OriginalFilename"/>
						<VersionInfoKeys Name="ProductName"/>
						<VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="Comments"/>
					</VersionInfoKeys>
				</Delphi.Personality>
				<Platforms>
					<Platform value="Win32">True</Platform>
				</Platforms>
			</BorlandProject>
			<ProjectFileVersion>12</ProjectFileVersion>
		</ProjectExtensions>
	</Project>
