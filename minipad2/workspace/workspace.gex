[General]
Name=workspace
Project=1
[Classes]
Count=31
Class0="TDataStore","TxlInterfacedObject, ILangObserver, ICommandExecutor, IListProvider","UDataStore","0","E:\minipad2\workspace\UDataStore.pas"
Class1="TEditorClient","TClientSuper, IOptionObserver, IMemorizer, IEventObserver","UEditor","0","E:\minipad2\workspace\UEditor.pas"
Class2="TListClient","TClientSuper, IOptionObserver, IEventObserver","UList","0","E:\minipad2\workspace\UList.pas"
Class3="TPageSuper","","UPageSuper","0","E:\minipad2\workspace\UPageSuper.pas"
Class4="TTabNavigator","TNavigatorSuper, IOptionObserver, ISizeObserver","UTabNavigator","0","E:\minipad2\workspace\UTabNavigator.pas"
Class5="TTreeNavigator","TNavigatorSuper, IOptionObserver","UTreeNavigator","0","E:\minipad2\workspace\UTreeNavigator.pas"
Class6="TWorkSpace","TxlPanel, IMemorizer, IEventObserver, IOptionObserver, ISizeObserver, IPageObserver","UWorkSpace","0","E:\minipad2\workspace\UWorkSpace.pas"
Class7="THistory","","UBrowseHistory","0","E:\minipad2\workspace\UBrowseHistory.pas"
Class8="TPageFactory","","UPageList","0","E:\minipad2\workspace\UPageList.pas"
Class9="TPageImageList","","UPageList","0","E:\minipad2\workspace\UPageList.pas"
Class10="TPageNameManager","","UPageList","0","E:\minipad2\workspace\UPageList.pas"
Class11="TPageDefSettingsManager","","UPageList","0","E:\minipad2\workspace\UPageList.pas"
Class12="TPageList","TxlCollection","UPageList","0","E:\minipad2\workspace\UPageList.pas"
Class13="TListPageSuper","TPageSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class14="TGroupSuper","TListPageSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class15="TGroupPage","TGroupSuper","UNotePage","19","E:\minipad2\workspace\UNotePage.pas"
Class16="TRecycleBin","TGroupSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class17="TNotePage","TPageSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class18="TFavoritePage","TGroupSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class19="TTagPage","TGroupSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class20="TChildItem","TPageSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class21="TSearchPage","TGroupSuper","UNotePage","0","E:\minipad2\workspace\UNotePage.pas"
Class22="TListSettings","","UListSettings","0","E:\minipad2\workspace\UListSettings.pas"
Class23="TClientSuper","TxlInterfacedObject, IPageObserver, ICommandExecutor","UClientSuper","0","E:\minipad2\workspace\UClientSuper.pas"
Class24="TNavigatorSuper","TxlInterfacedObject, IPageObserver, ICommandExecutor","UNavigatorSuper","0","E:\minipad2\workspace\UNavigatorSuper.pas"
Class25="TPanelSuper","TxlDialogML","UPropertyBox","0","E:\minipad2\workspace\UPropertyBox.pas"
Class26="TGeneralPanel","TPanelSuper","UPropertyBox","0","E:\minipad2\workspace\UPropertyBox.pas"
Class27="TListPanel","TPanelSuper","UPropertyBox","0","E:\minipad2\workspace\UPropertyBox.pas"
Class28="TPropertyFrame","TxlFrame","UPropertyBox","0","E:\minipad2\workspace\UPropertyBox.pas"
Class29="TPropertyBox","TxlDialog","UPropertyBox","0","E:\minipad2\workspace\UPropertyBox.pas"
Class30="TBlogClient","TClientSuper","UBlog","0","E:\minipad2\workspace\UBlog.pas"
