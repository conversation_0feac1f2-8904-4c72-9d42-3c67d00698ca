[General]
Name=pages
Project=0
[Classes]
Count=26
Class0="TFavoritePage","TPageContainer","UFavoritePage","0","E:\minipad2\pages\UFavoritePage.pas"
Class1="TFavoriteHandler","TxlInterfacedObject, ICommandExecutor, IListProvider","UFavoritePage","0","E:\minipad2\pages\UFavoritePage.pas"
Class2="TGroupPage","TPageContainer","UGroupPage","7","E:\minipad2\pages\UGroupPage.pas"
Class3="TRecycleBin","TGroupPage","UGroupPage","0","E:\minipad2\pages\UGroupPage.pas"
Class4="TNotePage","TEditPageSuper","UNotePage","0","E:\minipad2\pages\UNotePage.pas"
Class5="TPageFactory","","UPageFactory","0","E:\minipad2\pages\UPageFactory.pas"
Class6="TPageImageList","","UPageFactory","0","E:\minipad2\pages\UPageFactory.pas"
Class7="TPageNameManager","","UPageFactory","0","E:\minipad2\pages\UPageFactory.pas"
Class8="TPageDefSettingsManager","","UPageFactory","0","E:\minipad2\pages\UPageFactory.pas"
Class9="TPageEventCenter","","UPageFactory","0","E:\minipad2\pages\UPageFactory.pas"
Class10="TPropertySuper","","UPageProperty","0","E:\minipad2\pages\UPageProperty.pas"
Class11="TTruePageProperty","TPropertySuper","UPageProperty","0","E:\minipad2\pages\UPageProperty.pas"
Class12="TPageChilds","TPropertySuper","UPageProperty","0","E:\minipad2\pages\UPageProperty.pas"
Class13="TListProperty","TPropertySuper","UPageProperty","0","E:\minipad2\pages\UPageProperty.pas"
Class14="TPageStore","","UPageStore","0","E:\minipad2\pages\UPageStore.pas"
Class15="TPageSuper","","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class16="TTruePageSuper","TPageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class17="TListPageSuper","TTruePageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class18="TPageContainer","TListPageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class19="TEditPageSuper","TTruePageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class20="TRecentPage","TListPageSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class21="TRecentHandler","TxlInterfacedObject, ICommandExecutor, IListProvider","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class22="TSearchPage","TListPageSuper","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class23="TSearchBox","TxlDialog","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class24="TSearchHandler","TxlInterfacedObject, ICommandExecutor","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class25="TTagPage","TPageContainer","UTagPage","0","E:\minipad2\pages\UTagPage.pas"
Class26="TChildItemContainer","TPageContainer","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class27="TEditPageSuper","TTruePageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class28="TChildItemSuper","TPageSuper","UPageSuper","0","E:\minipad2\pages\UPageSuper.pas"
Class29="TRecentPage_Root","TPageContainer","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class30="TRecentPageSuper","TListPageSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class31="TRecentPage_Create","TRecentPageSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class32="TRecentPage_Modify","TRecentPageSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class33="TRecentPage_Visit","TRecentPageSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class34="TRecentCreateHandler","TSysPageContainerHandler","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class35="TRecentModifyHandler","TSysPageContainerHandler","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class36="TRecentVisitHandler","TSysPageContainerHandler","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class37="TRecentRootHandler","TSysPageHandlerSuper","URecentPage","0","E:\minipad2\pages\URecentPage.pas"
Class38="TRecycleBin","TGroupSuper","URecycleBin","0","E:\minipad2\pages\URecycleBin.pas"
Class39="TRecycleHandler","TSysPageHandlerSuper","URecycleBin","0","E:\minipad2\pages\URecycleBin.pas"
Class40="TRootPageHandlerSuper","TxlInterfacedObject, ICommandExecutor, ILangObserver","URootPageHandler","0","E:\minipad2\pages\URootPageHandler.pas"
Class41="TRootListPageHandler","TRootPageHandlerSuper, IListProvider","URootPageHandler","0","E:\minipad2\pages\URootPageHandler.pas"
Class42="TSearchPage","TListPageSuper","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class43="TSearchBox","TxlDialog","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class44="TSearchHandler","TSysPageHandlerSuper","USearchPage","0","E:\minipad2\pages\USearchPage.pas"
Class45="TSysPageHandlerSuper","TxlInterfacedObject, ICommandExecutor, ILangObserver","USysPageHandler","0","E:\minipad2\pages\USysPageHandler.pas"
Class46="TSysListPageHandler","TSysPageHandlerSuper, IListProvider","USysPageHandler","0","E:\minipad2\pages\USysPageHandler.pas"
Class47="TSysPageContainerHandler","TSysListPageHandler","USysPageHandler","0","E:\minipad2\pages\USysPageHandler.pas"
Class48="THandlerWithSubHotKey","TSysListPageHandler, IOptionObserver, IHotkeyOwner","USysPageHandler","0","E:\minipad2\pages\USysPageHandler.pas"
Class49="THandlerWithPaste","THandlerWithSubHotKey","USysPageHandler","0","E:\minipad2\pages\USysPageHandler.pas"
Class50="TTagPage","TPageContainer","UTagPage","0","E:\minipad2\pages\UTagPage.pas"
