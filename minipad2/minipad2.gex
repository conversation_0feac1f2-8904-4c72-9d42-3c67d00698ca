[General]
Name=minipad2
Project=0
[Classes]
Count=39
Class0="TLVToLV","","UDefineToolbarBox","0","E:\minipad2\UDefineToolbarBox.pas"
Class1="TDefineToolBarBox","TxlDialog","UDefineToolbarBox","0","E:\minipad2\UDefineToolbarBox.pas"
Class2="TAboutBox","TxlDialog","UDialogs","0","E:\minipad2\UDialogs.pas"
Class3="TTextCountBox","TxlDialog","UDialogs","0","E:\minipad2\UDialogs.pas"
Class4="TLogInBox","TxlDialog","UDialogs","0","E:\minipad2\UDialogs.pas"
Class5="TImportBox","TxlDialog","UDialogs","0","E:\minipad2\UDialogs.pas"
Class6="TCalculator","","UExtFuncs","0","E:\minipad2\UExtFuncs.pas"
Class7="TDictionary","","UExtFuncs","0","E:\minipad2\UExtFuncs.pas"
Class8="TSizeManager","TxlInterfacedObject, IOptionObserver","UGlobalObj","0","E:\minipad2\UGlobalObj.pas"
Class9="TScrollManager","TxlInterfacedObject, IOptionObserver","UGlobalObj","0","E:\minipad2\UGlobalObj.pas"
Class10="TSaveManager","TxlSaveCenter, IMemorizer, IOptionObserver","UGlobalObj","0","E:\minipad2\UGlobalObj.pas"
Class11="TLangManager","TxlLanguage, IOptionObserver","ULangManager","0","E:\minipad2\ULangManager.pas"
Class12="TMainForm","TxlWindow, ICommandExecutor, IMemorizer, IOptionObserver, ISizeObserver, IEventObserver","UMainForm","0","E:\minipad2\UMainForm.pas"
Class13="TMenuSuper","TxlMenu, ILangObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class14="TMainMenu","TMenuSuper, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class15="TTrayMenu","TMenuSuper, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class16="TPopupMenu","TxlMenu, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class17="TAccelTable","TxlAccelTable","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class18="TMenuManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver, ISizeObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class19="TOptionFrame","TxlFrame","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class20="TOptionPanel","TxlDialogML","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class21="TOpProgram","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class22="TOpAppearance","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class23="TOpBehavior","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class24="TOpSafety","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class25="TOpSpecialMode","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class26="TOpFontColor","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class27="TOpEditor","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class28="TOpExtFuncs","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class29="TOpCalcPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class30="TOpDictPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class31="TOpLinkPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class32="TOpTemplate","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class33="TOpClipboard","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class34="TOptionBox","TxlDialog","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class35="TOptionManager","TxlOption","UOptionManager","0","E:\minipad2\UOptionManager.pas"
Class36="TMemoryManager","TxlMemory","UOptionManager","0","E:\minipad2\UOptionManager.pas"
Class37="TSpecialMode","","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class38="TSpModeManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class39="TSpModeManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class40="TSpecialMode","","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class41="TSpModeManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class42="TLinkList","","UListObjects","0","E:\minipad2\UListObjects.pas"
Class43="TFastLinkList","TListObjSuper","UListObjects","0","E:\minipad2\UListObjects.pas"
Class44="TTemplateList","TListObjSuper","UListObjects","0","E:\minipad2\UListObjects.pas"
Class45="TClipList","TListObjSuper, ISaver","UListObjects","0","E:\minipad2\UListObjects.pas"
Class46="TListSettings","","UListStore","0","E:\minipad2\UListStore.pas"
Class47="TListStoreSuper","TxlInterfacedObject, ILangObserver","UListStore","25","E:\minipad2\UListStore.pas"
Class48="TGroupAdapter","TListStoreSuper","UListStore","0","E:\minipad2\UListStore.pas"
Class49="TMemoStore","TListStoreSuper","UListStore","0","E:\minipad2\UListStore.pas"
Class50="TLinkStore","TListStoreSuper","UListStore","0","E:\minipad2\UListStore.pas"
Class51="TContactStore","TListStoreSuper","UListStore","0","E:\minipad2\UListStore.pas"
Class52="TTemplateStore","TListStoreSuper","UListStore","0","E:\minipad2\UListStore.pas"
Class53="TListStoreFactory","TxlInterfacedObject, ISaver","UListStore","0","E:\minipad2\UListStore.pas"
Class54="TMainForm","TxlWindow, ICommandExecutor, IMemorizer, IOptionObserver, ISizeObserver, IEventObserver","UMainForm","0","E:\minipad2\UMainForm.pas"
Class55="TMenuSuper","TxlMenu, ILangObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class56="TMainMenu","TMenuSuper, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class57="TTrayMenu","TMenuSuper, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class58="TPopupMenu","TxlMenu, IEventObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class59="TAccelTable","TxlAccelTable","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class60="TMenuManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver, ISizeObserver","UMenuManager","0","E:\minipad2\UMenuManager.pas"
Class61="TScreenQuery","TxlInterfacedObject, IOptionObserver, IEventObserver","UMiscClass","0","E:\minipad2\UMiscClass.pas"
Class62="TTipQuery","TxlInterfacedObject, IOptionObserver, IEventObserver","UMiscClasses","0","E:\minipad2\UMiscClasses.pas"
Class63="TOptionFrame","TxlFrame","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class64="TOptionPanel","TxlDialogML","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class65="TOpProgram","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class66="TOpAppearance","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class67="TOpBehavior","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class68="TOpSafety","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class69="TOpSpecialMode","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class70="TOpFontColor","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class71="TOpEditor","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class72="TOpExtFuncs","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class73="TOpCalcPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class74="TOpDictPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class75="TOpLinkPage","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class76="TOpTemplate","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class77="TOpClipboard","TOptionPanel","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class78="TOptionBox","TxlDialog","UOptionBox","0","E:\minipad2\UOptionBox.pas"
Class79="TNavigatorSuper","TxlInterfacedObject","USpace","0","E:\minipad2\USpace.pas"
Class80="TClientSuper","TxlInterfacedObject","USpace","0","E:\minipad2\USpace.pas"
Class81="THistory","","USpace","0","E:\minipad2\USpace.pas"
Class82="TSpace","TxlInterfacedObject, IOptionObserver, ISaver, ICommandExecutor, IEventObserver, IMemorizer, ILangObserver","USpace","0","E:\minipad2\USpace.pas"
Class83="TSpecialMode","","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class84="TSpModeManager","TxlInterfacedObject, IOptionObserver, IMemorizer, ICommandExecutor, IEventObserver","USpecialMode","0","E:\minipad2\USpecialMode.pas"
Class85="TTabNavigator","TNavigatorSuper, IOptionObserver, ISizeObserver","UTabNavigator","0","E:\minipad2\UTabNavigator.pas"
Class86="TTreeNavigator","TNavigatorSuper, IOptionObserver, ILangObserver","UTreeNavigator","0","E:\minipad2\UTreeNavigator.pas"
Class87="TWorkSpace","TxlPanel, IMemorizer, IEventObserver, IOptionObserver, ISizeObserver","UWorkSpace","0","E:\minipad2\UWorkSpace.pas"
