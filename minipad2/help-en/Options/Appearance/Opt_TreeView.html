<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Tree Panel</title>
</head>

<body>
	<h1>Tree Panel</h1>

	<p><b>Tree Panel:</b> Set the font ( including size, style & color ) and backcolor of the tree panel.</p>

	<p><b>Tree on right:</b> Set whether to put the tree panel in the right side of the editor ( by default the tree panel is in the left side of the editor ).</p>

	<p><b>Tree horz-scroll:</b> set whether the tree-view shows horizontal scrollbar or not.</p>

	<p><b>Show node buttons:</b> set whether to show "+" before collapsed node and "-" before expanded node or not. </p>

	<p><b>Show node lines:</b> set whether to show lines connecting sibling nodes and child nodes.</p>

	<p><b>Show node icons:</b> Set whether to show node icons in the treeview which indicates the page-type (Note, Calc, Memo...) of the node.</p>

	<p><b>Lines at root:</b> set whether to show lines at root level (without this line can save a few horizontal spaces for the treeview)</p>
</body>
</html>
