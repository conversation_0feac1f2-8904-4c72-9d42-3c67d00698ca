<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Appearance</title>
</head>

<body>
	<h1>Appearance</h1>
	
	<p><b>Languages:</b> Choose the language of the program. (take effect after exit the "Options" dialog).</p>

	<p><b>Tool-window style:</b> set whether the main-window be tool-window-style (no "maximize" and "minimize" on the titlebar) or not.</p>

	<p><b>Pagename on titlebar:</b> if checked, then the text of titlebar will be something like "note1 - minipad2"; otherwise only "minipad2".</p>

	<p><b>Show menubar:</b> Show / hide the menubar.</p>

	<p><b>Show toolbar:</b> Show / hide the toolbar.</p>

	<p><b>Splitter width: </b>Set the width of the splitter (including the small button on it) between the tree-panel and the editor.</p>
	
	<p><b>Transparency:</b> Set the transparency of the main window. (  from 1 ... 10, where 10 is the highest transparency level ). To switch between opaque and transparent, use "Tools"--><a href="..\..\Menus\Menus_Tools.html#m_transparent">"Transparent"</a>.</p>

	<p><b>Auto adjust appearance:</b> Let the program automatically adusts the visual elements ( menubar, toolbar, tabbar ... ) when the size of the window changed. Please see "Featured Functions"--><a href='..\..\Featured Functions.html#autoadjust'>"Auto adjust appearance"</a>.<br>
</body>
</html>
