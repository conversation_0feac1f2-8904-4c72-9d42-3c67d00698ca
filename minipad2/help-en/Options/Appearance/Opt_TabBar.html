<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Tab Bar</title>
</head>

<body>
	<h1>Tab Bar</h1>

	<p><b>Tab bar:</b> Set the font of the tab bar (including font-size, font-sytle, font-color, etc.)</p>

	<p><b>Tab on bottom:</b> If set to yes, then the tabbar will be on the bottom. Otherwise it will be on the top side.</p>

	<p><b>Multi-line tabs:</b> If set to yes, then the tabbar will be multi-line style. Otherwise it will be single-line style.</p>

	<p><b>Show tab icons:</b> Set whether to show icons on the tabbar which indicates the page-type (Note, Calc, Memo...) of the tabpage.</p>

	<p><b>Highlight current tab:</b> Set whether to highlight the current tab.</p>

	<p><b>Fixed tab width:</b> Set whether the width of the tabs be fixed (adjustable from 1...10) or auto-adjusted (depending on the length of the title).</p>
</body>
</html>
