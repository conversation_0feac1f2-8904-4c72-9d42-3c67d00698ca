<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Template</title>
</head>

<body>
	<h1>Template</h1>

	<p><b>Template Hotkey:</b> Set the hotkey for the template-list popup menu. (You can use this hotkey to launch the pop-up menu of template list in any windows editor).</p>

	<p><a name="enablesubkey"></a><b>Enable subitem hotkey:</b> Set whether to register global hotkeys for the first nine Templates.</p>

	<p><b>Capture hotkey:</b> Set the hotkey to "capture" (add) the selected text to the template list.</p>

	<p><b>Template / Clipboard popup menu no focus:</b> Set when use hotkey to launch the popup-menu, the input focus is still on the editor, not on the popup-menu. (the old 2.3/3.0 style)<br>
	Advantage: Can be used to edit filenames in Windows Explorer. If this item is set to "No", when the menu popups, the input focus will change to the menu, and the file-name-inplace-editor will disppear.<br>
	Disadvantage: Since there's no focus for the popup menu, it's not possible to use 1..9 to select the first nine items; Also it's not possible to use Esc to cancel the menu.<br>
	This settings is valid for the popup menus of both Template and Clipboard.<br>
	This settings is by default unchecked. i.e., the popup-menu has focus, can use 1..9 to select the first nine items, can cancel the menu with Esc.</p>

	<p>See "Extensions"--><a href='..\..\Extensions\Ext_Template.html'>"Template"</a></p><br>
</body>
</html>
