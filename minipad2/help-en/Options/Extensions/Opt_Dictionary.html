<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Dictionary</title>
</head>

<body>
	<h1>Dictionary</h1>

	<p><b>Dictionary List:</b> Here you manage all the configured dictionaries, you can freely enable or disable some dictionaries, or adjust their query-orders.</p>

	<p><b>Show multi-dict results:</b> Set whether to show multiple query results when more than one dictionary is configured. (If unchecked, then after the first match is found, the program will stop furthur searching and gives out the result immediately. But in this case you can still use Shift_Enter to get the full search results)</p>

	<p><b>Tip-query hotkey:</b> Set the global hotkey for Tip-query (Inplace query). Select the target word in any software (Editor, Browser...) and press this hotkey, you'll see the query result (in Tooltip style) immediately. The color of the Tooltip window can also be set here.</p>

	<p>See "Extensions"--><a href='..\..\Extensions\Ext_Dictionary.html'>"Dictionary"</a></p>.<br>
</body>
</html>
