<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Clipboard</title>
</head>

<body>
	<h1>Clipboard</h1>

	<p><b>Maximum items:</b> Set the maximum number of clipItems. ( After this limit is reached, when there's a new item captured, the oldest item will be automatically deleted, so that the total clip count will not exceed this limit ). </p>

	<p><b>Item size limit:</b> Set the maximum byte size for a single cliptext. ( If the byte size of a newly copied text block exceeds this limit, it will not be added to the clip list of minipad2 ).</p>

	<p><b>Menu width:</b> Set the character limit of each item for the pop-up menu.</p>

	<p><b>Filter duplicate items: </b>When a new clip-item is captured, if there's already an item with the same text exists in the list, then the new item will not be pushed into the list.</p>

	<p><b>Only filter neighboring duplicate items: </b>When a new clip-item is captured, if the first item in the list has the same text with the new item, then the new item will not be pushed into the list.</p>

	<p><b>New pasted item go to top:</b> If this option is checked, then each time when an item in the list is pasted, this item will float to the top position.</p>

	<p><b>Popup hotkey:</b> Set the hotkey for the clip-list popup menu. (You can use this hotkey to launch the pop-up menu of clip list in any windows editor).</p>

	<p><a name="m_enablesubkey"></a><b>Enable subitem hotkey:</b> Set whether to register global hotkeys for the first nine ClipItems.</p>

	<p>See "Extensions"--><a href='..\..\Extensions\Ext_MultiClipboard.html'>"Multi-Clipboard"</a>.</p><br>
</body>
</html>
