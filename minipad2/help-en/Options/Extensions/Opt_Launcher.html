<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Launcher</title>
</head>

<body>
	<h1>Launcher</h1>

	<p><b>Disable Icon-read:</b> If set to "Yes", then for program/file links minipad2 will not read the separate icons, but use the same default icon for every link. This can improve the load-speed for the LinkPage (especially when there's some items link to the files on another computer through the intranet).</p>

	<p><b>Auto minimize minipad2 after open link:</b> set whether to minimize minipad2 after double-click and opening link. This is a useful feature for users to use minipad2 as program launcher.</p>

	<p><a name="fastlinkhotkey"></a><b>FastLink Hotkey:</b> Set the hotkey for FastLink pop-up menu.</p>

	<p><a name="enablesubkey"></a><b>Enable subitem hotkey:</b> Set whether to register global hotkeys for the first nine FastLinks.</p>

	<p>See "Extensions"--><a href='..\..\Extensions\Ext_Launcher.html'>"Launcher"</a></p><br>
</body>
</html>
