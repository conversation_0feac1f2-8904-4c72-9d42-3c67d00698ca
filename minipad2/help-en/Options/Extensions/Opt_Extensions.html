<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Extensions</title>
</head>

<body>
	<h1>Extensions</h1>

	<p>The various extensions of minipad2 are like plug-ins which can be freely enabled/disabled (i.e., show or hide from the menubar and the toolbar) in this option page. </p>

	<p><b>Enable CalcPage:</b> set whether to activate the <a href='..\..\Extensions\Ext_Calculator.html'>Calculator</a> extension.</p>

	<p><b>Enable MemoPage:</b> Set whether to activate the <a href='..\..\Extensions\Ext_Memo.html'>Memo</a> extension.</p>

	<p><b>Enable DictPage:</b> Set whether to activate the <a href='..\..\Extensions\Ext_Dictionary.html'>Dictionary</a> extension.</p>

	<p><b>Enable LinkPage:</b> Set whether to activate the <a href='..\..\Extensions\Ext_Launcher.html'>Launcher</a> extension.</p>

	<p><b>Enable ContactPage:</b> Set whether to activate the <a href='..\..\Extensions\Ext_ContactList.html'>Contact List</a> extension.</p>

	<p><b>Enable Template:</b> Set whether to activate the <a href='..\..\Extensions\Ext_Template.html'>Template</a> extension.</p>

	<p><b>Enable Clipboard:</b> Set whether to activate the <a href='..\..\Extensions\Ext_MultiClipboard.html'>Multi-Clipboard</a> extension.</p>

	<p>Remark: The modifications in this page will take effect after the program is re-opened.</p>

	<p>For the detailed descriptions of the various extensions, please refer to the corresponding chapters in <a href='..\..\Extensions\Extensions.html'>"Extensions"</a>.</p><br>
</body>
</html>
