<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Import / Export</title>
</head>

<body>
	<h1>Import / Export</h1>

	<p><b>When import:</b> Set whether to show the import dialog, or directly import using the following default settings:<br>
	&emsp;&emsp;<b>Import from:</b> Set the default import source: from file, or from folder<br>
	&emsp;&emsp;<b>External save:</b> If checked on this option, then the content is still saved in the original text file, in minipad2 only create a virtual node link to this file. Otherwise the whole content of the text file will be imported into the database of minipad2.</p>
	
	<p><b>When export:</b> Set whether to show the export dialog, or directly export using the following default settings:<br>
	&emsp;&emsp;<b>Encoding:</b> Set the encoding of the exported text file ( Ansi, UTF-8, UTF16LE (Unicode) or UTF16BE ).<br>
	&emsp;&emsp;<b>Export to:</b> Set the default target for export (clipboard, file, folder).</p>
	
	<p><b>Separator line:</b> Set the separator line for the contents of nodes, when export group to a single file ( Supports the various <a href="..\..\Extensions\Ext_Template.html#designator">designators</a> of the template extension )</p>

	<p><b>Use virtual import when drag-in files:</b> Set when import files using drag-drop, whether be virtual import by default. ( Shift+dragdrop means virtual import, Ctrl+dragdrop means import content. The meaning of direct dragdrop depends on the settings here ).</p>
</body>
</html>
