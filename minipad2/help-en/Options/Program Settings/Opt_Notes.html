<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Notes</title>
</head>

<body>
	<h1>Notes</h1>

	<p><b>New note hotkey:</b> Use this hotkey to launch the "New Note" dialog and write note. If there're text selected, then the selected text will be the title of the new note by default. In this dialog you can use "@xxx" to input the title of the new note in the first line.</p>

	<p><b>Close note hotkey:</b> Use this hotkey to save the new note and close the dialog ( same as click the close button of the dialog ).</p>

	<p><b>New note in background:</b> Use this hotkey to create a new note in background. If there're text selected, then the selected text will be the title of the new note by default.</p>

	<p><b>Snap text:</b> Use this hotkey to snap the selected text to the current note in minipad2.</p>

	<p><b>New note in background and snap text:</b> Use this hotkey to create a new note in background, and snap the selected text to this note.</p>

	<p><b>Snap text to file:</b> Press this hotkey will open the "Save to File" dialog, to save the selected text to the defined text file.</p>
	
	<p><b>Switch auto record:</b>Use this hotkey to enable / disable auto-record.</p>
</body>
</html>
