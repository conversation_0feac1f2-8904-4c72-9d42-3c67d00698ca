<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Auto hide</title>
</head>

<body>
	<h1>Auto hide</h1>

	<p>This option page contains the various settings for the Auto-hide feature.</p>

	<p><b>Direction:</b> the orientation for the side-hide. including four fixed directions (Left, Top, Right, Bottom) and one special "Free" direction. The default mode is "Free", with this mode the window will always hides to the side which is most close to the window. That means if the window hide in the right side previously and if you want it to hide to to top-side now, you needn't modify the settings, simply drag the window to a place close to the top-side of the desktop.</p>
	 
	<p><b>Hide delay:</b> Set the delay time before hide, after the mouse-pointer leaves the window.</p>

	<p><b>Show delay:</b> Set the delay time for the window to show after the mouse-pointer reaches the side where the window hides.</p>

	<p><b>Animation time:</b> Set the speed of the window moving.</p>

	<p><b>Side width:</b> set the edge-width of the window when it hides in the side of the desktop.</p>

	<p>See "Featured Functions"--><a href='..\..\Featured Functions.html#autohide'>"Auto hide"</a>.</p><br>
</body>
</html>
