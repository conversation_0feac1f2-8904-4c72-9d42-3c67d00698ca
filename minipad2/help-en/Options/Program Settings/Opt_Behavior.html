<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../../main.css" rel="stylesheet" type="text/css">
	<title>Behavior</title>
</head>

<body>
	<h1>Behavior</h1>

	<p><b>Esc used for: Minimize / Clear Text</b><br>
		Choose the action of pressing Esc: minimize the main-window, or <a href='..\..\Menus\Menus_Edit.html#m_clear'>clear</a> the text of the current page.</p>
		
	<p><b>Double-Click on TabPage: Rename / Switch Property / Delete</b><br>
		Choose the action of double-click on tabpage: <a href='..\..\Menus\Menus_File.html#m_rename'>Rename</a> the page, or <a href='..\..\Menus\Menus_File.html#m_switchlock'>switch page property</a>, or <a href='..\..\Menus\Menus_File.html#m_delete'>delete</a> the current page.</p>

	<p><b>Middle-Click on TabPage: Rename / Switch Property / Delete</b><br>
		Choose the action of middle-click on tabpage: <a href='..\..\Menus\Menus_File.html#m_rename'>Rename</a> the page, or <a href='..\..\Menus\Menus_File.html#m_switchlock'>switch page property</a>, or <a href='..\..\Menus\Menus_File.html#m_delete'>delete</a> the current page.</p>

	<p><b>Double-Click on Group: New Page / Level Up</b><br>
		Choose the action of double-click on group (i.e., on empty part of tabbar or page-list): <a href='..\..\Menus\Menus_File.html#m_newnote'>New page</a>, or <a href='..\..\Menus\Menus_File.html#m_levelup'>go to the parent group</a>.</p><br>
</body>
</html>
