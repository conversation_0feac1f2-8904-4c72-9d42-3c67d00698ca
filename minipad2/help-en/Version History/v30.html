<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../main.css" rel="stylesheet" type="text/css">
	<title>v3.0</title>
</head>

<body>
	<center><h1>Version History<br>( 3.0 )</h1></center>
		<h2>Updates of the kernel part:</h2>
		<ol>
			<li>Rewrite the whole program using Object Pascal / pure Win32 SDK. Thus greatly reduced the exe size and enhanced the execution efficiency.
			<li>100% Unicode inside. Wrong encodings never appears anymore. 
			<li>Use RichEdit instead of Edit to be the kernel Edit control, use streamlized read/write mechanism, thus greatly increased the performance of the Editor especially for large text size. And now built-in support OLE dragdrop, link-highlight, unlimited undo/redo, etc. 
			<li>Fully new-designed data-storage structure, use indexed block-read-write instead of the original per-line read-write-decode, greatly increased the efficiency of data access.
		</ol>
		
		<h2>Updates of the functionality:</h2>

		<h2>3.0 final (2009-3-19)</h2>
		<ol>
			<li>Support line number. 
			<li>Support page margins. 
			<li>Support "Protected" mode of the page content. 
			<li>Add features of backup.
			<li>Add some new options like Tool-Window-Style, Transparency, etc. 
			<li>Add two new triangular conversion functions for the calc-page: deg&#12289;rad 
			<li>fixed some minor problems.
		</ol>
		
		<h2>3.0 alpha4 (2008-12-5)</h2>
		<ol>
			<li>Fully new-designed memo extension, which can be used as Reminder, Task Executor, or Scheduler. 
			<li>Integrate the dictionary extension of version 2.x.
			<li>Add the auto-hide function of v2.x. 
			<li>The editor now support Link-highlight. Double-click to open the link. 
			<li>Some other minor improvements. 
		</ol>
		
		<h2>3.0 alpha3 (2008-11-1)</h2>
		<ol>
			<li>Integrate the Template extension of v2.x. 
			<li>Add two new extensions: Link Page ( used as program launcher ), and Contact Page ( used as address list ).
			<li>Support page-move across the groups.
			<li>Support independant Font & BackColor settings for the tree window and list window. 
			<li>Other minor improvements and bug fixes. 
		</ol>
		
		<h2>3.0 alpha2 &#65288;2008-9-28&#65289;</h2>
		<ol>
			<li>Integrate the multi-clipboard extension of v2.x. 
			<li>Some improvements for the Calc Page: The triangular functions (sin, cos...) now support to do calculations based on degree. Support two constants: pi & e&#65288; e.g., sin(e)+2+pi &#65289;. Support Embeded-calculation in normal Note Page ( use shift+enter by default ).
			<li>Some other minor improvements and some new option items. 
		</ol>
		
		<h2>3.0 alpha1&#65288;2008-9-14&#65289;</h2>
		<ol>
			<li>Support divide pages into different groups. Support unlimited group amounts, user can freely switch between different groups.
			<li>Support switch between two styles: The traditional Multi-TabPage-Style and the new TreeView style. 
		</ol><br>
</body>
</html>
