<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../main.css" rel="stylesheet" type="text/css">
	<title>v2.x</title>
</head>

<body>
	<center><h1>Version History<br>( 1.0 ... 2.3.2 )</h1></center>
		<h2>2.3.0 ... 2.3.2 ( 2007.12.2. ... 2008.4.22. )</h2>
		<ol>
		    <li>Re-write 90% codes.
		    <li>New clipboard-enhancement function. The clipboard menu can be called out in any windows editor program.
		    <li>New text-template function. The template menu can also be called out in any windows editor program.
		    <li>Delete the Desktop-pad mode, add two "Special Effect" modes ( "auto-scrollback", "auto-hide" ).
		    <li>The memo function now supports four modes: "Once", "Weekly", "Monthly" and "Daily". Add the pop-up effect of memo window.
		    <li>New menuitem / toolbutton for fast-switching between different pages.
		    <li>New "Send Email" function. The content of the pages now can be sent via email. 
		    <li>New "Show Menubar" and "Auto adjust appearance" options.
		    <li>The toolbar is now user-definable.
		    <li>Support the right-click menu.
		    <li>Enhanced undo functions. Support the undo of "clear" and "replace".operations.
		    <li>New "Backup-center", allows to restore the page most recently deleted, and view the most recent two backup files.
		    <li>Numerous minor improvements and bug-fixings.
	    </ol>
		    
		<h2>2.2.0 ... 2.2.1 ( 2007.9.23. ... 9.26.)</h2>
		<ol>
		    <li>New "Dictionary Page". The dictionary list is user definable and freely extensible.
		    <li>Support OLE dragdrop. Supports to drag the text from IE or other editors into the editor of minipad2. The dictionary page supports the dragdrop-style word-query.
		    <li>Supports two styles of "Desktop-pad" mode: "Auto-Scroll" and "Auto-Popup".
		    <li>Newly designed "Preference" window. Added many new options including "Auto empty line" and "export to previous file without prompt".
		    <li>The hotkeys for "Minimize/Restore" and "Desktop-mode" is now user-definable.
		    <li>The digits of calculation results is now user-definable.
		    <li>User can enable or disable the extended functions.
		    <li>Automatically remembers the window position when closed.
		    <li>Process-mutex design. Allows to run only one minipad2.exe process in the same directory.
		    <li>Supports extensible multi-language versions via auto-detection of *.lng files. Supports language-switching during program running.
		    <li>Other minor improvements and bug-fixings.
		</ol>
			
		<h2>2.1.0 ~ 2.1.1 ( 2007.9.3. ~ 9.5.)</h2>
		<ol>
		    <li>New "Calculator Page".
		    <li>New "Memo Page"
		    <li>New "Desktop Pad" mode.
		    <li>Add windows hotkey for the minimize / restore and normal_mode / desktoppad_mode switching.
		    <li>Re-designed "Search / Replace" module, add "match whole word" option.
		    <li>Combined all the lesser-used options under "Options" menu into one "Preference" dialogbox. Add many new options like "Wordwrap", "Auto Indent", "Auto backup", "Minimize when close", "Fixed tab width", etc.
		    <li>A few bug fixing.
		</ol>

		<h2>2.0.1 ~ 2.0.4 ( 2007.8.12. ~ 8.16. )</h2>
		<ol>
		    <li>Add standard notepad functions such as undo, select all, cut, copy, paste, etc.
		    <li>Allow exchange positions for the tab pages.
		    <li>New "Lock-Readonly-Unlock" switch function.
		    <li>Add search and replace function.
		    <li>Add import and export function for text files. The import function supports to import multi-files simultaneously.
		    <li>Auto-save when minimized.
		    <li>Add the option to switch between single-line-tab and multi-line-tab styles.
		    <li>Add transparancy settings.
		    <li>Add the Help file.
		    <li>A few bug fixing.
		</ol>

		<h2>2.0.0 ( 2007.8.11. )</h2>
		<ol>
		    <li>The first version for minipad2 in it's true sense. Re-designed and re-write the whole program. Now it's designed to be a tiny, green, multi-page note program.
		</ol>
		
		<h2>1.0 ( 2007.4. )</h2>
		<ol>
		    <li>The first version of minipad. A simple note program with dual pane (note list + editor) user interface.
		</ol><br>
</body>
</html>
