<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="main.css" rel="stylesheet" type="text/css">
	<title>Featured Functions</title>
</head>

<body>
	<h1>Featured Functions</h1>
	
	<h2>1. Stay On Top</h2>
	<p>Use "Tools"--><a href='Menus\Menus_Tools.html#m_stayontop'>"Stay On Top"</a>, you can let the window of minipad2 stays above all other windows on the desktop, this is quite useful especially for  taking down notes.</p>

	<h2>2. Transparent</h2>
	<p>With "Tools"--><a href='Menus\Menus_Tools.html#m_transparent'>"Transparent"</a>, you can change the minipad2 window into a half-transparent window. The transparency can be adjusted in "Options"--><a href='Options\Appearance\Opt_Appearance.html'>"Appearance"</a>.</p>

	<a name="autohide"></a><h2>3. Auto-Hide</h2>
	<p>Side-hide is something like minimize, the difference is if under this mode, when the mouse-pointer leaves the main-window, the window will automatically moves out of the desktop side, and when the mouse-pointer go back to the side where the window hides, the window will move back again.<br>
	Use "Tools"--><a href='Menus\Menus_Tools.html#m_autohide'>"Auto hide"</a> to switch between normal mode and side-hide mode.<br>
	Under the side-hide mode, the main window will change to "Tool-window style" (without maximize & minimize buttons, do not show on the taskbar), and always stay on the Z-top of the desktop.</p>

	<p>The corresponding settings (hide-direction, hide-delay, show-delay, animation time...) can be accessed in "Options"-->"Program Settings"--><a href='Options\Program Settings\Opt_AutoHide.html'>"Auto hide"</a>.</p>

	<p>Regarding the hide-directions, there are four fixed directions and one special "Free" direction. With "Free" direction, the window will always hide to the side which is most close to the window. That means if the window hide in the right side previously and if you want it to hide to to top-side now, you needn't modify the settings in the "Options" dialog, simply drag the window to a place close to the top-side of the desktop.</p>

	<a name="autoadjust"></a><h2>4. Auto adjust appearance</h2>
	<p>"Auto adjust appearance" means when you use mouse to minimize the window-size, when the window size becomes smaller and smaller, some other elements (tree-view, tab-bar, toolbar, menu-bar...) will hide one by one. When the window become very small, after all the other elements disappeared, finally there's only the editor remains.<br>
	This feature is very useful, especially when you use minipad2 as a small "stay on top" window to write some notes, in this case if you minimize the window to very small size, it will leave most of the spaces for the editor.<br>
	You can choose whether to activate this feature or not in "Options"--><a href='Options\Appearance\Opt_Appearance.html'>"Appearance"</a>, you can also set it's "Sensitivity". With the higher sentivity, the window will more tends to hide the other elements when the window become smaller and smaller.<br>
	When the menu-bar finally also hides, if you want to access some menuitems, you can right-click on the Title-bar.</p>

	<a name="externalscroll"></a><h2>5. External scroll</h2>
	<p>Use "Options"--><a href='Options\Program Settings\Opt_ProgramSettings.html'>"Program settings"</a> to choose whether to active the "External scroll" feature or not.<br>
	With "External scroll" activated, if the current caret is inside of minipad2 editor, and if you scroll the mouse-wheel outside of minipad2 (e.g., on the top of doc, pdf, or webpages), then the page under the mouse-wheel will scroll instead of the minipad2 editor.<br>
	This feature is useful for users who use minipad2 as a "stay on top" tiny window to write some notes. You neen't always change the focus between two programs just to do the page-scroll.</p>

	<a name="snaptext"></a><h2>6. Snap Notes</h2>
	
	<p>minipad2 can be used to collect information from other sources. Use the hotkeys defined in "Options"-->"Program Settings"--><a href='Options\Program Settings\Opt_Notes.html'>"Notes"</a>, you can easily capture the selected text from the webpage or file you're reading, and save into the database of minipad2, or directly save into a text file.</p>
	<p>Another way to snap notes is by checking on "Auto record" in the tray menu. By this way all the copied texts in other softwares or browsers will be automatically appended to the current page of minipad2. When you want to stop this feature, just check off this menu item.</p>

	<a name="navigation"></a><h2>7. Navigation functions</h2>
	<p>In the "Navigation" menu there're a series of functions for easily locate specific notes, including <a href='Menus\Menus_Navigation.html#m_prior'>"Go Prior"</a>, <a href='Menus\Menus_Navigation.html#m_next'>"Go Next"</a>, <a href='Menus\Menus_Navigation.html#m_recentcreate'>"Recent Create"</a>, <a href='Menus\Menus_Navigation.html#m_recentmodify'>"Recent Modify"</a>, <a href='Menus\Menus_Navigation.html#m_recentvisit'>"Recent Visit"</a>, etc. You can also add your favorite pages into <a href='Menus\Menus_Navigation.html#m_favorite'>"Favorite"</a> group for fast launch.</p>

	<a name="texthighlight"></a><h2>8. Text highlight</h2>
	
	<p>minipad2 supports to underline or highlight the selected text blocks, while still keeping plain text format. The corresponding menu item is "Edit"--><a href='Menus\Menus_Edit.html#m_texttools'>"Text tools"</a>. The software supports four color schemes, you can define the settings in "Options"-->"Appearance"--><a href='Options\Appearance\Opt_Editor.html'>"Editor"</a>.</p>

	<a name="search"></a><h2>9. Global search</h2>
	
	<p>The software has integrated a powerful <a href='Menus\Menus_Navigation.html#m_search'>global-search</a> function, can search the title, content and other properties for different types of pages, according to the defined criteria. The search result is listed in the "Search" page. Double-click any item will switch to this page, and highlight all matches within this page.</p>

	<a name="abstract"></a><h2>10. Abstract View</h2>
	<p>For the list-type pages, besides the normal big icon, small icon, list & report view, there is also a unique "Abstract" view. Under this view you can easily see the abstract contents of all the items in this page.</p>

	<a name="autosave"></a><h2>11. Data save and backup</h2>
	<p>The program automatically saves everything (text, settings, window size and position...) when the window is minimized, when side-hide or when closed. In mose cases it's not necessary to manually save the page content ("File"-->"Save").<br>
	Besides the auto-save after minimize or side-hide, the software also saves the contents every 10 minutes with the default settings. After each ten times of save, the program will do an extra backup (copy all the data files to a pre-defined backup folder). The software automatically keeps the latest five backups.<br>
	With "Options"--><a href='Options\Program Settings\Opt_Backup.html'>"Save & Backup"</a>, you can change the settings of timed saving and backup, you can also define the backup path, and total count of backups.</p>

	<a name="externalsave"></a><h2>12. External save</h2>
	<p>For each page of minipad2, it is possible to select whether save the data in the database, or save in an separate txt file. The import and file-drag-in also supports "virtual import", i.e., only import a link, the content are still saved in the original file. With this feature minipad2 can be used as a virtual manager for numerous text files, without necessary to save all the file contents in a single database. It's more safe and more fast.
<br>
	To switch the "external save" property, use "File"--><a href='Menus\Menus_File.html#m_property'>"Property"</a>-->"Save"</p>
	
	<a name="recycler"></a><h2>13. Recycler</h2>
	
	<p>The deleted pages will automatically enter the recycler. The recycler supports limit settings (in "Option"--><a href='Options\Program Settings\Opt_ProgramSettings.html'>"Program Settings"</a>), If the page count exceeds this limit, the most "old" pages will automatically be cleared. So normally it's not necessary to manually clear the recycler, also needn't worry about the performance down due too many pages inside of recycler after long time's use.<br>
	if you need to recover a deleted page, simply drag it outside of the recycler.</p>
	<br>
</body>
</html>
