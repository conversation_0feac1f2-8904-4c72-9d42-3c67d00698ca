<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../main.css" rel="stylesheet" type="text/css">
	<title>Tools</title>
</head>

<body>
	<h1>Tools</h1>

	<p><a name="m_showtree"></a><b>Show Tree:</b> use this menuitem to switch between multi-page-style and tree-view-style.<br>
	<a name="m_stayontop"></a><b>Stay On Top:</b> Let the minipad2 window to be the top-most window above all other windows programs. ( very useful for writing notes. )<br>
	<a name="m_transparent"></a><b>Transparent:</b> Switch the main-window between transparent status and normal status. (The transparency can be set in "Options"--><a href='..\Options\Appearance\Opt_Appearance.html'>"Appearance"</a>).<br>
	<a name="m_autohide"></a><b>Auto hide:</b> Allows the window to automatically hides out of the desktop side, when the mouse pointer leaves the window. ( see <a href='..\Featured Functions.html#autohide'>"Auto Hide"</a> section in the chapter "Featured Functions" )</p>

	<p><a name="m_template"></a><b>Template manage:</b> manage the template list. (see "Extensions"--><a href='..\Extensions\Ext_Template.html'>"Template"</a>)<br>
	<a name="m_fastlink"></a><b>FastLink manage:</b> manage the fast links. (see "Extensions"--><a href='..\Extensions\Ext_Launcher.html'>"Launcher"</a>)</p>

	<p><a name="m_watchclipboard"></a><b>Watch Clipboard:</b> Starts / Stops clipboard capture.<br>
	<a name="m_clearcliplist"></a><b>Clear Clipboard:</b> Clears all the cached clipitems.<br>
	<a name="m_clipboard"></a><b>Clipboard manage:</b> manage the clipitem list. (see "Extensions"--><a href='..\ExtensionsExt_MultiClipboard.html'>"Multi-Clipboard"</a>)</p>
	
	<p><a name="m_statistics"><b>Statistics:</b> Open the "Statistics" dialog, in the "Current Page" tab you can see the count of characters, words, lines & paragraphs in the current page. In the "Database" tab you can see the count for different page types and items in the whole database.</p>

	<p><a name="m_definetoolbar"><b>Define Toolbar:</b> Open the "Define Toolbar" dialog to define the visible buttons on the toolbar. In the dialog the right side is a list of current tool buttons, left side is the list of available buttons, it is possible to drag items between these two lists.<br>
	<b>Options:</b> Open the "Options" dialog to view/change the settings of the program. (see the chapter <a href='..\Options\Options.html'>"Options"</a>)</p><br>
</body>
</html>
