<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../main.css" rel="stylesheet" type="text/css">
	<title>List</title>
</head>

<body>
	<h1>List</h1>

	<p>This menu is used for the List Pages (<a href="..\Extensions\Ext_Memo.html">MemoPage</a>, <a href="..\Extensions\Ext_Launcher.html">LinkPage</a>, <a href="..\Extensions\Ext_ContactList.html">ContactPage</a>, <a href="..\Extensions\Ext_Template.html">Template</a>, <a href="..\Extensions\Ext_Launcher.html#fastlink">FastLink</a>).</p>

	<p><a name="m_newitem"></a><b>New Item:</b> Add a new item (<PERSON><PERSON><PERSON>, <PERSON>, Contact Person, ...) in the current page.<br>
	<b>Insert Item:</b> Insert a new item before the selected item.<br>
	<b>Edit Item:</b> Edit the selected item.<br>
	<a name="m_deleteitem"></a><b>Delete Item:</b> Delete the selected item.</p>

	<p><a name="m_copycontent"></a><b>Copy Content:</b> Copy the content of the current list into clipboard. (Only copies the columns in the current view, do not copy the unactivated columns).<br>
	<a name="m_definelist"></a><b>Define List:</b> Set the columns, ViewStyle (Big Icon, Small Icon, List, Report...), Full-Row-Select-Style and GridLines for the current page. It's also possible to save the current settings as the default settings.</p><br>
</body>
</html>
