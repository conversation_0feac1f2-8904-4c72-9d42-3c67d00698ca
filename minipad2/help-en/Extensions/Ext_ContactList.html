<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=Windows-1252">
	<link href="../main.css" rel="stylesheet" type="text/css">
	<title>Contact List</title>
</head>

<body>
	<h1>Contact List</h1>

	<p>minipad2 has integrated the function of a very simple contact list. <br>
	Use "File"-->"New ExtPage"--><a href='..\Menus\Menus_File.html#m_newcontact'>"New ContactPage"</a> to create a new ContactPage.</p>

	<h2>Basic Use</h2>
	
	<p>Use "Edit"--><a href='..\Menus\Menus_Edit.html#m_newitem'>"New Item"</a> (or double-click on any empty place inside of memopage) to open the "New Contact" dialog to create a new contact item.<br>
	Double-click an existing item to open the "Edit Contact" dialog to modify this item.<br>
	To delete an existing item, right-click it and choose <a href='..\Menus\Menus_Edit.html#m_delete'>"Delete Item"</a>.<br>
	To adjust the order of items, select the item and drag it to the target position.<br>
	If there's more than one ContactPages, you can move/copy the items between different pages using dragdrop.</p>

	<h2>Edit Contact dialog</h2>
	
	<p>In the "Edit Contact" dialog there're items including Name, Company, Address, Tel, Email, etc. It's quite easy to understand, so it's not necessary to give furthur descriptions here.</p><br>
</body>
</html>
