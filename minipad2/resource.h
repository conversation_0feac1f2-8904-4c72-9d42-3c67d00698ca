#ifndef IDC_STATIC
#define IDC_STATIC (-1)
#endif

#define ProgramIcon                             999
#define About_Box                               1000
#define st_link1                                1000
#define st_userfunctions                        1000
#define st_category1                            1001
#define st_translator                           1001
#define mle_userfunctions                       1002
#define st_description                          1002
#define st_freeware                             1002
#define st_action                               1003
#define st_copyrightinfo                        1003
#define st_sensitivity                          1003
#define st_copyright                            1004
#define st_forum                                1005
#define st_extfuncsprompt                       1006
#define st_forumlink                            1006
#define st_homepagelink                         1007
#define st_homepage                             1008
#define st_email                                1009
#define st_emaillink                            1010
#define st_authorname                           1011
#define st_author                               1012
#define st_program                              1013
#define st_donate                               1015
#define st_releasedate                          1017
#define st_pleasedonate                         1018
#define st_targettext                           1024
#define Find_Box                                1025
#define chk_allowreplace                        1028
#define DefineToolbar_Box                       1029
#define rb_up                                   1030
#define rb_down                                 1031
#define chk_matchcase                           1032
#define chk_wholeword                           1033
#define chk_rollback                            1034
#define cb_reset                                1035
#define lv_available                            1036
#define lv_current                              1037
#define Option_Box                              1044
#define tv_options                              1045
#define st_placeholder                          1046
#define ob_program                              1050
#define chk_captionpagename                     1053
#define chk_showtrayicon                        1054
#define chk_autostart                           1055
#define chk_closeminimize                       1056
#define chk_minimizetotray                      1057
#define hk_callwindow                           1058
#define rb_escminimize                          1059
#define rb_escclear                             1060
#define chk_startminimize                       1061
#define chk_toolwindowstyle                     1062
#define chk_showtoolbar                         1063
#define chk_showmenubar                         1065
#define cmb_language                            1066
#define st_minimizehotkey                       1067
#define st_esckey                               1068
#define st_transparency                         1069
#define st_language                             1070
#define chk_autoadjust                          1071
#define ob_safety                               1100
#define chk_autosave                            1101
#define cmb_autosavetime                        1102
#define chk_autobackup                          1103
#define cmb_backuptime                          1104
#define sle_backuppath                          1105
#define st_backuppath                           1106
#define ob_specialmode                          1120
#define cmb_direction                           1121
#define cmb_hidedelay                           1122
#define cmb_showdelay                           1123
#define cmb_animationtime                       1124
#define cmb_edgewidth                           1125
#define st_direction                            1126
#define st_hidedelay                            1127
#define st_showdelay                            1128
#define st_animationtime                        1129
#define st_edgewidth                            1130
#define ob_appearance                           1150
#define cmb_adjustlevel                         1151
#define chk_multilinetab                        1152
#define chk_fixedtabwidth                       1153
#define cmb_tabwidth                            1154
#define chk_allowprotected                      1155
#define chk_allowreadonly                       1156
#define chk_autoname                            1157
#define chk_confirmdelete                       1158
#define ob_fontcolor                            1180
#define st_tabfontdemo                          1181
#define cb_settabfont                           1182
#define st_treefontdemo                         1183
#define cb_settreefont                          1184
#define cb_settreecolor                         1185
#define st_listfontdemo                         1186
#define cb_setlistfont                          1187
#define cb_setlistcolor                         1188
#define st_editorfontdemo                       1189
#define cb_seteditorfont                        1190
#define cb_seteditorcolor                       1191
#define st_tabpage                              1193
#define st_tree                                 1194
#define st_list                                 1195
#define st_editor                               1196
#define st_linenumberdemo                       1197
#define cb_setlinenumbercolor                   1198
#define chk_highlightselline                    1199
#define cb_findcolor                            1201
#define ob_editor                               1250
#define chk_autoindent                          1251
#define chk_confirmclear                        1252
#define cmb_tabstops                            1253
#define sle_undolimit                           1254
#define chk_autoemptyline                       1255
#define cmb_margins                             1256
#define chk_showlinenumber                      1257
#define st_tabstops                             1258
#define st_undolimit                            1259
#define st_margins                              1260
#define st_findcolor                            1261
#define ob_extfuncs                             1280
#define chk_calcpage                            1281
#define chk_memopage                            1282
#define chk_dictpage                            1283
#define chk_linkpage                            1284
#define chk_contactpage                         1285
#define chk_template                            1286
#define chk_clipboard                           1287
#define chk_adveditor                           1288
#define ob_calcpage                             1300
#define cmb_decimal                             1301
#define rb_radian                               1302
#define rb_degree                               1303
#define cmb_embedcalc                           1304
#define chk_embedcalcnoreturn                   1305
#define st_decimal                              1306
#define st_radianordegree                       1307
#define st_embedcalc                            1308
#define ob_dictpage                             1350
#define cmb_embeddict                           1351
#define chk_multidict                           1352
#define lv_dictlist                             1353
#define st_dictlist                             1354
#define st_embeddict                            1355
#define ob_linkpage                             1380
#define chk_disableiconread                     1381
#define st_fastlinkhotkey                       1382
#define ob_template                             1400
#define chk_enableitemhotkey                    1402
#define st_templatehotkey                       1403
#define ob_clipboard                            1410
#define cmb_maxclipnum                          1412
#define cmb_maxitembyte                         1413
#define chk_itemreverse                         1415
#define chk_nonextsame                          1416
#define sle_sepline                             1417
#define st_maxclipnum                           1418
#define st_maxitembyte                          1419
#define st_cliphotkey                           1420
#define st_separateline                         1421
#define Link_Box                                1599
#define st_title                                1600
#define sle_title                               1601
#define rb_filelink                             1602
#define rb_folderlink                           1603
#define rb_pagelink                             1604
#define rb_emaillink                            1605
#define sle_linktext                            1606
#define rb_batchlink                            1608
#define mle_linktext                            1609
#define hk_linkhotkey                           1610
#define chk_fastlink                            1611
#define st_link                                 1612
#define st_category                             1613
#define st_hotkey                               1614
#define Memo_Box                                1629
#define cmb_timemode                            1630
#define st_time                                 1631
#define dtp_date                                1632
#define dtp_time                                1633
#define mle_description                         1634
#define rb_reminder                             1635
#define rb_executelink                          1636
#define rb_noaction                             1637
#define chk_sound                               1638
#define sle_days                                1639
#define sle_soundfile                           1640
#define st_to                                   1641
#define dtp_date2                               1642
#define ReminderPopup_Box                       1645
#define st_remindertext                         1646
#define chk_popupagain                          1647
#define cmb_time                                1648
#define st_minuteslater                         1649
#define Template_Box                            1650
#define mle_text                                1651
#define st_template                             1652
#define Contact_Box                             1699
#define sle_name                                1700
#define cmb_sex                                 1701
#define sle_mobile                              1702
#define sle_email                               1703
#define sle_qq                                  1704
#define sle_msn                                 1705
#define sle_company                             1706
#define sle_department                          1707
#define sle_address                             1708
#define sle_zipcode                             1709
#define sle_tel                                 1710
#define sle_fax                                 1711
#define sle_others                              1712
#define mle_remark                              1713
#define st_name                                 1714
#define st_company                              1715
#define st_mobile                               1716
#define st_im1                                  1718
#define st_im2                                  1719
#define st_sex                                  1720
#define st_address                              1721
#define st_zipcode                              1722
#define st_tel                                  1723
#define st_fax                                  1724
#define st_others                               1725
#define st_remark                               1726
#define st_department                           1727
#define DefineList_Box                          1749
#define lv_columns                              1750
#define cb_loaddefault                          1751
#define cb_saveasdefault                        1752
#define chk_selectall                           1753
#define rb_Icon                                 1758
#define rb_smallicon                            1759
#define rb_list                                 1760
#define rb_report                               1761
#define chk_fullrowselect                       1762
#define chk_gridlines                           1763
#define st_liststyle                            1764
#define Tip_Box                                 1800
#define st_tip                                  1801
#define TextCount_Box                           1900
#define st_textcount                            1901
#define hk_clipboard                            2200
#define hk_template                             2400
#define hk_fastlink                             2600
#define FoolsDay_Box                            3100
#define st_prompt                               3101
#define cb_previous                             10003
#define cb_next                                 10004
#define cb_add                                  10005
#define cb_remove                               10006
#define cb_up                                   10007
#define cb_down                                 10008
#define cb_new                                  10009
#define cb_browse                               10010
#define cmb_findtext                            10011
#define cmb_replacetext                         10012
#define cmb_transparency                        10013
#define cb_setsellinecolor                      10014
#define st_sellinedemo                          10015
#define rb_nosound  10016
#define rb_beep 10017
#define rb_soundfile 10018
#define st_sound 10019
#define hk_templatehotkey 10020
#define chk_treehorzscroll 10021
#define chk_nodebuttons 10022
#define chk_nodelines 10023