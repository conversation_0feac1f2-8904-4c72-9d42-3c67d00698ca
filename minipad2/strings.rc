#include "Resource.pas"

StringTable
BEGIN
   mc_workspace, "&File"
      m_newnote, "New &NotePage"
      m_newpage, "New Ex&tPage"
         m_newcalc, "New &CalcPage"
         m_newmemo, "New &MemoPage"
         m_newdict, "New &DictPage"
         m_newlink, "New &LinkPage"
         m_newcontact, "New Con&tactPage"
      m_newgroup, "New &Group"

      m_rename, "&Rename"
      m_switchlock, "Switch &Lock"
      m_deletepage, "&Delete Page"
      m_closepage, "&Close Page"
      m_save, "&Save"

      m_property, "&Property..."
      m_view, "&View"

      m_import, "&Import..."
      m_export, "&Export..."
      m_sendmail, "Send &Mail..."
      m_exit, "E&xit"

   mc_edit, "&Edit"
      m_clear, "C&lear"
      m_undo, "&Undo"
      m_redo, "&Redo"
      m_selectall, "Select &All"

      m_cut, "Cu&t"
      m_copy, "&Copy"
      m_paste, "&Paste"
      m_delete, "&Delete\tDel"

      m_newitem, "&New Item"
      m_insertitem, "&Insert Item"
      m_edititem, "&Edit Item"
      m_deleteitem, "&Delete Item\tDel"
      m_removeitem, "&Remove Item\tDel"

      m_wordwrap, "&Word Wrap"
      m_texttools, "&Text Tools"
         m_highlight1, "Highlight 1"
         m_highlight2, "Highlight 2"
         m_highlight3, "Highlight 3"
         m_highlight4, "Highlight 4"
         m_removehighlight, "Remove Highlight"
         m_ul1, "List 1"
         m_ul2, "List 2"
         m_ol, "Ordered List"
         m_removelist, "Remove List"
         m_noemptyline, "Delete all empty lines"
         m_oneemptyline, "Leave one empty line"
         
      m_find, "&Find / Replace..."
      m_subsequentfind, "Subsequent Find"
         m_findnext, "Find &Next"
         m_findprevious, "Find &Previous"
         m_replace, "&Replace && Find Next"
         m_replace_p, "&Replace && Find Previous"
         m_replaceall, "R&eplace All"
      m_highlightmatch, "&Highlight Match"

      m_insertlink, "Insert &Link..."
      m_inserttemplate, "Insert Te&mplate"
      m_insertcliptext, "Insert ClipTe&xt"

   mc_navigation, "&Navigation"
      m_prior, "Go &Prior"
      m_next, "Go &Next"
      m_levelup, "Level &Up"

      m_recentroot, "Recent Notes"
         m_recentCreate, "Recent &Create"
         m_recentModify, "Recent &Modify"
         m_recentVisit, "Recent &Visit"
         m_managerecent, "&Manage Recent Notes"

      m_favorite, "&Favorite"
         m_addfavorite, "&Add Favorite"
         m_removefavorite, "&Remove Favorite"
         m_managefavorite, "&Manage Favorite"

      m_search, "&Search..."
      m_recyclebin, "Recycle Bin"

   mc_tools, "&Tools"
      m_showtree, "Show &Tree"
      m_stayontop, "&Stay On Top"
      m_transparent, "&Transparent"
      m_specialmode, "Auto &Hide"

      m_template, "Tem&plate Manage"
      m_fastlink, "Fast&Link Manage"

      m_watchclipboard, "&Watch Clipboard"
      m_clearclipboard, "&Clear Clipboard"
      m_clipboard, "Clipboard &Manage"

      m_statistics, "Statistics..."
      m_definetoolbar, "&Define Toolbar..."
      m_options, "&Options..."

   mc_help, "&Help"
      m_helptopic, "&Help Topic"
      m_homepage, "Home&page"
      m_forum, "&Forum"
      m_donate, "&Donate"
      m_about, "&About..."

   m_restore, "Restore"
   m_minimize, "Minimize"
   m_newnoteforeground, "New Note"
   m_autorecord, "Auto Record"
   m_openfastlink, "Fast Links"

   IDOK, "OK"
   IDCANCEL, "Cancel"
   cb_add, "Add..."
   cb_new, "New"
   cb_reset, "Reset"
   cb_up, "A"
   cb_down, "V"
   cb_apply, "Apply"

   About_Box, "About minipad2"
      st_translator, "Translated into English by Xu Liang"
      st_freeware, "This program is a freeware!"
      st_pleasedonate, "If you wish to support, please click:"
      
   Find_Box, "Find / Replace"
      chk_allowreplace, "Replace with:"
      chk_matchcase, "Match case"
      chk_wholeword, "Whole word"
      rb_up, "Up"
      rb_down, "Down"
      chk_rollback, "Roll back"
      st_targettext, "Target text:"
      sr_findprompt, "Please input target text!"
      sr_replaceallprompt, "Do you really want to use \"%0\" to replace each \"%2\" in the %1 direction?"
      sr_upward, "upward"
      sr_downward, "downward"
      sr_allreplaced, "All target text has been replaced!"
      sr_notargetfound, "No target text found!"
      sr_noreplacefound, "No replace target found!"
      chk_withinselrange, "Within selected text"
      chk_exitafterfirstmatch, "Close me after find first match"
      chk_highlightmatch, "Highlight find result"

   DefineToolbar_Box, "Define Toolbar"
      sr_availablebuttons, "Available Buttons"
      sr_currentbuttons, "Current Buttons"
      sr_separator, "Separator"
      sr_ResetToolbarPrompt, "Do you want to reset the original toolbuttons?"

   Property_Box, "Property"

   Property_General, "General"
      st_status, "Status:"
      st_createtime, "Create time:"
      st_modifytime, "Modify time:"
      st_visittime, "Visit time:"
      st_path, "Path:"

   Property_Edit, "Extended"
      chk_externalsave, "Text saved in file:"

   Property_List, "List"
      cb_loaddefault, "Load Default"
      cb_saveasdefault, "Save Default"
      chk_selectall, "Select All"
      rb_Icon, "Big Icon"
      rb_smallicon, "Small Icon"
      rb_list, "List"
      rb_report, "Report"
      rb_blog, "Abstract"
      chk_fullrowselect, "Select Full Row"
      chk_gridlines, "Show Grids"
      st_liststyle, "Style"
      sr_optionalitems, "Optional Items"
      chk_checkboxes, "Show Checkbox"
      cb_applytoall, "Apply to all"

   Statistics_Box, "Statistics"
      tp_CurrentPage, "Current Page"
      tp_Database, "Database"
   sr_CharCount, "Total Characters: "
   sr_ExCrCharCount, "Characters ExCr:"
   sr_ExBlCharCount, "Characters ExBl: "
   sr_WordCount, "Words: "
   sr_LineCount, "Lines: "
   sr_ParaCount, "Paragraphs: "
      sr_ItemCount, "Items: "

   LogIn_Box, "Login minipad2"
      st_inputpassword, "Please input password:"
      sr_wrongpassword, "Incorrect password!"

   Import_Box, "Import"
   	st_importtype, "Import from:"
      rb_fromfile, "File"
	   rb_fromfolder, "Folder"
      chk_virtualimport, "Virtual import"

   Export_Box, "Export"
      st_exporttype, "Export to:"
      rb_tofile, "To file"
      rb_tofolder, "To folder"
      rb_toclipboard, "To Clipboard"
      st_encode, "Encode:"

   Search_Box, "Search"
      st_pagetype, "Page type:"
      st_searchcrit, "Criteria:"

   Note_Box, "New Note"
      sr_newClipItem, "New ClipItem"
      sr_editClipitem, "Edit ClipItem"
      chk_fixed, "Fixed Item"
      
   InsertLink_Box, "Insert Link"
      
   Option_Box, "Options"

   ob_program, "Program Settings"
      chk_autostart, "Start with windows"
      chk_showtrayicon, "Show trayIcon"
      chk_minimizetotray, "Minimize to tray"
      chk_startminimize, "Start minimized"
      chk_closeminimize, "Close to tray"
      chk_autoname, "Auto name new page"
      chk_confirmdelete, "Confirm delete"
      chk_externalscroll, "External scroll"
     	chk_alwaysfocuseditor, "Always focus editor"
      st_mailclient, "Mail Client:"
      st_callwindowhotkey, "Minimize/Restore hotkey:"
      st_recentnotescount, "Recent notes:"
      st_recyclerlimit, "Recycler limit:"
      chk_rememberpagescroll, "Remember page scroll"

   ob_edit, "Edit"
      chk_autoemptyline, "Auto empty line"
      chk_autoindent, "Auto indent"
	   chk_oneclickopenlink, "One click open link"
      chk_confirmclear, "Confirm clear"
      st_tabstops, "Tab stops:"
      st_undolimit, "Undo limit:"
      chk_smoothscroll, "Smooth scroll"
      st_ul1, "List 1:"
      st_ul2, "List 2:"

   ob_notes, "Notes"
      st_newnote_foreground_hotkey, "New Note:"
      st_newnote_background_hotkey, "New note in background:"
      st_snaptext_hotkey, "Snap text:"
      st_newnotebg_snaptext_hotkey, "New note in background and snap text:"
      st_snaptexttofile_hotkey, "Snap text to file:"
      st_closenotehotkey, "Close note:"
      st_autorecordhotkey, "Switch auto record:"

   ob_import_export, "Import / Export"
      st_importoption, "When import:"
      rb_import_default, "Use default settings"
      rb_import_showdialog, "Always ask"
      st_exportoption, "When export:"
      rb_export_showdialog, "Always ask"
      rb_export_default, "Use default settings"
      st_separateline, "Separator line:"
	   chk_externalsaveWhenDragInFile, "Use virtual import when drag in files"

   ob_behavior, "Behavior"
      st_esckey, "Esc used for:"
      rb_escminimize, "Minimize"
      rb_escclear, "Clear text"
      st_pagedblclick, "Double-click on tab:"
      rb_switchproperty1, "Switch status"
      rb_rename1,	"Rename"
      rb_delete1,	"Delete"
      st_mbuttonclick, "Middle-click on tab:"
      rb_rename2, "Rename"
      rb_switchproperty2, "Switch status"
      rb_delete2, "Delete"
      st_groupdblclick, "Double-click on group:"
      rb_newpage, "New page"
      rb_levelup, "Level up"

   ob_login, "Log In"
      chk_needlogin, "Login with password:"
      st_password, "Password:"
      st_confirmpassword, "Confirm:"
      chk_locktrayicon, "Lock tray-icon after:"
      chk_encryptdatafile, "Encrypt data file"

   ob_backup, "Save / Backup"
      chk_autosave, "Auto save"
      chk_autobackup, "Auto backup, interval:"
      st_backuppath, "Backup path:"
      sr_selectbackupFolder, "Select folder for backup:"
      st_totalbackup, "Total backups:"
      sr_saves, "saves"
      sr_minutes, "minutes"
      sr_hours, "hours"
      sr_days, "days"

   ob_specialmode, "Auto Hide"
      st_direction, "Direction:"
      st_hidedelay, "Hide delay:"
      st_showdelay, "Show delay:"
      st_animationtime, "Animation time:"
      st_edgewidth, "Edge width:"
      sr_free, "Free"
      sr_left, "Left"
      sr_top, "Top"
      sr_right, "Right"
      sr_bottom, "Bottom"

   ob_appearance, "Appearance"
      st_language, "Language:"
      chk_toolwindowstyle, "Tool-window style"
      chk_captionpagename, "Pagename on titlebar"
      chk_showmenubar, "Show menubar"
      chk_showtoolbar, "Show toolbar"
      chk_autoadjust, "Auto adjust appearance"
      st_sensitivity, "Sensitivity:"
      st_transparency, "Transparency:"
      st_splitterwidth, "Splitter width:"

   ob_treeview, "Tree Panel"
      st_tree, "Tree Panel:"
      cb_settreefont, "Font..."
      cb_settreecolor, "Color..."
      chk_treehorzscroll, "Tree Horz-scroll"
      chk_nodebuttons, "Show node buttons"
      chk_nodelines, "Show node lines"
      chk_linesatroot, "Lines at root"
      chk_shownodeimages, "Show node icons"
      chk_treeonright, "Tree on right"

   ob_tabcontrol, "Tab Bar"
      st_tabpage, "Tab Bar:"
      cb_settabfont, "Font..."
      chk_multilinetab, "MultiLine Tabs"
      chk_tabonbottom, "Tabs on bottom"
      chk_fixedtabwidth, "Fixed tab width:"
      chk_showtabimages, "Show tab icons"
      chk_highlightcurrenttab, "Highlight current tab"

   ob_listview, "List Panel"
      st_list, "List Panel:"
      cb_setlistfont, "Font..."
      cb_setlistcolor, "Color..."
      st_blog, "Abstract View:"
      cb_setblogfont, "Font..."
      cb_setblogcolor, "Color..."
      st_blogtitle, "Abstract Title:"
      cb_setblogtitlefont, "Font..."
      cb_setblogtitlecolor, "Color..."
      st_blogseltitle, "Abstract Sel-Title:"
      cb_setblogseltitlefont, "Font..."
      cb_setblogseltitlecolor, "Color..."

   ob_editor, "Editor"
      st_editor, "Editor:"
      cb_seteditorfont, "Font..."
      cb_seteditorcolor, "Color..."
      st_margins, "Margins:"
      chk_showlinenumber, "Show line number"
      cb_setlinenumbercolor, "Color..."
      chk_highlightselline, "Highlight current line"
      cb_setsellinecolor, "Color..."
      cb_HLColor, "Color..."
      chk_useunderline, "Underline"
      st_HLText, "Highlight scheme:"

   ob_othercontrols, "Other Controls"
      st_mininote, "Mini-note window:"
      cb_setmininotefont, "Font..."
      cb_setmininotecolor, "Color..."

   ob_extfuncs, "Extensions"
      chk_calcpage, "Enable CalcPage"
      chk_memopage, "Enable MemoPage"
      chk_dictpage, "Enable DictPage"
      chk_linkpage, "Enable LinkPage"
      chk_contactpage, "Enable ContactPage"
      chk_template, "Enable Template"
      chk_clipboard, "Enable Clipboard"
      st_extfuncsprompt, "Remark: The modifications in this page will take effect after the program restart."

   ob_calcpage, "Calculator"
      st_decimal, "Decimal:"
      st_radianordegree, "Triangular calc:"
      rb_radian, "Radian"
      rb_degree, "Degree"
      st_userfunctions, "User functions:"

   ob_dictpage, "Dictionary"
      st_dictlist, "Dictionary List:"
      chk_multidict, "Show all search results"
      sr_codetransfer, "Transferring encode of dictionary files, please wait..."
      st_textcapture, "Tip-query hotkey:"
      cb_setcolor, "Color..."

   ob_linkpage, "Launcher"
      st_fastlinkhotkey, "FastLink Hotkey:"
      chk_enableitemhotkey, "Enable subitem hotkey"
      chk_disableiconread, "Disable Icon-read (faster)"
      chk_autominimizeafteropenlink, "Auto minimize minipad2 after open link"

   ob_template, "Template"
      st_templatehotkey, "Popup hotkey:"
      chk_popupmenunofocus, "Template / Clipboard popup menu no focus"
      st_capturehotkey, "Capture hotkey:"
      st_abbrev, "Abbrev.:"

   ob_clipboard, "Clipboard"
      st_maxclipnum, "Maximum items:"
      st_maxitembyte, "Item size limit:"
      st_cliphotkey, "Popup hotkey:"
      st_menuwidth, "Menu width:"
      chk_newpastetotop, "New pasted item go to top"
      chk_filtersameitems, "Filter duplicate items";
      chk_onlyfilterneighboring, "Only filter neighboring duplicate items";

   sr_NewLink, "New Link"
   sr_EditLink, "Edit Link"
      rb_filelink, "Program / File"
      rb_folderlink, "Folder"
      rb_pagelink, "Url"
      rb_emaillink, "Email"
      rb_batchlink, "Batch"
      st_link, "Link:"
      st_category, "Type:"
      st_link1, "Link:"
      st_category1, "Type:"
      st_hotkey, "Hotkey:"
      st_title, "Title:"

   sr_NewReminder, "New Reminder"
   sr_EditReminder, "Edit Reminder"
      rb_reminder, "Reminder"
      rb_executelink, "Execute Link"
      rb_noaction, "No Action"
      st_time, "Time:"
      st_description, "Description:"
      st_action, "Action:"
      sr_today, "This day"
      sr_daily, "Daily"
      sr_weekly, "Weekly"
      sr_monthly, "Monthly"
      sr_yearly, "Yearly"
      sr_timespan, "Period"
      sr_notime, "No time"
      st_sound, "Sound:"
      rb_nosound, "None"
      rb_beep, "Beep"
      rb_soundfile, "Sound file"
      
   sr_EditContact, "Edit Contact"
   sr_NewContact, "New Contact"
      st_name, "Name:"
      st_company, "Company:"
      st_mobile, "Mobile:"
      st_im1, "ICQ:"
      st_im2, "MSN:"
      st_sex, "Sex:"
      st_address, "Address:"
      st_zipcode, "Zipcode:"
      st_tel, "Tel:"
      st_fax, "Fax:"
      st_others, "Others:"
      st_remark, "Remark:"
      st_department, "Department:"
      st_email, "Email:"
      
   sr_newTemplate, "New Template"
   sr_editTemplate, "Edit Template"

   ReminderPopup_Box, "minipad2 reminder"
      chk_popupagain, "Remind again:"
      st_minuteslater, "."
      chk_noremind, "No remind"
      chk_deleteafterclose, "Delete me"



   sr_newpage, "New Page"
   sr_inputpagename, "Please input page name:"
   sr_prompt, "Prompt"
   sr_deleteprompt, "Do you want to delete the current page?"
   sr_rename, "Rename page"
   sr_clearprompt, "Do you want to clear the content of the current page?"

   sr_boy, "Male"
   sr_girl, "Female"

   sr_selectfile, "Select File"
   sr_filefilter, "All Files (*.*)|*.*|Executable File(*.exe)|*.exe|DOC File(*.doc)|*.doc|XLS File(*.xls)|*.xls|PPT File(*.ppt)|*.ppt|PDF File(*.pdf)|*.pdf|Text File(*.txt)|*.txt"
   sr_selectsoundfile, "Select Sound File"
   sr_soundfilefilter, "Sound File (*.wav)|*.wav"
   sr_selectfolder, "Select Folder"

   sr_MemoAction, "Remind"
   sr_MemoAction + 1, "Task"
   sr_MemoAction + 2, "None"

   sr_Time, "Time"
   sr_Action, "Action"
   sr_UseSound, "Enable Sound"
   sr_SoundFile, "Sound File"

   sr_Description, "Description"
   sr_LinkType, "Category"
   sr_LinkTypes, "File"
   sr_LinkTypes + 1, "Folder"
   sr_LinkTypes + 2, "Url"
   sr_LinkTypes + 3, "Email"
   sr_LinkTypes + 4, "Node"
   sr_LinkTypes + 5, "Others"
   sr_LinkTypes + 6, "Batch Link"
   sr_Link, "Link"
   sr_HotKey, "Hotkey"
   sr_Abbrev, "Abbrev."

   sr_Name, "Name"
   sr_Sex, "Sex"
   sr_Mobile, "Mobile"
   sr_Email, "Email"
   sr_IM1, "IM"
   sr_IM2, "MSN"
   sr_Company, "Company"
   sr_Department, "Department"
   sr_Address, "Address"
   sr_Zipcode, "ZipCode"
   sr_Tel, "Tel"
   sr_Fax, "Fax"
   sr_Others, "Others"

   sr_clearclipboardprompt, "Are you sure to clear all the clip text?"
   sr_SelectImportFile, "Select file for import"
   sr_ImportFilter, "Text Files (*.txt)|*.txt|minipad2 Export File (*.mep)|*.mep|All Files (*.*)|*.*"
   sr_NameExportFile, "Name export file"
   sr_ExportFilter1, "Text File (*.txt)|*.txt|All Files (*.*)|*.*"
   sr_ExportFilter2, "Text File (*.txt)|*.txt|minipad2 Export File (*.mep)|*.mep|All Files (*.*)|*.*"
   sr_ExportFilter3, "minipad2 Export File (*.mep)|*.mep|Text File (*.txt)|*.txt|All Files (*.*)|*.*"
   sr_ListCopied, "The content of the list has been sent to the clipboard."
   sr_RemoveFastLinkItemsPrompt, "Are you sure to remove the selected items from the fastlink list?"
   sr_DeleteItemsPrompt, "Are you sure to delete the selected items?"
   sr_RemoveItemsPrompt, "Are you sure to remove the selected items?"
   sr_DeletePagesPrompt, "Are you sure to delete the selected pages?"
   sr_Root, "Root"
   sr_DeleteGroupFailed, "The selected group contains locked pages and cannot be deleted."
   sr_DeleteGroupPrompt, "Are you sure to delete the whole group with all it's subitems?"
   sr_PasswordNotConfirmed, "Password not match! Please check!"
   sr_BrowseMailClient, "Select mail client executable"
   sr_ExeFilter, "Executable File (*.exe)|*.exe"
   sr_TemplateCaptured, "The selected text has been added to the template list of minipad2."
   sr_TemplateExists, "The item with same content (Nr. %0) has already existed in the template list!"
   sr_TableOfContents, "Table of Contents"
   sr_info, "Information"
   sr_applytoallprompt, "Are you sure to apply the current settings to all pages with the same type?"

   sr_SelectIcon, "Select Icon File"
   sr_IconFilter, "Icon file (*.ico)|*.ico|All files (*.*)|*.*"
   sr_UnsupportedOperation, "Unsupported operation!"
   sr_NewNoteCreated, "New note created with title:"
   sr_SnapTextSuccess, "The following text is snapped:"
   sr_newnotebgandsnaptextsuccess, "Created new note and snapped the following text:"
   sr_DataSaved, "Data is saved."
   sr_SaveSnapText, "Save snapped text to file"
   sr_SnapTextSavedToFile, "The following text has been saved to file"
   sr_ExportedToFolder, "The current node (and subnodes if any) has been exported to folder:"
   sr_PageExportedToFile, "The content of current page has been exported to file:"
   sr_PageExportedToClipboard, "The content of current page has been exported to clipboard"
   sr_GroupExportedToFile, "The content of current node (and subnodes if any) has been exported to file:"
   sr_MepVersionNotMatch, "The version of the mep file does not match the current version!"
   sr_invalidnodelink, "Invalid node link!"
   sr_importingPrompt, "File importing, holding ESC to abort..."
   sr_userabortimport, "User abort import"
   sr_deletingprompt, "Deleting, press and holding ESC to abort..."
   sr_exportingprompt, "Page exporting, holding ESC to abort..."
   sr_userabortexport, "User abort export"
   
   sr_DefGroupName, "Group"
   sr_DefNoteName, "Note"
   sr_DefTagName, "Tag"
   sr_DefCalcName, "Calc"
   sr_DefMemoName, "Memo"
   sr_DefDictName, "Dict"
   sr_DefLinkName, "Link"
   sr_DefContactName, "Contact"

   sr_GroupPage, "Group"
   sr_GroupPage + 1, "NotePage"
   sr_GroupPage + 2, "CalcPage"
   sr_GroupPage + 3, "MemoPage"
   sr_GroupPage + 4, "DictPage"
   sr_GroupPage + 5, "LinkPage"
   sr_GroupPage + 6, "ContactPage"
   sr_GroupPage + 15, "Reminder"
   sr_GroupPage + 16, "Link"
   sr_GroupPage + 17, "Contact"
   sr_GroupPage + 18, "Template"
   sr_GroupPage + 26, "ClipItem"

   sr_TemplatePage, "Template"
   sr_FastLink, "FastLink"
   sr_Clipboard, "Clipboard"

   sr_Normal, "Normal"
   sr_Locked, "Locked"
   sr_Protected, "Protected"
   sr_ReadOnly, "ReadOnly"

   sr_title, "Title"
   sr_createtime, "Create Time"
   sr_modifytime, "Modify Time"
   sr_visittime, "Visit Time"
   sr_externalsave, "External"
   sr_exportfile, "File"
   sr_remark, "Remark"
   sr_Text, "Text"
   sr_abstract, "Abstract"
   sr_searchresult, "Search result"
   sr_nodepath, "Node path"

   sr_RecentRoot, "Recent Notes"
      sr_RecentCreate, "Recent Created"
      sr_RecentModify, "Recent Modified"
      sr_RecentVisit, "Recent Visited"
   sr_favoritepage, "Favorite"
   sr_searchpage, "Search"
   sr_tagroot, "Tags"
   sr_grouproot, "Root"
   sr_recyclebin, "RecycleBin"

   sr_Equal, "="
   sr_NotEqual, "<>"
   sr_Include, "Include"
   sr_NotInclude, "Exclude"

END
