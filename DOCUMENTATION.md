# minipad2 - Comprehensive Project Documentation

## Project Overview

**minipad2** is a lightweight, multi-functional text editor and note-taking application designed for Windows. It integrates multiple useful functions including notepad, calculator, reminder, dictionary, launcher, address list, text templates, and clipboard enhancement features, all while maintaining very low system resource usage and memory footprint.

The application automatically saves all content when minimized or closed, and restores the last state when reopened. It can freely switch between two interface styles: multi-page tab style and tree-view navigation style. When not in use, it can be minimized to the system tray or hidden at the desktop edge.

## Technical Stack

### Programming Language & Framework
- **Language**: Object Pascal (Delphi)
- **IDE**: Borland Delphi 7.0
- **Target Platform**: Windows (32-bit)
- **Architecture**: Single executable application

### Core Libraries & Components
- **XCL (eXtended Component Library)**: Custom UI framework providing Windows controls and utilities
- **ExtLibrary**: Extended functionality library for specialized components
- **Windows API**: Direct Windows system integration

### Build System
- **Compiler**: Delphi Command Line Compiler (dcc32.exe)
- **Resource Compiler**: Borland Resource Compiler (brcc32.exe)
- **Project Structure**: Borland Project Group (.bpg) with multiple packages (.dpk)

## Feature List

### Core Features
1. **Multi-Style Interface**
   - Multi-page tab style for quick note switching
   - Tree-view navigation style for hierarchical organization
   - Seamless switching between interface modes

2. **Text Editing & Management**
   - Rich text editing with syntax highlighting
   - Text tools: underline, highlight, ordered lists
   - Find and replace with advanced options (case sensitivity, reverse direction)
   - Text statistics and word count
   - Multiple encoding support (ANSI, UTF-8, Unicode)

3. **Auto-Save & Backup**
   - Automatic saving every 10 minutes (configurable)
   - Automatic backup after every 10 saves
   - Maintains latest 5 backups automatically
   - External file saving option (virtual file management)

4. **Window Management**
   - Stay on top functionality
   - Transparency control (0-10 levels)
   - Auto-hide to desktop edges with animation
   - Auto-adjust appearance based on window size
   - External scroll support

### Extension Features (Plugin-like Architecture)

#### 1. Calculator Extension
- Mathematical expression evaluation
- Support for basic arithmetic and advanced functions
- Operators: +, -, *, /, \\ (div), ! (mod), int(x), round(x,y)
- Integrated within the main interface

#### 2. Memo/Reminder Extension
- Task management with checkboxes
- Reminder notifications with various intervals
- Support for yearly, monthly, daily reminders
- To-do list functionality

#### 3. Dictionary Extension
- Built-in dictionary lookup
- Support for custom dictionary files
- Word translation and definition display

#### 4. Launcher Extension
- Quick application launcher
- File and folder shortcuts
- Email link support
- Batch operation support

#### 5. Contact List Extension
- Address book management
- Contact information storage
- External icon support for contacts

#### 6. Template Extension
- Text template management
- Variable substitution with %c(##) designators
- Abbreviation support for quick launching
- ASCII character insertion support

#### 7. Multi-Clipboard Extension
- Enhanced clipboard functionality
- Multiple clipboard item storage
- Configurable item limits and filtering
- Fixed item support
- Menu width customization

### Advanced Features

#### 1. Global Search
- Search across all pages and content
- Multiple search criteria support
- Results displayed in dedicated search page
- Match highlighting within pages
- Node path display in results

#### 2. Navigation System
- Recent Create/Modify/Visit pages
- Favorite pages management
- Go Prior/Next functionality
- Breadcrumb navigation

#### 3. Data Management
- Import/Export functionality
- Virtual import (link-only import)
- Export to various formats and encodings
- Recycler with automatic cleanup
- Statistics dialog for database analysis

#### 4. Text Enhancement
- Text highlighting with 4 color schemes
- Background highlighting and underline
- Link insertion dialog
- Snap text functionality with hotkeys
- Auto-record feature

#### 5. Security Features
- Login protection with time-based locking
- Data encryption support
- Tray icon lock functionality

## Architecture & File Structure

### High-Level Architecture
```
minipad2 Application
├── Core Engine (XCL Framework)
├── Main Application (minipad2.exe)
├── Plugin System (Extensions)
├── Language Support
├── Help System
└── Utility Applications
```

### Directory Structure

```
minipad2/
├── minipad2/                 # Main application source
│   ├── minipad2.dpr         # Main program file
│   ├── minipad2.dof         # Project options
│   ├── minipad2.bpg         # Build project group
│   ├── U*.pas               # Core units (MainForm, GlobalObj, etc.)
│   ├── RESOURCE.PAS         # Resource definitions
│   ├── *.RC, *.res          # Resource files
│   ├── data/                # Application data
│   ├── lang/                # Language files
│   ├── help-en/             # English help documentation
│   ├── help-cn/             # Chinese help documentation
│   ├── images/              # Application icons and images
│   ├── backup/              # Backup storage
│   ├── workspace/           # Workspace management
│   ├── pages/               # Page management
│   ├── plugins/             # Plugin extensions
│   ├── options/             # Options management
│   └── dataupdater/         # Data update utilities
├── XCL/                      # eXtended Component Library
│   ├── XCL.dpk              # Package definition
│   ├── Uxl*.pas             # UI and utility components
│   └── XCLRes.res           # XCL resources
├── ExtLibrary/               # Extended functionality library
│   ├── ExtLibrary.dpk       # Package definition
│   └── U*.pas               # Extended components
├── DailyWord/                # Daily word application
│   ├── DailyWord.dpr        # Vocabulary learning tool
│   └── *.pas                # DailyWord components
└── include/                  # C/C++ header files for development
```

### Key Modules and Relationships

#### Core Modules
- **UMainForm**: Main application window and event handling
- **UGlobalObj**: Global objects and managers (SaveManager, SizeManager, etc.)
- **UTypeDef**: Type definitions and enumerations
- **UMenuManager**: Menu system management
- **ULangManager**: Multi-language support
- **UDialogs**: Dialog management

#### Plugin Architecture
- **UPluginManager**: Plugin system coordinator
- **Individual Plugin Units**: Calculator, Memo, Dictionary, etc.
- **Plugin Packages**: Compiled as separate .bpl files

#### Data Management
- **UIOHandler**: File I/O operations
- **UBackupHandler**: Backup management
- **UPageStore**: Page data storage
- **Database**: minipad2.dat (main data file)

## Technical Details

### Build System and Compilation

#### Project Group Structure
The application uses a Borland Project Group (.bpg) that builds multiple components:

1. **XCL.bpl** - Core UI library
2. **ExtLibrary.bpl** - Extended functionality
3. **dataupdater.bpl** - Data update utilities
4. **plugins.bpl** - Plugin extensions
5. **workspace.bpl** - Workspace management
6. **pages.bpl** - Page management
7. **options.bpl** - Options management
8. **minipad2.exe** - Main application
9. **DailyWord.exe** - Vocabulary learning tool
10. **LangExtractor.exe** - Language extraction utility
11. **LngUpdater.exe** - Language update utility

#### Compilation Process
```bash
# Build all components
make -f minipad2.bpg

# Individual component builds
dcc32 XCL.dpk
dcc32 ExtLibrary.dpk
dcc32 minipad2.dpr
```

### Package Dependencies

#### Runtime Requirements
- **rtl**: Delphi Runtime Library
- **vcl**: Visual Component Library (for some components)

#### Custom Dependencies
- **XCL**: All components depend on the XCL framework
- **ExtLibrary**: Depends on XCL
- **Plugins**: Depend on both XCL and ExtLibrary

### Key Technical Implementation Details

#### Memory Management
- Automatic save every 10 minutes to prevent data loss
- Efficient memory usage with lazy loading
- Automatic cleanup of temporary resources

#### File I/O System
- Rewritten file I/O classes for improved performance
- Line-by-line reading optimization
- Support for multiple text encodings
- Virtual file management for external files

#### Plugin System
- Dynamic loading of plugin packages (.bpl files)
- Interface-based plugin architecture
- Configurable plugin activation/deactivation
- Plugin-specific options and settings

#### Multi-Language Support
- Language files (.lng format)
- Runtime language switching
- Support for 10+ languages including:
  - English, Chinese (Simplified & Traditional)
  - German, French, Spanish, Italian
  - Russian, Japanese, Dutch, Finnish, Bulgarian

#### Security Implementation
- Optional data encryption
- Time-based access control
- Secure password handling
- Tray icon locking mechanism

## Development Information

### Version Information
- **Current Version**: 3.2 beta3
- **Build**: 100526
- **Last Updated**: May 26th, 2010
- **Development Timeline**: 2009-2010

### Version History Highlights
- **v3.2**: Complete core rewrite, new database structure, plugin architecture
- **v3.1**: Enhanced UI, improved stability
- **v3.0**: Major feature additions, multi-language support
- **v2.x**: Initial public releases

### Author Details
- **Developer**: xiaodiega
- **Organization**: 星之原软件工作室 (Star Origin Software Studio)
- **Contact**: <EMAIL>
- **Website**: http://www.nebulasoft.cn
- **Forums**: 
  - Chinese: http://www.nebulasoft.cn/bbs
  - English: http://www.nebulasoft.cn/forum

### License and Distribution Terms

#### Software License
- **Type**: Freeware
- **Usage Rights**: Free to use, copy, distribute, and transmit
- **Restrictions**:
  - Must maintain package and program integrity
  - No modification without author permission
  - No plugin bundling without permission
  - No commercial bundling or sales

#### Distribution Requirements
- Complete installation package must be preserved
- No modifications to core program files
- No commercial redistribution
- Attribution to original author required

#### Support Policy
- Free technical support via email and forum
- No warranty or liability for software use
- Community-driven support and development

#### Registry Usage
- Minimal registry usage (green software principle)
- Only adds "Run" key when set to start with Windows
- No other registry modifications

### Additional Applications

#### DailyWord
A vocabulary learning companion application included with minipad2:
- **Purpose**: Daily vocabulary learning and practice
- **Features**: Word display, pronunciation, definitions
- **Modes**: Embedded display, full-screen mode, test mode
- **Customization**: User-defined word lists, display settings
- **Integration**: Can be used standalone or with minipad2

This comprehensive documentation covers all major aspects of the minipad2 project based on the actual codebase analysis, configuration files, and help documentation found in the repository.
