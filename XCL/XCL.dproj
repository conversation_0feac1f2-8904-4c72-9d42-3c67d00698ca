﻿	<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
		<PropertyGroup>
			<ProjectGuid>{62308F5A-9369-4FCA-A662-15363A23679A}</ProjectGuid>
			<MainSource>XCL.dpk</MainSource>
			<Config Condition="'$(Config)'==''">Debug</Config>
			<DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
			<ProjectVersion>12.3</ProjectVersion>
			<Base>True</Base>
			<Platform>Win32</Platform>
			<AppType>Package</AppType>
			<FrameworkType>None</FrameworkType>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_1)'!=''">
			<Cfg_1>true</Cfg_1>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_2)'!=''">
			<Cfg_2>true</Cfg_2>
			<CfgParent>Base</CfgParent>
			<Base>true</Base>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Base)'!=''">
			<DCC_UnitAlias>WinTypes=Windows;WinProcs=Windows;DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE;$(DCC_UnitAlias)</DCC_UnitAlias>
			<DCC_DependencyCheckOutputName>C:\Users\<USER>\Documents\RAD Studio\7.0\Bpl\XCL.bpl</DCC_DependencyCheckOutputName>
			<GenPackage>true</GenPackage>
			<GenDll>true</GenDll>
			<DCC_OutputNeverBuildDcps>true</DCC_OutputNeverBuildDcps>
			<DCC_ImageBase>00400000</DCC_ImageBase>
			<DCC_Platform>x86</DCC_Platform>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_1)'!=''">
			<DCC_LocalDebugSymbols>false</DCC_LocalDebugSymbols>
			<DCC_Define>RELEASE;$(DCC_Define)</DCC_Define>
			<DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
			<DCC_DebugInformation>false</DCC_DebugInformation>
		</PropertyGroup>
		<PropertyGroup Condition="'$(Cfg_2)'!=''">
			<DCC_Define>DEBUG;$(DCC_Define)</DCC_Define>
		</PropertyGroup>
		<ItemGroup>
			<DelphiCompile Include="XCL.dpk">
				<MainSource>MainSource</MainSource>
			</DelphiCompile>
			<DCCReference Include="rtl.dcp"/>
			<DCCReference Include="UxlClasses.pas"/>
			<DCCReference Include="UxlCommDlgs.pas"/>
			<DCCReference Include="UxlDragControl.pas"/>
			<DCCReference Include="UxlDialog.pas"/>
			<DCCReference Include="UxlEdit.pas"/>
			<DCCReference Include="UxlFile.pas"/>
			<DCCReference Include="UxlFunctions.pas"/>
			<DCCReference Include="UxlImageList.pas"/>
			<DCCReference Include="UxlIniFile.pas"/>
			<DCCReference Include="UxlListSuper.pas"/>
			<DCCReference Include="UxlListView.pas"/>
			<DCCReference Include="UxlMath.pas"/>
			<DCCReference Include="UxlMenu.pas"/>
			<DCCReference Include="UxlStack.pas"/>
			<DCCReference Include="UxlStdCtrls.pas"/>
			<DCCReference Include="UxlStrUtils.pas"/>
			<DCCReference Include="UxlTabControl.pas"/>
			<DCCReference Include="UxlWinClasses.pas"/>
			<DCCReference Include="UxlTreeView.pas"/>
			<DCCReference Include="UxlWinControl.pas"/>
			<DCCReference Include="UxlWindow.pas"/>
			<DCCReference Include="UxlMiscCtrls.pas"/>
			<DCCReference Include="UxlList.pas"/>
			<DCCReference Include="UxlRichEdit.pas"/>
			<DCCReference Include="UxlCanvas.pas"/>
			<DCCReference Include="UxlConst.pas"/>
			<DCCReference Include="UxlExtDlgs.pas"/>
			<DCCReference Include="UxlExtClasses.pas"/>
			<DCCReference Include="UxlWinDef.pas"/>
			<DCCReference Include="UxlPanel.pas"/>
			<DCCReference Include="UxlGrid.pas"/>
			<DCCReference Include="UxlGridEx.pas"/>
			<DCCReference Include="UxlDateTimeUtils.pas"/>
			<DCCReference Include="UxlComboBox.pas"/>
			<DCCReference Include="UxlRichEditClasses.pas"/>
			<DCCReference Include="UxlAppbar.pas"/>
			<BuildConfiguration Include="Debug">
				<Key>Cfg_2</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
			<BuildConfiguration Include="Base">
				<Key>Base</Key>
			</BuildConfiguration>
			<BuildConfiguration Include="Release">
				<Key>Cfg_1</Key>
				<CfgParent>Base</CfgParent>
			</BuildConfiguration>
		</ItemGroup>
		<Import Condition="Exists('$(BDS)\Bin\CodeGear.Delphi.Targets')" Project="$(BDS)\Bin\CodeGear.Delphi.Targets"/>
		<Import Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')" Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj"/>
		<ProjectExtensions>
			<Borland.Personality>Delphi.Personality.12</Borland.Personality>
			<Borland.ProjectType>Package</Borland.ProjectType>
			<BorlandProject>
				<Delphi.Personality>
					<Source>
						<Source Name="MainSource">XCL.dpk</Source>
					</Source>
					<Parameters/>
					<VersionInfo>
						<VersionInfo Name="IncludeVerInfo">True</VersionInfo>
						<VersionInfo Name="AutoIncBuild">False</VersionInfo>
						<VersionInfo Name="MajorVer">1</VersionInfo>
						<VersionInfo Name="MinorVer">0</VersionInfo>
						<VersionInfo Name="Release">0</VersionInfo>
						<VersionInfo Name="Build">0</VersionInfo>
						<VersionInfo Name="Debug">False</VersionInfo>
						<VersionInfo Name="PreRelease">False</VersionInfo>
						<VersionInfo Name="Special">False</VersionInfo>
						<VersionInfo Name="Private">False</VersionInfo>
						<VersionInfo Name="DLL">False</VersionInfo>
						<VersionInfo Name="Locale">2052</VersionInfo>
						<VersionInfo Name="CodePage">936</VersionInfo>
					</VersionInfo>
					<VersionInfoKeys>
						<VersionInfoKeys Name="CompanyName"/>
						<VersionInfoKeys Name="FileDescription"/>
						<VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="InternalName"/>
						<VersionInfoKeys Name="LegalCopyright"/>
						<VersionInfoKeys Name="LegalTrademarks"/>
						<VersionInfoKeys Name="OriginalFilename"/>
						<VersionInfoKeys Name="ProductName"/>
						<VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
						<VersionInfoKeys Name="Comments"/>
					</VersionInfoKeys>
				</Delphi.Personality>
				<Platforms>
					<Platform value="Win32">True</Platform>
				</Platforms>
			</BorlandProject>
			<ProjectFileVersion>12</ProjectFileVersion>
		</ProjectExtensions>
	</Project>
