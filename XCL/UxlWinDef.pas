unit UxlWinDef;

interface

uses Windows, Messages, CommCtrl, Shell<PERSON><PERSON>;

const
	IDC_Static = -1;

	WM_TRAYICONMESSAGE = WM_APP + 100;
   WM_TRAYICONMOUSEMOVE = WM_APP + 101;
   WM_TRAYICONAFTERPOPUPMENU = WM_APP + 102;

   WM_DIALOGOPENED = WM_APP + 300;
	WM_DIALOGCLOSED = WM_APP + 301;
   WM_ESCAPE = WM_APP + 402;
   WM_RESTORESCROLLPOS = WM_APP + 403;
   WM_LOCATEANCHOR = WM_APP + 404;
   WM_LOCATEPAGE = WM_APP + 405;
   GW_ENABLEDPOPUP = 6;

//   WM_TREELABELEDITED = WM_APP + 405;
	WM_FOCUSEDITOR = WM_APP + 406;
   WM_EDITTREELABEL = WM_APP + 407;
   WM_CALLWINDOWDEMAND = WM_APP + 408;
   APPBAR_CALLBACK = WM_APP + 409;

   LVS_EX_BORDERSELECT = 32768;
   LVM_SORTITEMS = LVM_FIRST + 48;

   EM_SETZOOM = WM_USER+225;
	EM_SETSCROLLPOS = WM_USER + 222;
	EM_GETSCROLLPOS = WM_USER + 221;
	EM_FINDTEXTW = WM_USER + 123;

   TTM_GETBUBBLESIZE = WM_USER + 30;
   TTS_NOANIMATE = $10;
   TTS_NOFADE = $20;
   TTM_ADJUSTRECT = WM_USER+31;
   TTM_SETTITLEW = WM_USER+33;

   TVIF_ITEMINFO = TVIF_TEXT or TVIF_PARAM or TVIF_IMAGE or TVIF_SELECTEDIMAGE or TVIF_STATE or TVIF_CHILDREN;
	TVS_NOHSCROLL = 32768;

	LVIF_ITEMINFO = LVIF_IMAGE or LVIF_PARAM or LVIF_STATE;
   LVIF_OVERLAYMASK = $0F00;

   VK_VOLUME_MUTE = $AD;

   IID_IPersistFile: TGUID = (
    D1:$0000010B;D2:$0000;D3:$0000;D4:($C0,$00,$00,$00,$00,$00,$00,$46));
   
type
   TNMLVDISPINFOW = record
       hdr: TNMHDR;
       item: TLVITEMW;
   end;
   PNMLVDISPINFOW = ^TNMLVDISPINFOW;

   TNMTVDISPINFOW = record
       hdr: TNMHDR;
       item: TTVITEMW;
   end;
   PNMTVDISPINFOW = ^TNMTVDISPINFOW;

implementation

end.
