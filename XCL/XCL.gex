[General]
Name=XCL
Project=0
[Classes]
Count=122
Class0="TxlCanvas","","UxlCanvas","0","E:\XCL\UxlCanvas.pas"
Class1="TxlCollection","","UxlClasses","0","E:\XCL\UxlClasses.pas"
Class2="TxlFont","","UxlClasses","0","E:\XCL\UxlClasses.pas"
Class3="TxlBrush","","UxlClasses","0","E:\XCL\UxlClasses.pas"
Class4="TxlInterfacedObject","TObject, IInterface","UxlClasses","0","E:\XCL\UxlClasses.pas"
Class5="TxlThread","","UxlClasses","0","E:\XCL\UxlClasses.pas"
Class6="TListItems","TxlCollection","UxlComboBox","0","E:\XCL\UxlComboBox.pas"
Class7="TxlListBox","TxlControl","UxlComboBox","0","E:\XCL\UxlComboBox.pas"
Class8="TComboItems","TxlCollection","UxlComboBox","0","E:\XCL\UxlComboBox.pas"
Class9="TxlComboBox","TxlControl","UxlComboBox","0","E:\XCL\UxlComboBox.pas"
Class10="TxlCommDlgSuper","","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class11="TxlOpenSaveDialog","TxlCommDlgSuper","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class12="TxlOpenDialog","TxlOpenSaveDialog","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class13="TxlSaveDialog","TxlOpenSaveDialog","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class14="TxlPathDialog","TxlCommDlgSuper","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class15="TxlFontDialog","TxlCommDlgSuper","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class16="TxlColorDialog","TxlCommDlgSuper","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class17="TxlPrintDialog","TxlCommDlgSuper","UxlCommDlgs","0","E:\XCL\UxlCommDlgs.pas"
Class18="TxlContainter","TxlInterfacedObject","UxlContainer","0","E:\XCL\UxlContainer.pas"
Class19="TxlDialogSuper","TxlWinContainer","UxlDialog","10","E:\XCL\UxlDialog.pas"
Class20="TxlDialog","TxlDialogSuper","UxlDialog","0","E:\XCL\UxlDialog.pas"
Class21="TxlDialogML","TxlDialogSuper","UxlDialog","0","E:\XCL\UxlDialog.pas"
Class22="TxlDragControl","TxlControl, IDragSource, IDropTarget","UxlDragControl","0","E:\XCL\UxlDragControl.pas"
Class23="TxlControlWithImages","TxlDragControl","UxlDragControl","0","E:\XCL\UxlDragControl.pas"
Class24="TDragHandler","","UxlDragControl","0","E:\XCL\UxlDragControl.pas"
Class25="TxlEditControl","TxlControl","UxlEdit","0","E:\XCL\UxlEdit.pas"
Class26="TxlEdit","TxlEditControl","UxlEdit","0","E:\XCL\UxlEdit.pas"
Class27="TxlMaskEdit","TxlEdit","UxlEdit","0","E:\XCL\UxlEdit.pas"
Class28="TxlMemo","TxlEditControl","UxlEdit","0","E:\XCL\UxlEdit.pas"
Class29="TxlSaveCenter","TxlInterfacedObject, ISaver","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class30="TxlOption","","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class31="TxlMemory","TxlInterfacedObject, ISaver","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class32="TxlLanguage","TxlInterfacedObject","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class33="TxlEventCenter","TxlInterfacedObject","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class34="TxlListCenter","","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class35="TxlCommandCenter","TxlInterfacedObject","UxlExtClasses","0","E:\XCL\UxlExtClasses.pas"
Class36="TInputBox","TxlDialog","UxlExtDlgs","0","E:\XCL\UxlExtDlgs.pas"
Class37="TxlFile","","UxlFile","0","E:\XCL\UxlFile.pas"
Class38="TxlTextFile","TxlFile","UxlFile","0","E:\XCL\UxlFile.pas"
Class39="TxlMemoryTextFile","TxlTextFile","UxlFile","0","E:\XCL\UxlFile.pas"
Class40="TxlGrid","TxlControl","UxlGrid","0","E:\XCL\UxlGrid.pas"
Class41="TxlGridEx","TxlGrid","UxlGridEx","0","E:\XCL\UxlGridEx.pas"
Class42="TxlImageList","","UxlImageList","0","E:\XCL\UxlImageList.pas"
Class43="TIniCache","","UxlIniFile","0","E:\XCL\UxlIniFile.pas"
Class44="TxlIniFile","","UxlIniFile","0","E:\XCL\UxlIniFile.pas"
Class45="TxlRegistry","","UxlIniFile","0","E:\XCL\UxlIniFile.pas"
Class46="TxlStrList","TxlListSuper","UxlList","0","E:\XCL\UxlList.pas"
Class47="TxlIntList","TxlListSuper","UxlList","0","E:\XCL\UxlList.pas"
Class48="TxlObjList","TxlListSuper","UxlList","0","E:\XCL\UxlList.pas"
Class49="TxlInterfaceList","TxlListSuper","UxlList","0","E:\XCL\UxlList.pas"
Class50="TxlListSuper","TxlCollection","UxlListSuper","0","E:\XCL\UxlListSuper.pas"
Class51="TListViewColumns","TxlCollection","UxlListView","0","E:\XCL\UxlListView.pas"
Class52="TListViewItems","TxlCollection","UxlListView","0","E:\XCL\UxlListView.pas"
Class53="TxlListView","TxlDragControl","UxlListView","0","E:\XCL\UxlListView.pas"
Class54="TxlMenu","TxlInterfacedObject, ICommandSender","UxlMenu","0","E:\XCL\UxlMenu.pas"
Class55="TxlToolBar","TxlControl, ICommandSender","UxlMenu","0","E:\XCL\UxlMenu.pas"
Class56="TxlAccelTable","","UxlMenu","0","E:\XCL\UxlMenu.pas"
Class57="TxlUpDown","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class58="TxlProgressBar","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class59="TxlSlideSuper","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class60="TxlVertSlide","TxlSlideSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class61="TxlHorzSlide","TxlSlideSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class62="TxlSplitterSuper","TxlSlideSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class63="TxlVertSplitter","TxlSplitterSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class64="TxlHorzSplitter","TxlSplitterSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class65="TxlHotKey","TxlControl","UxlMiscCtrls","125","E:\XCL\UxlMiscCtrls.pas"
Class66="TxlDateTimePicker","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class67="TxlDatePicker","TxlDateTimePicker","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class68="TxlTimePicker","TxlDateTimePicker","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class69="TxlStatusBar","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class70="TxlToolTipSuper","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class71="TxlToolTip","TxlToolTipSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class72="TxlTrackingTip","TxlToolTipSuper","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class73="TxlCalendar","TxlControl","UxlMiscCtrls","0","E:\XCL\UxlMiscCtrls.pas"
Class74="TxlCustomControl","TxlControl","UxlPanel","0","E:\XCL\UxlPanel.pas"
Class75="TxlPanel","TxlCustomControl","UxlPanel","0","E:\XCL\UxlPanel.pas"
Class76="TxlFrame","TxlPanel","UxlPanel","0","E:\XCL\UxlPanel.pas"
Class77="TxlRichEdit","TxlEditControl","UxlRichEdit","0","E:\XCL\UxlRichEdit.pas"
Class78="TxlRichEditDecorator","TxlInterfacedObject, IRichEditDecorator","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class79="TxlRichEditLineNumber","TxlRichEditDecorator","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class80="TxlRichEditHLSuper","TxlRichEditDecorator","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class81="TxlRichEditHighlightSelLine","TxlRichEditHLSuper","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class82="TxlRichEditHighlightText","TxlRichEditHLSuper","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class83="TxlRichEditHighLightTextBlock","TxlRichEditHLSuper","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class84="TxlRichEditFindHandler","","UxlRichEditClasses","0","E:\XCL\UxlRichEditClasses.pas"
Class85="TxlIntStackQueueSuper","","UxlStack","7","E:\XCL\UxlStack.pas"
Class86="TxlIntStack","TxlIntStackQueueSuper","UxlStack","0","E:\XCL\UxlStack.pas"
Class87="TxlIntQueue","TxlIntStackQueueSuper","UxlStack","0","E:\XCL\UxlStack.pas"
Class88="TxlStrStackQueueSuper","","UxlStack","0","E:\XCL\UxlStack.pas"
Class89="TxlStrStack","TxlStrStackQueueSuper","UxlStack","0","E:\XCL\UxlStack.pas"
Class90="TxlStrQueue","TxlStrStackQueueSuper","UxlStack","0","E:\XCL\UxlStack.pas"
Class91="TxlObjListLite","TxlCollection","UxlStack","0","E:\XCL\UxlStack.pas"
Class92="TxlIntListLite","TxlCollection","UxlStack","0","E:\XCL\UxlStack.pas"
Class93="TxlBoolList","TxlCollection","UxlStack","0","E:\XCL\UxlStack.pas"
Class94="TxlStaticControl","TxlControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class95="TxlStaticText","TxlStaticControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class96="TxlStaticLink","TxlStaticText","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class97="TxlStaticIcon","TxlStaticControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class98="TxlButtonControl","TxlControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class99="TxlButton","TxlButtonControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class100="TxlCheckBox","TxlButtonControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class101="TxlRadioButton","TxlButtonControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class102="TxlGroupBox","TxlButtonControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class103="TxlScrollBar","TxlControl","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class104="TxlHorzScrollBar","TxlScrollBar","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class105="TxlVertScrollBar","TxlScrollBar","UxlStdCtrls","0","E:\XCL\UxlStdCtrls.pas"
Class106="TTabItems","TxlCollection","UxlTabControl","0","E:\XCL\UxlTabControl.pas"
Class107="TxlTabControlSuper","TxlControlWithImages","UxlTabControl","0","E:\XCL\UxlTabControl.pas"
Class108="TxlTabControl","TxlTabControlSuper","UxlTabControl","0","E:\XCL\UxlTabControl.pas"
Class109="TxlPageControl","TxlTabControlSuper","UxlTabControl","0","E:\XCL\UxlTabControl.pas"
Class110="TTreeViewItems","","UxlTreeView","0","E:\XCL\UxlTreeView.pas"
Class111="TxlTreeView","TxlControlWithImages","UxlTreeView","0","E:\XCL\UxlTreeView.pas"
Class112="TxlTrayIcon","","UxlWinClasses","0","E:\XCL\UxlWinClasses.pas"
Class113="TxlTimer","","UxlWinClasses","0","E:\XCL\UxlWinClasses.pas"
Class114="TxlTimerCenter","","UxlWinClasses","0","E:\XCL\UxlWinClasses.pas"
Class115="TxlHotKeyCenter","","UxlWinClasses","0","E:\XCL\UxlWinClasses.pas"
Class116="TxlClipboard","","UxlWinClasses","0","E:\XCL\UxlWinClasses.pas"
Class117="TxlWinControl","TxlInterfacedObject","UxlWinControl","14","E:\XCL\UxlWinControl.pas"
Class118="TxlWinContainer","TxlWinControl","UxlWinControl","120","E:\XCL\UxlWinControl.pas"
Class119="TxlControl","TxlWinContainer","UxlWinControl","150","E:\XCL\UxlWinControl.pas"
Class120="TMHCenter","","UxlWinControl","0","E:\XCL\UxlWinControl.pas"
Class121="TxlWindow","TxlWinContainer","UxlWindow","0","E:\XCL\UxlWindow.pas"
